[{"serial_no": "N8OZO22B0005N01", "mac_address": "00:1A:2B:3C:4D:01", "change_no": 1, "model": "E500", "version": "Pass-1", "unit_status": "Development", "manifest": {"mainboard": {}, "nic": {}, "rffe": [{}]}, "supported_bands": [], "created_at": "2024-07-15T11:14:19.865572Z"}, {"serial_no": "N8OZO2290001N01", "mac_address": "00:1A:2B:3C:4D:02", "change_no": 1, "model": "E500", "version": "Pass-1", "unit_status": "Development", "manifest": {"mainboard": {}, "nic": {}, "rffe": [{}]}, "supported_bands": [], "created_at": "2024-07-15T11:14:47.986543Z"}, {"serial_no": "N8OZO22E0008N01", "mac_address": "00:1A:2B:3C:4D:03", "change_no": 1, "model": "E500", "version": "Pass-1", "unit_status": "Development", "manifest": {"mainboard": {}, "nic": {}, "rffe": [{}]}, "supported_bands": [], "created_at": "2024-07-15T11:14:55.802277Z"}, {"serial_no": "N8OZO22F0009N01", "mac_address": "00:1A:2B:3C:4D:04", "change_no": 1, "model": "E500", "version": "Pass-1", "unit_status": "Development", "manifest": {"mainboard": {}, "nic": {}, "rffe": [{}]}, "supported_bands": [], "created_at": "2024-07-15T11:15:02.333488Z"}, {"serial_no": "N8OZO2280002N01", "mac_address": "00:1A:2B:3C:4D:05", "change_no": 1, "model": "E500", "version": "Pass-1", "unit_status": "Development", "manifest": {"mainboard": {}, "nic": {}, "rffe": [{}]}, "supported_bands": [], "created_at": "2024-07-15T11:15:08.854404Z"}]