/// <reference types="cypress" />

const createCBSDOUrl = '/oran-du-cu-manager/cbrs-management/cbsd/create';

describe('Create CBSD Form', () => {
  beforeEach(() => {
    const paths = {
      ruManifest: 'CBSD/ruManifest.json',
      clusters: 'CBSD/clusters.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/inv/manifest/cluster', {
      fixture: paths.clusters,
    }).as('getRegions');

    cy.intercept('GET', '**/inv/manifest/ru', {
      fixture: paths.ruManifest,
    }).as('getSites');

    cy.intercept('POST', '**/orc/cbsd**', { statusCode: 200, body: { success: true } }).as('createCbsd');

    // Ensure system health and login before visiting the page
    cy.system_health();
    cy.useLogin();
    cy.visit(createCBSDOUrl);
    cy.log('Visited create cbsd form');
  });

  it('should have the Create button disabled until all required fields are filled', () => {
    // The Create button should be disabled initially
    cy.get('[data-testid="create-cbsd"]').should('be.disabled');

    cy.get('[data-testid="display-name"]').type('Test Device');
    cy.get('select#serialNumber').should('be.visible').select('N8OZO22B0005N01');

    // Verify MAC address is auto-populated when serial number is selected
    cy.get('[data-testid="mac-address"]').should('have.value', '00:1A:2B:3C:4D:01');

    cy.get('[data-testid="fcc-id"]').type('FCC123456');
    cy.get('select#clusterId').should('be.visible').select('denseair-gke-k-bmctl-edge02-1-15-2');
    cy.get('select#category').should('be.visible').select('A');

    // The Create button should be enabled because all required fields are filled
    cy.get('[data-testid="create-cbsd"]').should('be.enabled');
  });

  it('should disable the Create button and show an error if string values are entered in numeric fields of the Installation form', () => {
    cy.get('[data-testid="display-name"]').type('Test Device');
    cy.get('select#serialNumber').should('be.visible').select('N8OZO22B0005N01');

    // Verify MAC address is auto-populated
    cy.get('[data-testid="mac-address"]').should('have.value', '00:1A:2B:3C:4D:01');

    cy.get('[data-testid="fcc-id"]').type('FCC123456');
    cy.get('select#clusterId').should('be.visible').select('denseair-gke-k-bmctl-edge02-1-15-2');
    cy.get('select#category').should('be.visible').select('A');

    cy.contains('Next').click();

    // Navigate to Installation form
    cy.contains('Next').click();

    cy.get('[data-testid="latitude"]').type('invalid');
    cy.get('[data-testid="longitude"]').type('invalid');

    // The Create button should be disabled due to invalid inputs
    cy.get('[data-testid="create-cbsd"]').should('be.disabled');
  });

  it('Successfully create CBSD form', () => {
    // The Create button should be disabled initially
    cy.get('[data-testid="create-cbsd"]').should('be.disabled');

    cy.get('[data-testid="display-name"]').type('Test Device');
    cy.get('select#serialNumber').should('be.visible').select('N8OZO22B0005N01');

    // Verify MAC address is auto-populated and readonly
    cy.get('[data-testid="mac-address"]').should('have.value', '00:1A:2B:3C:4D:01');
    cy.get('[data-testid="mac-address"]').should('have.attr', 'readonly');

    cy.get('[data-testid="fcc-id"]').type('FCC123456');
    cy.get('select#clusterId').should('be.visible').select('denseair-gke-k-bmctl-edge02-1-15-2');
    cy.get('select#category').should('be.visible').select('A');

    cy.get('[data-testid="create-cbsd"]').should('be.enabled');

    // Submit the form
    cy.get('[data-testid="create-cbsd"]').click();
    cy.contains('The CBSD has been successfully created.');
  });

  it('should auto-populate MAC address when serial number changes', () => {
    cy.get('[data-testid="display-name"]').type('Test Device');

    // Select first serial number and verify MAC address
    cy.get('select#serialNumber').should('be.visible').select('N8OZO22B0005N01');
    cy.get('[data-testid="mac-address"]').should('have.value', '00:1A:2B:3C:4D:01');

    // Change to second serial number and verify MAC address updates
    cy.get('select#serialNumber').select('N8OZO2290001N01');
    cy.get('[data-testid="mac-address"]').should('have.value', '00:1A:2B:3C:4D:02');

    // Verify MAC address field is readonly
    cy.get('[data-testid="mac-address"]').should('have.attr', 'readonly');
  });

  it('should clear MAC address when serial number is cleared', () => {
    cy.get('[data-testid="display-name"]').type('Test Device');

    // Select a serial number and verify MAC address is populated
    cy.get('select#serialNumber').should('be.visible').select('N8OZO22B0005N01');
    cy.get('[data-testid="mac-address"]').should('have.value', '00:1A:2B:3C:4D:01');

    // Clear the serial number selection
    cy.get('select#serialNumber').select('');

    // Verify MAC address is cleared
    cy.get('[data-testid="mac-address"]').should('have.value', '');
  });
});
