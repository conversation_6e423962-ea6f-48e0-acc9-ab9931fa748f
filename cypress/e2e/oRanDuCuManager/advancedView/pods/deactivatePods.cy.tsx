/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const oRanDuCuManagerUrl = '/oran-du-cu-manager';

//NOTE: this code: cy.intercept('GET', '/nms/dev1/rad/config/parameter_sets/8').as('inputRenderTrigger');
// will need to be changed on a per environment basis. /nms/dev1/ will need to be changed to /nms/dev2/ or /nms/test1/ etc.
// it will need to be injected in when we push the code to the environment.

describe('Test cell O-RAN DU CU Pod Deactivate page', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(oRanDuCuManagerUrl);
  });

  it('Deactivate pods for DU App Config - Identity and Connectivity', () => {
    cy.navigateToAdvancedView();

    cy.get('.chakra-container').find('[data-testid="pods"]').find('button').click();
    cy.get('.chakra-container').find('h2').should('contain', 'Pods');
    cy.get('[data-testid="ORAN-DU-CU-pods-Manager"]').should('exist');

    cy.get('[data-testid="ORAN-DU-CU-pods-Manager"]')
      .find('select.chakra-select')
      .first()
      .should('exist')
      .select('11: denseair-gke-bmctl-edge-5-1-16-6');

    cy.get('.chakra-accordion').find('h2').should('contain', 'table for selected cluster');

    cy.get('[data-testid="ORAN-DU-CU-pods-Manager"]').find('select.chakra-select').last().should('exist').select('Du');

    cy.get('input[type="text"]')
      .clear()
      .type('cypress-test-value-create-cr-du-site-name', { delay: 100 })
      .should('have.value', 'cypress-test-value-create-cr-du-site-name');

    cy.get('button').contains('.chakra-button', 'Deactivate Du custom resources').should('exist').click();

    cy.get('button').contains('.chakra-button', 'Activate Du custom resources to create pod').should('exist');
  });

  it('Deactivate pods for CuUp App Config - Identity and Connectivity', () => {
    cy.navigateToAdvancedView();

    cy.get('.chakra-container').find('[data-testid="pods"]').find('button').click();
    cy.get('.chakra-container').find('h2').should('contain', 'Pods');
    cy.get('[data-testid="ORAN-DU-CU-pods-Manager"]').should('exist');

    cy.get('[data-testid="ORAN-DU-CU-pods-Manager"]')
      .find('select.chakra-select')
      .first()
      .should('exist')
      .select('11: denseair-gke-bmctl-edge-5-1-16-6');

    cy.get('.chakra-accordion').find('h2').should('contain', 'table for selected cluster');

    cy.get('[data-testid="ORAN-DU-CU-pods-Manager"]')
      .find('select.chakra-select')
      .last()
      .should('exist')
      .select('CuUp');

    cy.get('input[type="text"]')
      .clear()
      .type('cypress-create-cr-cuup-site-name', { delay: 100 })
      .should('have.value', 'cypress-create-cr-cuup-site-name');
    cy.get('button').contains('.chakra-button', 'Deactivate CuUp custom resources').should('exist').click();

    cy.get('button').contains('.chakra-button', 'Activate CuUp custom resources to create pod').should('exist');
  });

  it('Deactivate pods for CuCp App Config - Identity and Connectivity', () => {
    cy.navigateToAdvancedView();

    cy.get('.chakra-container').find('[data-testid="pods"]').find('button').click();
    cy.get('.chakra-container').find('h2').should('contain', 'Pods');
    cy.get('[data-testid="ORAN-DU-CU-pods-Manager"]').should('exist');

    cy.get('[data-testid="ORAN-DU-CU-pods-Manager"]')
      .find('select.chakra-select')
      .first()
      .should('exist')
      .select('11: denseair-gke-bmctl-edge-5-1-16-6');

    cy.get('.chakra-accordion').find('h2').should('contain', 'table for selected cluster');

    cy.get('[data-testid="ORAN-DU-CU-pods-Manager"]')
      .find('select.chakra-select')
      .last()
      .should('exist')
      .select('CuUp');

    cy.get('input[type="text"]')
      .clear()
      .type('cypress-create-cr-cucp-site-name', { delay: 100 })
      .should('have.value', 'cypress-create-cr-cucp-site-name');

    cy.get('button').contains('.chakra-button', 'Deactivate CuCp custom resources').should('exist').click();

    cy.get('button').contains('.chakra-button', 'Activate CuCp custom resources to create pod').should('exist');
  });
});
