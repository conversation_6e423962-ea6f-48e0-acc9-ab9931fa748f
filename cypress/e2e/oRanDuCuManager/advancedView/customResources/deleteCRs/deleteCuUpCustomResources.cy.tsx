/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const oRanDuCuManagerUrl = '/oran-du-cu-manager';

//NOTE: this code: cy.intercept('GET', '/nms/dev1/rad/config/parameter_sets/8').as('inputRenderTrigger');
// will need to be changed on a per environment basis. /nms/dev1/ will need to be changed to /nms/dev2/ or /nms/test1/ etc.
// it will need to be injected in when we push the code to the environment.

describe('Test cell O-RAN DU CU Custom Resource create page', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(oRanDuCuManagerUrl);
  });

  it('Delete CUUP Deployment', () => {
    cy.navigateToAdvancedView();

    cy.get('.chakra-container').find('[data-testid="custom-resources"]').find('button').click();
    cy.get('.chakra-container').find('h2').should('contain', 'Custom resources');
    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]').should('exist');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
      .find('select.chakra-select')
      .first()
      .should('exist')
      .select('11: denseair-gke-bmctl-edge-5-1-16-6');

    cy.get('.chakra-accordion').find('h2').should('contain', 'table for selected cluster');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
      .find('select.chakra-select')
      .last()
      .should('exist')
      .select('CuUp');

    cy.get('input[type="text"]')
      .clear()
      .type('cypress-create-cr-cuup-site-name', { delay: 100 })
      .should('have.value', 'cypress-create-cr-cuup-site-name');

    cy.get('.chakra-alert').last().find('p').should('contain', 'CuUp requires the following custom resources:');

    cy.get('.chakra-heading').should('contain', 'Complete CRs');
    cy.get('.chakra-container').contains('button', 'Create Pod').should('exist');

    cy.get('[data-testid="ORAN-DUCU-Manager-advanced-view-CustomResource-Delete-CuUpDeployment"]').click();
  });

  it('Delete CUUP App Config', () => {
    cy.navigateToAdvancedView();

    cy.get('.chakra-container').find('[data-testid="custom-resources"]').find('button').click();
    cy.get('.chakra-container').find('h2').should('contain', 'Custom resources');
    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]').should('exist');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
      .find('select.chakra-select')
      .first()
      .should('exist')
      .select('11: denseair-gke-bmctl-edge-5-1-16-6');

    cy.get('.chakra-accordion').find('h2').should('contain', 'table for selected cluster');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
      .find('select.chakra-select')
      .last()
      .should('exist')
      .select('CuUp');

    cy.get('input[type="text"]')
      .clear()
      .type('cypress-create-cr-cuup-site-name', { delay: 100 })
      .should('have.value', 'cypress-create-cr-cuup-site-name');

    cy.get('.chakra-alert').last().find('p').should('contain', 'CuUp requires the following custom resources:');

    cy.get('.chakra-heading').should('contain', 'Complete CRs');

    cy.get('[data-testid="ORAN-DUCU-Manager-advanced-view-CustomResource-Delete-CuUpDeployment"]').click();
  });

  it('All Crs for CUUP deleted', () => {
    cy.navigateToAdvancedView();

    cy.get('.chakra-container').find('[data-testid="custom-resources"]').find('button').click();
    cy.get('.chakra-container').find('h2').should('contain', 'Custom resources');
    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]').should('exist');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
      .find('select.chakra-select')
      .first()
      .should('exist')
      .select('11: denseair-gke-bmctl-edge-5-1-16-6');

    cy.get('.chakra-accordion').find('h2').should('contain', 'table for selected cluster');

    cy.get('[data-testid="ORAN-DU-CU-CustomResource-Manager"]')
      .find('select.chakra-select')
      .last()
      .should('exist')
      .select('CuUp');

    cy.get('input[type="text"]')
      .clear()
      .type('cypress-create-cr-cuup-site-name', { delay: 100 })
      .should('have.value', 'cypress-create-cr-cuup-site-name');

    cy.get('.chakra-alert').last().find('p').should('contain', 'CuUp requires the following custom resources:');

    cy.get('.chakra-heading').should('contain', 'Missing CRs');
  });
});
