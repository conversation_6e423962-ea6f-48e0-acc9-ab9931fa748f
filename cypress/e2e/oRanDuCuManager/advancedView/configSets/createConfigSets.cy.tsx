/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const oRanDuCuManagerUrl = '/oran-du-cu-manager';

//NOTE: this code: cy.intercept('GET', '/nms/dev1/rad/config/parameter_sets/8').as('inputRenderTrigger');
// will need to be changed on a per environment basis. /nms/dev1/ will need to be changed to /nms/dev2/ or /nms/test1/ etc.
// it will need to be injected in when we push the code to the environment.

describe('Test cell O-RAN DU CU Config sets create page', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(oRanDuCuManagerUrl);
  });

  it('Create config set for CU_CP app config', () => {
    cy.navigateToAdvancedView();
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-container').find('h2').should('contain', 'Config set manager');
    cy.get('[data-testid="ORAN-DU-CU-ConfigSet-Manager"]').should('exist');
    cy.get('[data-testid="ORAN-DU-CU-ConfigSet-Manager"]')
      .contains('button.chakra-button', 'Create a new config set')
      .should('exist')
      .click();
    cy.get('[data-testid="ORAN-DU-CU-CreateConfigSet-Manager"]')
      .find('select.chakra-select')
      .should('exist')
      .select('CU_CP App Config - Identity and Connectivity');

    cy.intercept('GET', '/nms/dev1/rad/config/parameter_sets/8').as('paramSets');
    //cy.wait('@paramSets');

    cy.contains('label', 'NgC gatewayAddress').parent().find('input').type('*******');
    cy.contains('label', 'NgC localIpAddress').parent().find('input').type('*******');
    cy.contains('label', 'NgC remoteAddres').parent().find('input').type('*******');
    cy.contains('label', 'MCC').parent().find('input').type('111');
    cy.contains('label', 'MNC').parent().find('input').type('22');
    cy.get('#configSetName').type('Cypress_Test_Value_CU_CP_App_Config-Identity-and-Connectivity');
    cy.get('[data-testid="ORAN-DU-CU-CreateConfigSet-Manager"]')
      .find('button.chakra-button')
      .should('contain', 'Create config set')
      .click();
    cy.get('.chakra-alert').should('contain', 'Config set has been created successfully.');
  });

  it('Create config set for CU_CP cell config', () => {
    cy.navigateToAdvancedView();
    cy.intercept('GET', '/nms/dev1/rad/config/parameter_sets/7').as('paramSets');
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-container').should('contain', 'Config sets');
    cy.get('.chakra-container').find('h2').should('contain', 'Config set manager');
    cy.get('[data-testid="ORAN-DU-CU-ConfigSet-Manager"]').should('exist');
    cy.get('[data-testid="ORAN-DU-CU-ConfigSet-Manager"]')
      .contains('button.chakra-button', 'Create a new config set')
      .should('exist')
      .click();
    cy.get('[data-testid="ORAN-DU-CU-CreateConfigSet-Manager"]')
      .find('select.chakra-select')
      .should('exist')
      .select('CU_CP Cell Config - Identity');

    //cy.wait('@paramSets');

    cy.contains('label', 'Local Cell ID').parent().find('input').type('123456');
    cy.contains('label', 'Cell Identity (nCI)').parent().find('input').type('1a2b3c4d');
    cy.contains('label', 'MCC').parent().find('input').type('111');
    cy.contains('label', 'MNC').parent().find('input').type('22');
    cy.get('#configSetName').type('Cypress_Test_Value_CU_CP_Cell_Config-Identity');
    cy.get('[data-testid="ORAN-DU-CU-CreateConfigSet-Manager"]')
      .find('button.chakra-button')
      .should('contain', 'Create config set')
      .click();
    cy.get('.chakra-alert').should('contain', 'Config set has been created successfully.');
  });

  it('Create config set for CU_UP app config', () => {
    cy.navigateToAdvancedView();
    cy.intercept('GET', '/nms/dev1/rad/config/parameter_sets/9').as('paramSets');
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-container').should('contain', 'Config sets');
    cy.get('.chakra-container').find('h2').should('contain', 'Config set manager');
    cy.get('[data-testid="ORAN-DU-CU-ConfigSet-Manager"]').should('exist');
    cy.get('[data-testid="ORAN-DU-CU-ConfigSet-Manager"]')
      .contains('button.chakra-button', 'Create a new config set')
      .should('exist')
      .click();
    cy.get('[data-testid="ORAN-DU-CU-CreateConfigSet-Manager"]')
      .find('select.chakra-select')
      .should('exist')
      .select('CU_UP App Config - Identity and Connectivity');

    //cy.wait('@paramSets');

    cy.contains('label', 'E1 gatewayAddress').parent().find('input').type('*******');
    cy.contains('label', 'E1 localIpAddress').parent().find('input').type('*******');
    cy.contains('label', 'E1 remoteAddress').parent().find('input').type('*******');
    cy.contains('label', 'F1-U gatewayAddress').parent().find('input').type('*******');
    cy.contains('label', 'F1-U localIpAddress').parent().find('input').type('*******');
    cy.contains('label', 'F1-U remoteAddress').parent().find('input').type('*******');
    cy.contains('label', 'NgU gatewayAddress').parent().find('input').type('*******');
    cy.contains('label', 'NgU localIpAddress').parent().find('input').type('*******');
    cy.contains('label', 'NgU remoteAddress').parent().find('input').type('*******');
    cy.contains('label', 'MCC').parent().find('input').type('111');
    cy.contains('label', 'MNC').parent().find('input').type('22');
    cy.contains('label', 'NrCGI - nCI - NR Cell Id').parent().find('input').type('a0a1a1a1a');
    cy.contains('label', 'id').parent().find('input').type('123456789');
    cy.contains('label', 'gNodeB ID').parent().find('input').type('123456789');
    cy.contains('label', 'gNodeB Index').parent().find('input').type('123456789');
    cy.get('#configSetName').type('Cypress_Test_Value_CU_UP_App_Config-Identity-and-Connectivity');
    cy.get('[data-testid="ORAN-DU-CU-CreateConfigSet-Manager"]')
      .find('button.chakra-button')
      .should('contain', 'Create config set')
      .click();
    cy.get('.chakra-alert').should('contain', 'Config set has been created successfully.');
  });

  it('Create config set for DU Cell Config - Cell Identity', () => {
    cy.navigateToAdvancedView();
    cy.intercept('GET', '/nms/dev1/rad/config/parameter_sets/1').as('paramSets');
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-container').should('contain', 'Config sets');
    cy.get('.chakra-container').find('h2').should('contain', 'Config set manager');
    cy.get('[data-testid="ORAN-DU-CU-ConfigSet-Manager"]').should('exist');
    cy.get('[data-testid="ORAN-DU-CU-ConfigSet-Manager"]')
      .contains('button.chakra-button', 'Create a new config set')
      .should('exist')
      .click();
    cy.get('[data-testid="ORAN-DU-CU-CreateConfigSet-Manager"]')
      .find('select.chakra-select')
      .should('exist')
      .select('DU');
    cy.get('select.chakra-select').eq(1).should('exist').select('DU Cell Config - Cell Identity');

    //cy.wait('@paramSets');

    cy.get('select[name="Administrative State"]').select('Shutdown');
    cy.contains('label', 'MCC').parent().find('input').type('111');
    cy.contains('label', 'MNC').parent().find('input').type('22');
    cy.contains('label', 'RAN Area Code').parent().find('input').type('1a2b3c4d');
    cy.contains('label', 'Local Cell ID').parent().find('input').type('a1b2c3d4e');
    cy.contains('label', 'Physical Cell ID').parent().find('input').type('1a2b3c4d');
    cy.contains('label', 'Tracking Area Code (TAC)').parent().find('input').type('1234');
    cy.get('#configSetName').type('Cypress_Test_Value_DU_Cell_Config-Cell_Identity');
    cy.get('[data-testid="ORAN-DU-CU-CreateConfigSet-Manager"]')
      .find('button.chakra-button')
      .should('contain', 'Create config set')
      .click();
    cy.get('.chakra-alert').should('contain', 'Config set has been created successfully.');
  });

  it('Create config set for DU Cell Config - Frequency and SSB', () => {
    cy.navigateToAdvancedView();
    cy.intercept('GET', '/nms/dev1/rad/config/parameter_sets/2').as('paramSets');
    cy.intercept('GET', '/nms/dev1/rad/config/cell_config_models').as('configModels');
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-container').should('contain', 'Config sets');
    cy.get('.chakra-container').find('h2').should('contain', 'Config set manager');
    cy.get('[data-testid="ORAN-DU-CU-ConfigSet-Manager"]').should('exist');
    cy.get('[data-testid="ORAN-DU-CU-ConfigSet-Manager"]')
      .contains('button.chakra-button', 'Create a new config set')
      .should('exist')
      .click();
    cy.get('[data-testid="ORAN-DU-CU-CreateConfigSet-Manager"]')
      .find('select.chakra-select')
      .should('exist')
      .select('DU');
    cy.get('select.chakra-select').eq(1).should('exist').select('DU Cell Config - Frequency and SSB');

    //cy.wait(['@configModels', '@paramSets']);

    cy.get('select[name="Band"]').select('48');
    cy.get('select[name="DL_Bandwidth"]').select('40MHZ');
    cy.get('#configSetName').type('Cypress_Test_Value_DU_Cell_Config-Frequency-and-SSB-Band-40');
    cy.get('[data-testid="ORAN-DU-CU-CreateConfigSet-Manager"]')
      .find('button.chakra-button')
      .should('contain', 'Create config set')
      .click();
    cy.get('.chakra-alert').should('contain', 'Config set has been created successfully.');
  });

  it('Create config set for DU Cell Config - SIB1 / Power', () => {
    cy.navigateToAdvancedView();
    cy.intercept('GET', '/nms/dev1/rad/config/parameter_sets/3').as('paramSets');
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-container').should('contain', 'Config sets');
    cy.get('.chakra-container').find('h2').should('contain', 'Config set manager');
    cy.get('[data-testid="ORAN-DU-CU-ConfigSet-Manager"]').should('exist');
    cy.get('[data-testid="ORAN-DU-CU-ConfigSet-Manager"]')
      .contains('button.chakra-button', 'Create a new config set')
      .should('exist')
      .click();
    cy.get('[data-testid="ORAN-DU-CU-CreateConfigSet-Manager"]')
      .find('select.chakra-select')
      .should('exist')
      .select('DU');
    cy.get('select.chakra-select').eq(1).should('exist').select('DU Cell Config - SIB1 / Power');

    //cy.wait('@paramSets');

    cy.get('input[name="Q Min Quality (dB)"]').type('-43');
    cy.get('input[name="Q Rx Min Level (dBm)"]').type('-70');
    cy.get('input[name="P-Max (dBm)').type('-30');
    cy.get('#configSetName').type('Cypress_Test_Value_DU_Cell_Config-SIB1/Power');
    cy.get('[data-testid="ORAN-DU-CU-CreateConfigSet-Manager"]')
      .find('button.chakra-button')
      .should('contain', 'Create config set')
      .click();
    cy.get('.chakra-alert').should('contain', 'Config set has been created successfully.');
  });

  it('Create config set for DU Cell Config - Timers', () => {
    cy.navigateToAdvancedView();
    cy.intercept('GET', '/nms/dev1/rad/config/parameter_sets/4').as('paramSets');
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-container').should('contain', 'Config sets');
    cy.get('.chakra-container').find('h2').should('contain', 'Config set manager');
    cy.get('[data-testid="ORAN-DU-CU-ConfigSet-Manager"]').should('exist');
    cy.get('[data-testid="ORAN-DU-CU-ConfigSet-Manager"]')
      .contains('button.chakra-button', 'Create a new config set')
      .should('exist')
      .click();
    cy.get('[data-testid="ORAN-DU-CU-CreateConfigSet-Manager"]')
      .find('select.chakra-select')
      .should('exist')
      .select('DU');
    cy.get('select.chakra-select').eq(1).should('exist').select('DU Cell Config - Timers');

    //cy.wait('@paramSets');

    cy.get('select[name="N310"]').select('N20');
    cy.get('select[name="N311"]').select('N10');
    cy.get('select[name="T300"]').select('MS2000');
    cy.get('select[name="T301"]').select('MS2000');
    cy.get('select[name="T310"]').select('MS2000');
    cy.get('select[name="T311"]').select('MS20000');
    cy.get('select[name="T319"]').select('MS2000');
    cy.get('#configSetName').type('Cypress_Test_Value_DU_Cell_Config-Timers');
    cy.get('[data-testid="ORAN-DU-CU-CreateConfigSet-Manager"]')
      .find('button.chakra-button')
      .should('contain', 'Create config set')
      .click();
    cy.get('.chakra-alert').should('contain', 'Config set has been created successfully.');
  });

  it('Create config set for DU Cell Config - MAC', () => {
    cy.navigateToAdvancedView();
    cy.intercept('GET', '/nms/dev1/rad/config/parameter_sets/5').as('paramSets');
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-container').should('contain', 'Config sets');
    cy.get('.chakra-container').find('h2').should('contain', 'Config set manager');
    cy.get('[data-testid="ORAN-DU-CU-ConfigSet-Manager"]').should('exist');
    cy.get('[data-testid="ORAN-DU-CU-ConfigSet-Manager"]')
      .contains('button.chakra-button', 'Create a new config set')
      .should('exist')
      .click();
    cy.get('[data-testid="ORAN-DU-CU-CreateConfigSet-Manager"]')
      .find('select.chakra-select')
      .should('exist')
      .select('DU');
    cy.get('select.chakra-select').eq(1).should('exist').select('DU Cell Config - MAC');

    //cy.wait('@paramSets');

    cy.get('select[name="Contention Resolution Timer"]').select('sf64');
    cy.get('input[name="Preamble Received Target Power (dBm)"]').type('-90');
    cy.get('select[name="Power Ramping Step (dB)"]').select('dB6');
    cy.get('#configSetName').type('Cypress_Test_Value_DU_Cell_Config-MAC');
    cy.get('[data-testid="ORAN-DU-CU-CreateConfigSet-Manager"]')
      .find('button.chakra-button')
      .should('contain', 'Create config set')
      .click();
    cy.get('.chakra-alert').should('contain', 'Config set has been created successfully.');
  });

  it('Create config set for DU App Config - Identity and Connectivity', () => {
    cy.navigateToAdvancedView();
    cy.intercept('GET', '/nms/dev1/rad/config/parameter_sets/6').as('paramSets');
    cy.get('.chakra-container').find('[data-testid="config-sets"]').find('button').click();
    cy.get('.chakra-container').should('contain', 'Config sets');
    cy.get('.chakra-container').find('h2').should('contain', 'Config set manager');
    cy.get('[data-testid="ORAN-DU-CU-ConfigSet-Manager"]').should('exist');
    cy.get('[data-testid="ORAN-DU-CU-ConfigSet-Manager"]')
      .contains('button.chakra-button', 'Create a new config set')
      .should('exist')
      .click();
    cy.get('[data-testid="ORAN-DU-CU-CreateConfigSet-Manager"]')
      .find('select.chakra-select')
      .should('exist')
      .select('DU');
    cy.get('select.chakra-select').eq(1).should('exist').select('DU App Config - Identity and Connectivity');

    //cy.wait('@paramSets');

    cy.get('input[name="F1-U remoteAddress"]').type('*******');
    cy.get('input[name="F1-C remoteAddress"]').type('*******');
    cy.get('input[name="RU CONFIG ruUniqueId"]').type('*******');
    cy.get('input[name="FH-C subNetMask"]').type('255');
    cy.get('#configSetName').type('Cypress_Test_Value_DU_App_Config-Identity-and-Connectivity');
    cy.get('[data-testid="ORAN-DU-CU-CreateConfigSet-Manager"]')
      .find('button.chakra-button')
      .should('contain', 'Create config set')
      .click();
    cy.get('.chakra-alert').should('contain', 'Config set has been created successfully.');
  });
});
