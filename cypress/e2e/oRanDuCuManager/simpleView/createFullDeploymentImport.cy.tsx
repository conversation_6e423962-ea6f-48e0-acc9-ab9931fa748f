/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const oRanDuCuManagerUrl = '/oran-du-cu-manager';

describe('Test create full deployment import', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(oRanDuCuManagerUrl);
  });

  it('creates a new deployment, update interface, update identity using import', () => {
    cy.navigateToSimpleView();

    cy.get('.chakra-container').find('[data-testid="create-deployment"]').should('exist');
    cy.get('.chakra-container').find('[data-testid="edit-view-deployment"]').should('exist');
    cy.get('[data-testid="ORAN-DU-CU-Manager-App-Bar"]').should('exist');

    cy.get('.chakra-container').find('[data-testid="create-deployment"]').find('button').click();
    cy.get('.chakra-container').find('h2').should('contain', 'Create a new deployment');

    // Create deployment
    cy.get('input[type="file"]', { timeout: 10000 }).should('exist');
    cy.get('input[type="file"]').selectFile('cypress/fixtures/OranDuCuManager/importExport/22-10-24-deployment.json', {
      force: true,
    });
    cy.contains('button.chakra-button', 'Import deployment').click();
    cy.get('.chakra-container').find('[data-testId="create-deployment-button"]').should('exist').click();

    cy.get('.chakra-alert', { timeout: 10000 }).should('contain', 'Deployment custom resource created.');
    cy.get('.chakra-container').find('[data-testid="dataTable-container"]').should('exist');

    cy.get('[aria-label="Close"]').click();

    cy.get('.chakra-container').contains('button.chakra-button', 'Interface').should('exist').click();

    // Update Interface
    cy.get('input[type="file"]', { timeout: 10000 }).should('exist');
    cy.get('input[type="file"]').selectFile('cypress/fixtures/OranDuCuManager/importExport/22-10-24-interface.json', {
      force: true,
    });
    cy.contains('button.chakra-button', 'Import Interface').click();
    cy.contains('button', 'Update', { timeout: 10000 }).should('be.visible').click();

    cy.get('.chakra-alert', { timeout: 10000 }).should('contain', 'Interfaces have been updated successfully.');

    cy.get('[aria-label="Close"]').click();

    cy.get('.chakra-container').contains('button.chakra-button', 'Identity').should('exist').click();

    // Update Identity
    cy.get('input[type="file"]', { timeout: 20000 }).should('exist'); //NOTE: add because identities takes a while to load
    cy.get('input[type="file"]').selectFile('cypress/fixtures/OranDuCuManager/importExport/22-10-24-identity.json', {
      force: true,
    });
    cy.contains('button.chakra-button', 'Import Identities').click();
    cy.contains('button', 'Update', { timeout: 10000 }).should('be.visible').click();

    cy.get('.chakra-alert', { timeout: 10000 }).should('contain', 'Identity has been created successfully.');

    cy.get('[aria-label="Close"]').click();

    cy.get('.chakra-container').contains('button.chakra-button', 'Pods').should('exist').click();

    // activate pod/crs
    cy.get('[data-testid="custom-resource-table"] [data-testid="cell-main-table-status-icon"]')
      .should('exist')
      .then(($button) => {
        cy.wrap($button).should('have.css', 'background-color', 'rgb(0, 0, 0)');
      });

    cy.log('Clicking the Activate button from higher up in the DOM');
    cy.get('button.chakra-button:contains("Activate")', { timeout: 10000 }).should('exist').click();

    cy.get('.chakra-alert', { timeout: 10000 }).should('contain', 'Custom resource has been activated successfully.');

    cy.get('[aria-label="Close"]').click();

    cy.get('[data-testid="custom-resource-table"] [data-testid="cell-main-table-status-icon"]')
      .should('exist')
      .then(($button) => {
        cy.wrap($button).should('have.css', 'background-color', 'rgb(56, 161, 105)');
      });

    // deactivate pod/crs
    cy.get('[data-testid="custom-resource-table"] [data-testid="cell-main-table-status-icon"]')
      .should('exist')
      .then(($button) => {
        cy.wrap($button).should('have.css', 'background-color', 'rgb(56, 161, 105)');
      });

    cy.get('button.chakra-button:contains("Deactivate")', { timeout: 10000 }).should('exist').click();

    cy.get('.chakra-alert', { timeout: 10000 }).should(
      'contain',
      'Custom resource has been de-activated successfully.'
    );

    cy.get('[aria-label="Close"]').click();

    cy.get('[data-testid="custom-resource-table"] [data-testid="cell-main-table-status-icon"]')
      .should('exist')
      .then(($button) => {
        cy.wrap($button).should('have.css', 'background-color', 'rgb(0, 0, 0)');
      });

    // delete deployment
    cy.get('button.chakra-button:contains("Delete")', { timeout: 10000 }).should('exist').click();
    cy.get('footer.chakra-modal__footer').contains('button.chakra-button', 'Confirm').should('exist').click();
  });
});
