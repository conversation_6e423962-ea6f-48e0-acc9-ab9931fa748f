/* eslint-disable cypress/no-unnecessary-waiting */
/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const oRanDuCuManagerUrl = '/oran-du-cu-manager';

describe('Test create full deployment manual', () => {
  beforeEach(() => {
    cy.system_health();
    cy.useLogin();
    cy.visit(oRanDuCuManagerUrl);
  });

  function editRowByName(name, value) {
    cy.contains('table tr', name, { timeout: 10000 }).within(() => {
      cy.get('button[aria-label="Edit field"]').first().scrollIntoView().click({ force: true });
      cy.get('input[type="text"], input[type="number"]').clear().type(value);
    });
  }

  it.skip('display create deployment Landing page', () => {
    cy.navigateToSimpleView();

    cy.get('.chakra-container').find('[data-testid="create-deployment"]').should('exist');
    cy.get('.chakra-container').find('[data-testid="edit-view-deployment"]').should('exist');
    cy.get('[data-testid="ORAN-DU-CU-Manager-App-Bar"]').should('exist');

    cy.get('.chakra-container').find('[data-testid="create-deployment"]').find('button').click();
    cy.get('.chakra-container').find('h2').should('contain', 'Create a new deployment');
  });

  it('creates a new deployment, update interface, update identity manual', () => {
    cy.navigateToSimpleView();
    cy.intercept('POST', '**/rad/config/clusters/integration/deployments').as('CreateDeployment');

    cy.get('.chakra-container').find('[data-testid="create-deployment"]').should('exist');
    cy.get('.chakra-container').find('[data-testid="edit-view-deployment"]').should('exist');
    cy.get('[data-testid="ORAN-DU-CU-Manager-App-Bar"]').should('exist');

    cy.get('.chakra-container').find('[data-testid="create-deployment"]').find('button').click();

    // create cell for deployment
    cy.get('.chakra-container').find('h2').should('contain', 'Create a cell for the deployment');
    cy.get('.chakra-container').find('select[name="region"]').select('Marlow Development United Kingdom');
    cy.get('.chakra-container').find('select[name="site"]').select('8'); //NOTE: value 8 is Ansir Test
    cy.get('.chakra-container').find('select[name="placement"]').select('Indoor');
    cy.get('.chakra-container')
      .find('input[name="cell_ref"]')
      .should('not.have.value', '')
      .invoke('val')
      .then((cellRef) => {
        cy.log('Captured cellRef:', cellRef);
        cy.get('.chakra-container').contains('button.chakra-button', 'Create cell').should('exist').click();
        cy.get('.chakra-container');
        cy.wait(1000);
        cy.get('.chakra-alert', { timeout: 500 }).should('exist').should('be.visible'); // Ensure the alert exists
      });
    cy.get('.chakra-container').contains('button.chakra-button', 'Deployment').should('exist').click();

    // Create deployment
    cy.get('.chakra-container').find('h2').should('contain', 'Deployment');
    //cy.get('.chakra-container').find('#deployment_name').type('cypress-test-deployment');

    cy.get('.chakra-container').find('#f1_ip').type('*******');
    cy.get('.chakra-container').find('#e1_ip').type('*******');
    cy.get('.chakra-container').find('select[name="ru_vendor"]').select('Pal');

    cy.get('.chakra-container').find('#du_site_name').type('du-cypress-test');
    cy.get('.chakra-container').find('#cucp_site_name').type('cucp-cypress-test');
    cy.get('.chakra-container').find('#cuup_site_name').type('cuup-cypress-test');

    cy.get('.chakra-container')
      .find('select[name="du_cluster"]')
      .select('11:denseair-gke-bmctl-edge-5-1-16-6 - Dell 5 - oct_24');
    cy.get('.chakra-container')
      .find('select[name="cucp_cluster"]')
      .select('11:denseair-gke-bmctl-edge-5-1-16-6 - Dell 5  - oct_24');
    cy.get('.chakra-container')
      .find('select[name="cuup_cluster"]')
      .select('11:denseair-gke-bmctl-edge-5-1-16-6 - Dell 5  - oct_24');

    cy.get('.chakra-container').find('select[name="bands"]').select('41');
    cy.get('.chakra-container').find('select[name="bandwidths"]').select('40MHZ');
    cy.get('.chakra-container').find('#arfcns').type('503202');

    cy.get('.chakra-container').find('[data-testId="create-deployment-button"]').should('exist').click();
    cy.wait('@CreateDeployment', { timeout: 10000 }).its('response.statusCode').should('eq', 200);

    cy.get('.chakra-alert').should('contain', 'Deployment custom resource created.');
    cy.get('.chakra-container').find('[data-testid="dataTable-container"]').should('exist');

    cy.get('[aria-label="Close"]').click();

    cy.get('.chakra-container').contains('button.chakra-button', 'Interface').should('exist').click();

    // Update Interface
    editRowByName('FH-C localIpAddress', '***********');
    editRowByName('FH-C subNetMask', '24');
    editRowByName('FH-C gatewayAddress', '***********');
    editRowByName('FH-U localIpAddress', '***********');
    editRowByName('FH-U gatewayAddress', '***********');
    editRowByName('F1-C gatewayAddress', '***********');
    cy.contains('h2', 'DU Interfaces')
      .parent()
      .within(() => {
        editRowByName('F1-U localIpAddress', '***********');
        editRowByName('F1-U remoteAddress', '***********');
      });

    editRowByName('NgC remoteAddress', '***********');
    editRowByName('NgC gatewayAddress', '***********');

    editRowByName('NgU localIpAddress', '***********');
    editRowByName('NgU gatewayAddress', '***********');

    cy.contains('h2', 'CUUP Interfaces')
      .parent()
      .within(() => {
        editRowByName('F1-U remoteAddress', '***********');
        editRowByName('F1-U gatewayAddress', '***********');
      });

    editRowByName('E1 remoteAddress', '***********');

    cy.contains('button', 'Update').should('be.visible').click();

    cy.get('.chakra-alert', { timeout: 10000 }).should('contain', 'Interfaces have been updated successfully.');

    cy.get('[aria-label="Close"]').click();

    cy.get('.chakra-container').contains('button.chakra-button', 'Identity').should('exist').click();

    // Update Identity
    editRowByName('gNodeB DU ID', '2');
    editRowByName('Tracking Area Code (TAC)', '0002');
    editRowByName('RAN Area Code', '2');

    editRowByName('gNodeB CU Name', 'cypressCuName');

    editRowByName('gNodeB ID', '1');
    editRowByName('MCC', '312');
    editRowByName('MNC', '47');

    cy.contains('button', 'Update').should('be.visible').click();

    cy.get('.chakra-alert', { timeout: 10000 }).should('contain', 'Identity has been created successfully.');

    cy.get('[aria-label="Close"]').click();

    cy.get('.chakra-container').contains('button.chakra-button', 'Pods').should('exist').click();

    // activate pod/crs
    cy.get('[data-testid="custom-resource-table"] [data-testid="cell-main-table-status-icon"]')
      .should('exist')
      .then(($button) => {
        cy.wrap($button).should('have.css', 'background-color', 'rgb(0, 0, 0)');
      });

    cy.log('Clicking the Activate button from higher up in the DOM');
    cy.get('button.chakra-button:contains("Activate")', { timeout: 10000 }).should('exist').click();

    cy.get('.chakra-alert', { timeout: 10000 }).should('contain', 'Custom resource has been activated successfully.');

    cy.get('[aria-label="Close"]').click();

    cy.get('[data-testid="custom-resource-table"] [data-testid="cell-main-table-status-icon"]')
      .should('exist')
      .then(($button) => {
        cy.wrap($button).should('have.css', 'background-color', 'rgb(56, 161, 105)');
      });

    //cy.get('.chakra-container').contains('button.chakra-button', 'Activate').should('exist').click();

    // Pod created --NOTE: there is a timing issue here, the pods is not created immediately they are slow and go from critical to ok eventually
    // cy.get('[data-testid="pod-table"] [data-testid="cell-main-table-status-icon"]', { timeout: 10000 })
    //   .should('exist')
    //   .then(($button) => {
    //     cy.wrap($button).should('have.css', 'background-color', 'rgb(56, 161, 105)');
    //   });

    // deactivate pod/crs
    cy.get('[data-testid="custom-resource-table"] [data-testid="cell-main-table-status-icon"]')
      .should('exist')
      .then(($button) => {
        cy.wrap($button).should('have.css', 'background-color', 'rgb(56, 161, 105)');
      });

    cy.get('button.chakra-button:contains("Deactivate")', { timeout: 10000 }).should('exist').click();

    cy.get('.chakra-alert', { timeout: 10000 }).should(
      'contain',
      'Custom resource has been de-activated successfully.'
    );

    cy.get('[aria-label="Close"]').click();

    cy.get('[data-testid="custom-resource-table"] [data-testid="cell-main-table-status-icon"]')
      .should('exist')
      .then(($button) => {
        cy.wrap($button).should('have.css', 'background-color', 'rgb(0, 0, 0)');
      });

    // delete deployment
    cy.get('button.chakra-button:contains("Delete")', { timeout: 10000 }).should('exist').click();
    cy.get('footer.chakra-modal__footer').contains('button.chakra-button', 'Confirm').should('exist').click();
  });
});
