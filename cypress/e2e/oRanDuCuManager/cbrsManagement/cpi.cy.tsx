/* eslint-disable cypress/require-data-selectors */
/// <reference types="cypress" />
import 'cypress-real-events';

const cpiViewUrl = '/oran-du-cu-manager/cbrs-management/cpi/';

describe('CBRS Overview', () => {
  beforeEach(() => {
    const paths = {
      search: 'cpi/search.json',
    };

    // Logging paths to verify
    cy.task('log', 'Validating fixture paths...');
    cy.task('log', JSON.stringify(paths));

    // Mocking network requests with the exact URLs
    cy.intercept('GET', '**/orc/cpi/search**', {
      fixture: paths.search,
    }).as('getCbsdDevices');

    // Ensure system health and login before visiting the page
    cy.system_health();
    cy.useLogin();
    cy.visit(cpiViewUrl);
    cy.log('Visited cbsd ');
    cy.wait(2000);
  });

  it('should load the CPI view page', () => {
    cy.url().should('include', cpiViewUrl);
    cy.get('#tabs-\\:rl\\:--tab-1').should('be.visible');
    cy.get('#tabs-\\:rl\\:--tab-1').should('be.enabled');
    cy.get('[data-testid="cpi-heading"] > .chakra-heading').should('be.visible');
    cy.get('[data-testid="cpi-heading"] > .chakra-heading').should(
      'have.text',
      'Certified Professional Installer (CPI)'
    );
  });

  it('should load and display CPI data', () => {
    cy.get('[data-testid="dataTable-container"] > .chakra-table > tbody.css-0 > :nth-child(1) > :nth-child(1)').should(
      'be.visible'
    );
    cy.get('[data-testid="dataTable-container"] > .chakra-table > tbody.css-0 > :nth-child(1) > :nth-child(1)').should(
      'have.text',
      '<EMAIL>'
    );
    cy.get('[data-testid="dataTable-container"] > .chakra-table > tbody.css-0 > :nth-child(1) > :nth-child(2)').should(
      'be.visible'
    );
    cy.get('[data-testid="dataTable-container"] > .chakra-table > tbody.css-0 > :nth-child(1) > :nth-child(2)').should(
      'have.text',
      'test_installer'
    );
    cy.get('[data-testid="dataTable-container"] > .chakra-table > tbody.css-0 > :nth-child(1) > :nth-child(3)').should(
      'be.visible'
    );
    cy.get('[data-testid="dataTable-container"] > .chakra-table > tbody.css-0 > :nth-child(1) > :nth-child(3)').should(
      'have.text',
      'Test_First'
    );
  });
});
