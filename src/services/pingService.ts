import { apiClient } from './httpCommon';
import {
  PingRequest,
  PingHistoryEntry,
  ContinuousPingConfig,
  ContinuousPingSession,
  BulkPingStatusResponse,
  NetworkConnectivityStatus,
  PingMetrics,
  PingAlertConfig,
  PingAlert,
} from '../types/ping.types';
import { PingResponse } from './orchestrator';

export class PingService {
  /**
   * Ping a single node - Updated to use the actual orchestrator endpoint
   */
  async pingNode(nodeId: string): Promise<PingResponse> {
    if (!nodeId || nodeId.trim() === '') {
      throw new Error('Node ID is required');
    }
    const { data } = await apiClient.get<PingResponse>(`/orc/connectivity/ping/${nodeId}`);
    return data;
  }

  /**
   * Legacy ping method with full request object (for backward compatibility)
   */
  async pingNodeWithRequest(request: PingRequest): Promise<PingResponse> {
    this.validatePingRequest(request);
    // For now, we'll use the nodeId from the request to ping via the orchestrator endpoint
    // In the future, this could be updated to use request parameters
    return this.pingNode(request.nodeId);
  }

  /**
   * Get ping history for a node
   */
  async getPingHistory(nodeId: string, hours = 24): Promise<PingHistoryEntry[]> {
    const { data } = await apiClient.get<PingHistoryEntry[]>(`/orc/network/ping/history/${nodeId}`, {
      params: { hours },
    });
    return data;
  }

  /**
   * Start continuous ping monitoring
   */
  async startContinuousPing(config: ContinuousPingConfig): Promise<ContinuousPingSession> {
    const { data } = await apiClient.post<ContinuousPingSession>('/orc/network/ping/continuous', config);
    return data;
  }

  /**
   * Stop continuous ping monitoring
   */
  async stopContinuousPing(sessionId: string): Promise<{ sessionId: string; status: string }> {
    const { data } = await apiClient.delete<{ sessionId: string; status: string }>(
      `/orc/network/ping/continuous/${sessionId}`
    );
    return data;
  }

  /**
   * Get bulk ping status for multiple nodes
   */
  async getBulkPingStatus(nodeIds: string[]): Promise<BulkPingStatusResponse[]> {
    const { data } = await apiClient.post<BulkPingStatusResponse[]>('/orc/network/ping/bulk-status', { nodeIds });
    return data;
  }

  /**
   * Get network connectivity status for all nodes
   */
  async getNetworkConnectivityStatus(): Promise<NetworkConnectivityStatus[]> {
    const { data } = await apiClient.get<NetworkConnectivityStatus[]>('/orc/network/connectivity/status');
    return data;
  }

  /**
   * Get ping metrics summary
   */
  async getPingMetrics(): Promise<PingMetrics> {
    const { data } = await apiClient.get<PingMetrics>('/orc/network/ping/metrics');
    return data;
  }

  /**
   * Configure ping alerts for a node
   */
  async configurePingAlerts(config: PingAlertConfig): Promise<PingAlertConfig> {
    const { data } = await apiClient.post<PingAlertConfig>('/orc/network/ping/alerts/config', config);
    return data;
  }

  /**
   * Get ping alerts
   */
  async getPingAlerts(nodeId?: string): Promise<PingAlert[]> {
    const params = nodeId ? { nodeId } : {};
    const { data } = await apiClient.get<PingAlert[]>('/orc/network/ping/alerts', {
      params,
    });
    return data;
  }

  /**
   * Acknowledge ping alert
   */
  async acknowledgePingAlert(alertId: string, acknowledgedBy: string): Promise<PingAlert> {
    const { data } = await apiClient.patch<PingAlert>(`/orc/network/ping/alerts/${alertId}/acknowledge`, {
      acknowledgedBy,
    });
    return data;
  }

  /**
   * Test network connectivity to a specific IP
   * Note: The current API doesn't support arbitrary IP pinging, only node pinging
   */
  async testConnectivity(nodeId: string, targetIp?: string, count = 4, timeout = 5000): Promise<PingResponse> {
    // For now, we'll just ping the node since the API doesn't support arbitrary IPs
    return this.pingNode(nodeId);
  }

  /**
   * Get real-time ping stream (WebSocket connection)
   */
  startRealTimePing(
    nodeId: string,
    targetIp: string,
    onUpdate: (response: PingResponse) => void,
    onError: (error: Error) => void
  ): () => void {
    // In a real implementation, this would establish a WebSocket connection
    // For testing purposes, we'll simulate with a periodic API call
    const interval = setInterval(async () => {
      try {
        const response = await this.pingNode(nodeId);
        onUpdate(response);
      } catch (error) {
        onError(error as Error);
      }
    }, 30000); // 30 second intervals

    // Return cleanup function
    return () => clearInterval(interval);
  }

  /**
   * Validate ping request parameters
   */
  private validatePingRequest(request: PingRequest): void {
    if (!request.nodeId || request.nodeId.trim() === '') {
      throw new Error('Node ID is required');
    }

    if (!request.targetIp || request.targetIp.trim() === '') {
      throw new Error('Target IP is required');
    }

    // Basic IP validation
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    if (!ipRegex.test(request.targetIp)) {
      throw new Error('Invalid IP address format');
    }

    if (request.count !== undefined && request.count <= 0) {
      throw new Error('Count must be greater than 0');
    }

    if (request.timeout !== undefined && request.timeout <= 0) {
      throw new Error('Timeout must be greater than 0');
    }

    if (request.packetSize !== undefined && (request.packetSize < 8 || request.packetSize > 65507)) {
      throw new Error('Packet size must be between 8 and 65507 bytes');
    }
  }
}

// Create and export a singleton instance
export const pingService = new PingService();
