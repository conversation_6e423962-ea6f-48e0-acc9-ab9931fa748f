import { Box, Flex, Heading, Stack, useColorModeValue, useDisclosure } from '@chakra-ui/react';
import { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { InterfaceFieldData, SelectedOption } from '../../../types/duCuManager.type';
import AppBar from '../AppBar';
import SimpleViewAppBar from './SimpleViewAppBar';
import DeploymentForm from '../formFields/simpleView/DeploymentForm';

export const initialFieldData: InterfaceFieldData = {
  default: null,
  user_setting: null,
  name: '',
  target: '',
};

const Deployment = () => {
  const location = useLocation();
  const passedData: any = location.state;

  const deployment_type = passedData?.deployment_type;
  const generatedCellRef = passedData?.generatedCellRef;
  const deployment_name = passedData?.selectedOption?.deployment_name;
  const maxCell = passedData?.formValues?.deployment_params?.max_cells;

  const useCase = passedData?.selectedUseCaseObj?.use_case;
  const ssbArfcn = passedData?.formValues?.deployment_params?.ssb_arfcns;
  const duFhVlans = passedData?.formValues?.deployment_params?.du_fh_vlans;

  const colorModeValue = useColorModeValue('sm', 'sm-dark');
  const [editing, setEditing] = useState<Record<string, boolean>>({});
  const [selectedOption, setSelectedOption] = useState<SelectedOption>({
    deployment_name: generatedCellRef || deployment_name,
    du_site_name: '',
    cucp_site_name: '',
    cuup_site_name: '',
    du_cluster: null,
    cucp_cluster: null,
    cuup_cluster: null,
    du_cell_config_set_ids: [],
    du_app_config_set_ids: [],
    cu_cell_config_set_ids: [],
    cu_cp_app_config_set_ids: [],
    cu_up_app_config_set_ids: [],
    ru_vendor: '',
    f1_ip: '',
    e1_ip: '',
    deployment_params: {
      rf_use_case: Number(useCase) || 0,
      max_cells: maxCell,
      is_comp: false,
      comp_trp_count: 0,
      ssb_arfcns: Array.isArray(ssbArfcn) ? ssbArfcn.map(Number) : [],
      du_fh_vlans: Array.isArray(duFhVlans) && duFhVlans.length > 0 ? duFhVlans.map(Number) : [0],
    },
  });

  return (
    <>
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Stack spacing="1">
          <Heading fontWeight="bold">O-RAN DU/CU Manager</Heading>
        </Stack>
      </Stack>
      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: colorModeValue,
          }}
        >
          {/* App bar */}
          <Flex justify="center" m="8">
            {deployment_type === 'edit' && (
              <SimpleViewAppBar deployment_type={deployment_type} selectedOption={selectedOption} />
            )}
            {deployment_type === 'new' && <AppBar caller="simpleView" />}
          </Flex>
          <Heading as="h2" size="lg" textAlign="center" pb="4">
            Deployment
          </Heading>
          <Box p="12">
            <DeploymentForm
              isDisabled={deployment_type === 'edit'}
              selectedOption={selectedOption}
              setSelectedOption={setSelectedOption}
              caller="simpleViewCustomResource"
              deployment_type={deployment_type}
              editing={editing}
              setEditing={setEditing}
              maxCell={maxCell}
            />
          </Box>
        </Box>
      </Stack>
    </>
  );
};

export default Deployment;
