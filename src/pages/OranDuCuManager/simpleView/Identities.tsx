import { <PERSON>, But<PERSON>, Divider, Flex, <PERSON><PERSON>, <PERSON>ack, useColorModeValue } from '@chakra-ui/react';
import { useState, useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Loader from '../../../components/loader/Loader';
import { InterfaceFieldData } from '../../../types/duCuManager.type';
import AppBar from '../AppBar';
import transformIdentitiesTableData, {
  transformIdentitiesForApi,
} from '../hooks/identities/useTransformIdentitiesTableData';
import useClusterList from '../hooks/services/useClusters';
import useIdentity from '../hooks/services/useIdentity';
import InterfaceAndIdentitiesForm from './interfaces/summary/InterfaceAndIdentitiesForm';

import { isEqual, omit, some } from 'lodash';
import SimpleViewAppBar from './SimpleViewAppBar';
import { ChevronRightIcon } from '@chakra-ui/icons';
import useLogin from '../../../hooks/useLogin';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../data/constants';
import { DynamicTableHandle } from './interfaces/tables/DynamicTable';

export const initialFieldData: InterfaceFieldData = {
  default: null,
  user_setting: null,
  name: '',
  target: '',
};

type NestedIdentities = {
  du?: Record<string, { name: string; default: string; user_setting?: string; target?: string }>;
  shared?: Record<string, { name: string; default: string; user_setting?: string; target?: string }>;
};

const Identities = () => {
  const [editedUserSettings, setEditedUserSettings] = useState<NestedIdentities>({});
  const [editing, setEditing] = useState<Record<string, boolean>>({});
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [isCreateSuccess, setIsCreateSuccess] = useState(false);

  const dynamicTableRef = useRef<DynamicTableHandle>(null);
  const podsButtonRef = useRef<HTMLButtonElement>(null);

  const { checkNmsDevAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkNmsDevAccess(READ_WRITE_ACCESS_ROLES);

  const navigate = useNavigate();
  const location = useLocation();
  const passedData: any = location.state;
  const deployment_type = passedData?.deployment_type;
  const selectedOption = passedData?.selectedOption;

  const colorModeValue = useColorModeValue('sm', 'sm-dark');

  //API
  const { createIdentities, getIdentitiesList } = useIdentity();
  const mutation = createIdentities();
  const { isLoading } = mutation;
  //GET Clusters
  const { data: clusterList = [], isLoading: isClusterLoading, error: clusterError } = useClusterList();
  //GET Identities
  const {
    data: getIdentitiesData,
    error: identitiesError,
    isLoading: isIdentitiesLoading,
    refetch: refetchIdentities,
    isFetching: isIdentitiesFetching,
  } = getIdentitiesList(selectedOption?.deployment_name, true);

  useEffect(() => {
    if (!getIdentitiesData || !selectedOption) return;

    //NOTE: Build a “nested” structure exactly like our DynamicTable expects:
    // DynamicTables, FileUpload and useTransformIdentitiesTableData all use the same structure
    const nested: {
      du: Record<string, { name: string; default: string; user_setting?: string; target?: string }>;
      shared: Record<string, { name: string; default: string; user_setting?: string; target?: string }>;
    } = {
      du: {},
      shared: {},
    };

    // ──── BUILD DU FIELDS IN THE FIXED ORDER ────
    const duKeyOrder: string[] = [
      'tracking_area_code', // → rowIndex 0
      'ran_area_code', // → rowIndex 1
      'physical_cell_id', // → rowIndex 2
      'tracking_area_code2', // → rowIndex 3
      'ran_area_code2', // → rowIndex 4
      'physical_cell_id2', // → rowIndex 5
    ];

    let duIndex = 0;
    for (const fieldKey of duKeyOrder) {
      const obj = getIdentitiesData.du[fieldKey];
      if (!obj) {
        continue;
      }

      nested.du[fieldKey] = {
        name: obj.name,
        default: obj.default.toString(),
        user_setting: obj.user_setting?.toString() ?? '',
        target: obj.target,
      };

      // Seed editing[cellKey] = false for initial load (not in edit mode)
      const cellKey = `du-${fieldKey}-user_setting-${duIndex}`;
      setEditing((prev) => ({ ...prev, [cellKey]: false }));
      duIndex++;
    }

    // ──── BUILD SHARED FIELDS IN THE FIXED ORDER ────
    const sharedKeyOrder: string[] = [
      'gnodeb_id', // → rowIndex 0
      'mcc', // → rowIndex 1
      'mnc', // → rowIndex 2
      'gnodeb_id2', // → rowIndex 3
      'mcc2', // → rowIndex 4
      'mnc2', // → rowIndex 5
    ];

    let sharedIndex = 0;
    for (const fieldKey of sharedKeyOrder) {
      const obj = getIdentitiesData.shared[fieldKey];
      if (!obj) {
        continue;
      }

      nested.shared[fieldKey] = {
        name: obj.name,
        default: obj.default.toString(),
        user_setting: obj.user_setting?.toString() ?? '',
        target: obj.target,
      };

      const cellKey = `shared-${fieldKey}-user_setting-${sharedIndex}`;
      setEditing((prev) => ({ ...prev, [cellKey]: false }));
      sharedIndex++;
    }

    setEditedUserSettings(nested);
  }, [getIdentitiesData, selectedOption]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (dynamicTableRef.current) {
      const allValid = await dynamicTableRef.current.validateAllIdentities();

      if (!allValid) {
        console.warn('🔹 Validation failed—stopping submission');
        return;
      }
    }

    if (!editedUserSettings || !selectedOption) return;
    const flatValues = dynamicTableRef.current?.getIdentityValues() || {};

    const apiPayload = transformIdentitiesForApi(flatValues, selectedOption);

    mutation.mutate(apiPayload, {
      onSuccess: (data) => {
        setIsCreateSuccess(true);
        refetchIdentities();
      },
      onError: (error) => {
        console.error('Error saving identities:', error);
      },
    });
  };

  useEffect(() => {
    if (isCreateSuccess && !isIdentitiesFetching && podsButtonRef.current) {
      // defer until after the render so the button is actually in the DOM
      setTimeout(() => podsButtonRef.current!.scrollIntoView({ behavior: 'smooth' }), 0);
    }
  }, [isCreateSuccess, isIdentitiesFetching]);

  if (selectedOption && isIdentitiesLoading) return <Loader />;

  return (
    <>
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Stack spacing="1">
          <Heading fontWeight="bold">O-RAN DU/CU Manager</Heading>
        </Stack>
      </Stack>
      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: colorModeValue,
          }}
        >
          {/* App bar */}
          <Flex justify="center" m="8">
            {deployment_type === 'edit' && (
              <SimpleViewAppBar deployment_type={deployment_type} selectedOption={selectedOption} />
            )}
            {deployment_type === 'new' && <AppBar caller="simpleView" />}
          </Flex>
          <Heading as="h2" size="lg" textAlign="center" pb="4">
            Identities
          </Heading>
          <Box p="12">
            <InterfaceAndIdentitiesForm
              ref={dynamicTableRef}
              caller="identities"
              isCreateIdentitiesSuccess={isCreateSuccess}
              getApiData={getIdentitiesData}
              clusterList={clusterList}
              editedUserSettings={editedUserSettings}
              setEditedUserSettings={setEditedUserSettings}
              isIdentitiesFetching={isIdentitiesFetching}
              selectedOption={selectedOption}
              setIsButtonDisabled={setIsButtonDisabled}
              setEditing={setEditing}
              editing={editing}
            />
            {isLoading ? (
              <Loader />
            ) : (
              <form onSubmit={handleSubmit}>
                <Divider />
                {deployment_type === 'edit' ? (
                  <Flex justifyContent="space-between" alignItems="center" py="4">
                    {false && <Button variant="primary">placeholder Button</Button>}

                    {checkRoleAccess && (
                      <Box flex="1" textAlign="center">
                        <Button variant="primary" type="submit" isDisabled={!some(editing, (v) => v === true)}>
                          Update
                        </Button>
                      </Box>
                    )}

                    {false && <Button variant="primary">Update Identity</Button>}
                  </Flex>
                ) : (
                  <Flex justifyContent="space-between" alignItems="center" py="4">
                    {false && <Button variant="primary">placeholder Button</Button>}

                    {checkRoleAccess && (
                      <Box flex="1" textAlign="center">
                        <Button variant="primary" type="submit" isDisabled={!some(editing, (v) => v === true)}>
                          Update
                        </Button>
                      </Box>
                    )}

                    {isCreateSuccess && (
                      <Button
                        ref={podsButtonRef}
                        variant="primary"
                        onClick={() =>
                          navigate('/oran-du-cu-manager/simple-view/update-pods', {
                            state: { selectedOption, deployment_type },
                          })
                        }
                      >
                        Pods
                        <ChevronRightIcon boxSize={6} />
                      </Button>
                    )}
                  </Flex>
                )}
              </form>
            )}
          </Box>
        </Box>
      </Stack>
    </>
  );
};

export default Identities;
