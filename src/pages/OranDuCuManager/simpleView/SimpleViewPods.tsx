import {
  Box,
  Card,
  CardBody,
  Flex,
  Text,
  Heading,
  Select,
  Stack,
  useColorModeValue,
  Alert,
  AlertIcon,
  Button,
  AlertTitle,
  AlertDescription,
} from '@chakra-ui/react';
import { isEmpty } from 'lodash';
import { useEffect, useState } from 'react';
import { FiLink } from 'react-icons/fi';
import { useLocation } from 'react-router';
import { useNavigate } from 'react-router-dom';
import { passedData, SelectedOption } from '../../../types/duCuManager.type';
import AppBar from '../AppBar';
import DeploymentForm from '../formFields/simpleView/DeploymentForm';
import useCustomResource from '../hooks/services/useCustomResource';
import useRus from '../hooks/services/useRU';
import SimpleViewAppBar from './SimpleViewAppBar';

type SimpleViewPodsProps = {
  selectedOption?: SelectedOption;
  deployment_type?: string;
  view?: string;
};

const SimpleViewPods: React.FC<SimpleViewPodsProps> = ({
  selectedOption: initialSelectedOptionProp,
  deployment_type: initialDeploymentTypeProp,
  view = 'simple',
}) => {
  const colorModeValue = useColorModeValue('sm', 'sm-dark');
  const location = useLocation();
  const navigate = useNavigate();

  const passedData: passedData | undefined = location.state;
  const initialSelectedOption = initialSelectedOptionProp || passedData?.selectedOption;
  const initialDeploymentType = initialDeploymentTypeProp || passedData?.deployment_type;
  const maxCell = passedData?.selectedOption?.deployment_params?.max_cells || 0;
  const deployment_type = 'edit';

  const navigateToPage = (path: any) => {
    navigate(path, {
      state: { deployment_type, selectedOption },
    });
  };

  const [selectedOption, setSelectedOption] = useState<SelectedOption>({
    deployment_name: '',
    du_site_name: '',
    cucp_site_name: '',
    cuup_site_name: '',
    du_cluster: null,
    cucp_cluster: null,
    cuup_cluster: null,
    du_cell_config_set_ids: [],
    du_app_config_set_ids: [],
    cu_cell_config_set_ids: [],
    cu_cp_app_config_set_ids: [],
    cu_up_app_config_set_ids: [],
    ru_vendor: 'EdgeQ',
    f1_ip: '',
    e1_ip: '',
  });

  const [editing, setEditing] = useState<Record<string, boolean>>({});

  const isReady = !!selectedOption?.deployment_name;

  // API calls
  const { getAllDeployments, getDeploymentCustomResources } = useCustomResource();
  const { getDeploymentRus } = useRus();

  // CR's for deployment
  const {
    data: DeploymentCustomResourcesData,
    isLoading: isDeploymentCustomResourcesDataLoading,
    error: deploymentCustomResourcesDataError,
  } = getDeploymentCustomResources(selectedOption?.deployment_name ?? '', isReady);

  // Get all Deployments
  const {
    data: allDeployments,
    isLoading: isAllDeploymentsLoading,
    error: allDeploymentsError,
  } = getAllDeployments('edit');

  const {
    data: ruDeployments,
    isLoading: isRuDeploymentsLoading,
    isFetching: isRuDeploymentsFetching,
  } = getDeploymentRus(selectedOption?.deployment_name ?? '', deployment_type);

  const handleUserInputChange = (selectedValue: string) => {
    if (selectedValue !== null) {
      const parsedValue = JSON.parse(selectedValue);
      setSelectedOption((prev: any) => ({
        ...prev,
        deployment_name: parsedValue.deployment_name,
        du_site_name: parsedValue.du_site_name,
        cucp_site_name: parsedValue.cucp_site_name,
        cuup_site_name: parsedValue.cuup_site_name,
        du_cluster: parsedValue.du_cluster,
        cucp_cluster: parsedValue.cucp_cluster,
        cuup_cluster: parsedValue.cuup_cluster,
        du_cell_config_set_ids: parsedValue.du_cell_config_set_ids,
        du_app_config_set_ids: parsedValue.du_app_config_set_ids,
        cu_cell_config_set_ids: parsedValue.cu_cell_config_set_ids,
        cu_cp_app_config_set_ids: parsedValue.cu_cp_app_config_set_ids,
        cu_up_app_config_set_ids: parsedValue.cu_up_app_config_set_ids,
        ru_vendor: parsedValue.ru_vendor,
        f1_ip: parsedValue.f1_ip,
        e1_ip: parsedValue.e1_ip,
      }));
    }
  };

  useEffect(() => {
    if (initialSelectedOption) {
      setSelectedOption((prev) => ({
        ...prev,
        ...initialSelectedOption,
        deployment_name: initialSelectedOption.deployment_name || '',
        du_site_name: initialSelectedOption.du_site_name || '',
        cucp_site_name: initialSelectedOption.cucp_site_name || '',
        cuup_site_name: initialSelectedOption.cuup_site_name || '',
        du_cluster: initialSelectedOption.du_cluster || null,
        cucp_cluster: initialSelectedOption.cucp_cluster || null,
        cuup_cluster: initialSelectedOption.cuup_cluster || null,
        f1_ip: initialSelectedOption.f1_ip || '',
        e1_ip: initialSelectedOption.e1_ip || '',
      }));
    }
  }, [initialSelectedOption]);

  return (
    <>
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Stack spacing="1">
          <Heading fontWeight="bold">O-RAN DU/CU Manager</Heading>
        </Stack>
      </Stack>
      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: colorModeValue,
          }}
        >
          {/* App bar */}
          <Flex justify="center" m="8">
            {initialDeploymentType === 'edit' && view === 'simple' && (
              <SimpleViewAppBar deployment_type={initialDeploymentType} selectedOption={selectedOption} />
            )}
            {initialDeploymentType === 'new' && <AppBar caller="simpleView" />}
            {view === 'advanced' && <AppBar caller="advancedView" />}
          </Flex>
          <Heading as="h2" size="lg" textAlign="center">
            Pods
          </Heading>

          {!isRuDeploymentsLoading && !isRuDeploymentsFetching && isEmpty(ruDeployments) && (
            <Box p={6} bg="gray.50" borderRadius="lg" boxShadow="md" mx="20">
              <Alert
                status="info"
                variant="left-accent"
                flexDirection="column"
                alignItems="flex-start"
                justifyContent="center"
                textAlign="left"
                p={4}
                borderRadius="md"
                mb={4}
              >
                <Flex alignItems="center" mb={2}>
                  <AlertIcon />
                  <AlertTitle mr={0} ml={2} fontSize="lg" fontWeight="bold">
                    Action Required: Attach Radio Unit
                  </AlertTitle>
                </Flex>
                <AlertDescription mb={4}>
                  A Radio Unit (RU) must be attached to this deployment before activating custom resources and creating
                  pods. Failure to do so will prevent successful pod creation.
                </AlertDescription>
                <Button
                  colorScheme="teal"
                  size="md"
                  rightIcon={<FiLink />}
                  onClick={() =>
                    navigate('/oran-du-cu-manager/simple-view/deployment', {
                      state: {
                        selectedOption: initialSelectedOption,
                        deployment_type: deployment_type,
                        scrollTo: 'ruSelection',
                      },
                    })
                  }
                >
                  Attach Radio Unit
                </Button>
              </Alert>
            </Box>
          )}

          {view === 'advanced' && (
            <Box mx="15%" mb="18">
              <Heading textAlign="center" as="h2" fontSize="20px" mb="4" mt="12">
                Select a deployment
              </Heading>
              <Select
                mb="2"
                placeholder="Select option"
                value={selectedOption?.cluster ?? undefined}
                onChange={(e) => handleUserInputChange(e.target.value)}
              >
                {allDeployments?.map((item: any) => (
                  <option key={item.id} value={JSON.stringify(item)}>
                    {`${item.id}: ${item.deployment_name}`}
                  </option>
                ))}
              </Select>
            </Box>
          )}

          {/* FORM */}
          <Box mx="20" mt="10">
            <DeploymentForm
              isDisabled={true}
              DeploymentCustomResourcesData={DeploymentCustomResourcesData}
              selectedOption={selectedOption}
              setSelectedOption={setSelectedOption}
              caller="simpleViewPods"
              editing={editing}
              setEditing={setEditing}
              maxCell={maxCell}
            />
          </Box>
        </Box>
      </Stack>
    </>
  );
};

export default SimpleViewPods;
