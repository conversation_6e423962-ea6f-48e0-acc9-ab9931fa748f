import { <PERSON>, <PERSON><PERSON>, Divider, Flex } from '@chakra-ui/react';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import 'reactflow/dist/style.css';
import Loader from '../../../../components/loader/Loader';
import useInterface from '../../hooks/services/useInterface';
import InterfaceAndIdentitiesForm from './summary/InterfaceAndIdentitiesForm';
import { isEqual } from 'lodash';
import { InterfaceFieldData } from '../../../../types/duCuManager.type';
import { initialFieldData } from '../Identities';
import useCustomResource from '../../hooks/services/useCustomResource';
import { ChevronRightIcon } from '@chakra-ui/icons';
import useLogin from '../../../../hooks/useLogin';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../../data/constants';
import { DynamicTableHandle } from './tables/DynamicTable';
import { transformInterfaceForExport } from '../../../../components/fileUpload/FileUpload';

const InterfaceTable: React.FC = () => {
  const [isCreateInterfaceSuccess, setIsCreateInterfaceSuccess] = useState(false);
  const [createResponseData, setCreateResponseData] = useState(null);
  const [editedUserSettings, setEditedUserSettings] = useState<Record<string, InterfaceFieldData>>({});
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [editing, setEditing] = useState<Record<string, boolean>>({});

  const { checkNmsDevAccess, getNMSRoles } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkNmsDevAccess(READ_WRITE_ACCESS_ROLES);

  const navigate = useNavigate();
  const location = useLocation();
  const passedData = location.state as any;
  const deployment_type = passedData?.deployment_type;
  const selectedOption = passedData?.selectedOption;
  const [isClustersValid, setIsClustersValid] = useState(false);
  const [payloadData, setPayloadData] = useState({
    deployment_name: selectedOption?.deployment_name ?? '',
    version: 'v1beta1',
    du_cluster: selectedOption?.du_cluster ?? 0,
    cucp_cluster: selectedOption?.cucp_cluster ?? 0,
    cuup_cluster: selectedOption?.cuup_cluster ?? 0,
  });

  const dynamicTableRef = useRef<DynamicTableHandle>(null);
  const identityButtonRef = useRef<HTMLButtonElement>(null);

  // API
  const { createInterface, getInterfaceList } = useInterface();
  const { getClusterList } = useCustomResource();
  // POST interface
  const mutation = createInterface();
  const { isLoading } = mutation;
  // GET interface
  const {
    data: getInterfaceData,
    error,
    isLoading: isInterfaceListQueryLoading,
    refetch: refetchInterfaceList,
    isFetching: isInterfaceListQueryFetching,
  } = getInterfaceList(payloadData, isClustersValid);

  // Get cluster list
  const { data: clusterList, isLoading: isClusterLoading } = getClusterList();

  // Submit form
  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();

    if (!dynamicTableRef.current) {
      return;
    }

    const flatInterfaceValues = dynamicTableRef.current.getInterfaceValues();

    const reconstructed = transformInterfaceForExport(flatInterfaceValues);

    const payload = {
      ...reconstructed,
      deployment_name: selectedOption?.deployment_name || '',
    };

    mutation.mutate(payload, {
      onSuccess: (data) => {
        setIsCreateInterfaceSuccess(true);
        setCreateResponseData(data);
        refetchInterfaceList();
        setEditing({});
      },
      onError: (err) => {
        console.error('Error submitting data:', err);
      },
    });
  };

  useEffect(() => {
    const isDeploymentValid = !_.isEmpty(payloadData.deployment_name);

    setIsClustersValid(isDeploymentValid);
  }, [payloadData]);

  useEffect(() => {
    if (isCreateInterfaceSuccess && !isInterfaceListQueryFetching && identityButtonRef.current) {
      identityButtonRef.current!.scrollIntoView({ behavior: 'smooth' });
    }
  }, [isCreateInterfaceSuccess, isInterfaceListQueryFetching]);

  if (selectedOption && isInterfaceListQueryLoading) return <Loader />;

  return (
    <Box p="12">
      <InterfaceAndIdentitiesForm
        caller="interfaces"
        isCreateInterfaceSuccess={isCreateInterfaceSuccess}
        getApiData={getInterfaceData}
        clusterList={clusterList}
        editedUserSettings={editedUserSettings}
        setEditedUserSettings={setEditedUserSettings}
        isInterfaceListQueryFetching={isInterfaceListQueryFetching}
        selectedOption={selectedOption}
        setIsButtonDisabled={setIsButtonDisabled}
        setEditing={setEditing}
        editing={editing}
        ref={dynamicTableRef}
      />

      {/* Buttons */}
      {isLoading ? (
        <Loader />
      ) : (
        <form onSubmit={handleSubmit}>
          <Divider />
          {deployment_type === 'edit' ? (
            <Flex justifyContent="space-between" alignItems="center" py="4">
              {false && <Button variant="primary">placeholder Button</Button>}

              {checkRoleAccess && (
                <Box flex="1" textAlign="center">
                  <Button variant="primary" type="submit" isDisabled={isButtonDisabled}>
                    Update
                  </Button>
                </Box>
              )}

              {false && <Button variant="primary">Identity</Button>}
            </Flex>
          ) : (
            <Flex justifyContent="space-between" alignItems="center" py="4">
              {false && <Button variant="primary">placeholder Button</Button>}

              {checkRoleAccess && (
                <Box flex="1" textAlign="center">
                  <Button variant="primary" type="submit" isDisabled={isButtonDisabled}>
                    Update
                  </Button>
                </Box>
              )}

              {isCreateInterfaceSuccess && (
                <Button
                  ref={identityButtonRef}
                  variant="primary"
                  onClick={() =>
                    navigate('/oran-du-cu-manager/simple-view/identities', {
                      state: { createResponseData, selectedOption, deployment_type, getInterfaceData },
                    })
                  }
                >
                  Identity
                  <ChevronRightIcon boxSize={6} />
                </Button>
              )}
            </Flex>
          )}
        </form>
      )}
    </Box>
  );
};

export default InterfaceTable;
