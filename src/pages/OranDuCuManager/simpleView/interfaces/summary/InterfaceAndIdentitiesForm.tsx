import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { forwardRef, useState } from 'react';
import DynamicTable from '../tables/DynamicTable';
import DeploymentSummary from './DeploymentSummary';

type InterfaceAndIdentitiesFormProps = {
  caller?: 'interfaces' | 'identities';
  isCreateInterfaceSuccess?: boolean;
  isCreateIdentitiesSuccess?: boolean;
  getApiData?: any;
  editedUserSettings?: any;
  setEditedUserSettings?: React.Dispatch<React.SetStateAction<any>>;
  clusterList?: any;
  isInterfaceListQueryFetching?: boolean;
  isIdentitiesFetching?: boolean;
  selectedOption?: any;
  setIsButtonDisabled?: (v: boolean) => void;
  editing: Record<string, boolean>;
  setEditing: React.Dispatch<React.SetStateAction<Record<string, boolean>>>;
};

const InterfaceAndIdentitiesForm = forwardRef<any, InterfaceAndIdentitiesFormProps>(
  (
    {
      caller,
      isCreateInterfaceSuccess,
      isCreateIdentitiesSuccess,
      getApiData,
      editedUserSettings,
      setEditedUserSettings,
      clusterList,
      isInterfaceListQueryFetching,
      isIdentitiesFetching,
      selectedOption,
      setIsButtonDisabled,
      setEditing,
      editing,
    }: InterfaceAndIdentitiesFormProps,
    ref
  ) => {
    if (caller === 'interfaces') {
      return (
        <>
          <DeploymentSummary getApiData={getApiData} clusterList={clusterList} selectedOption={selectedOption} />
          <Accordion defaultIndex={[0]} allowMultiple>
            <AccordionItem>
              <AccordionButton>
                <Text width="100%">Node Interfaces</Text>
                <AccordionIcon />
              </AccordionButton>
              <AccordionPanel pb="4">
                <DynamicTable
                  ref={ref}
                  caller="interfaces"
                  getInterfaceData={getApiData}
                  editedUserSettings={editedUserSettings}
                  setEditedUserSettings={setEditedUserSettings}
                  isCreateInterfaceSuccess={isCreateInterfaceSuccess}
                  isInterfaceListQueryFetching={isInterfaceListQueryFetching}
                  selectedOption={selectedOption}
                  setIsButtonDisabled={setIsButtonDisabled}
                  setEditing={setEditing}
                  editing={editing}
                />
              </AccordionPanel>
            </AccordionItem>
          </Accordion>
        </>
      );
    } else {
      return (
        <>
          <DeploymentSummary getApiData={getApiData} clusterList={clusterList} selectedOption={selectedOption} />
          <Accordion defaultIndex={[0]} allowMultiple>
            <AccordionItem>
              <AccordionButton>
                <Text width="100%">Node Identities</Text>
                <AccordionIcon />
              </AccordionButton>
              <AccordionPanel pb="4">
                <DynamicTable
                  ref={ref}
                  caller="identities"
                  getIdentitiesData={getApiData}
                  editedUserSettings={editedUserSettings}
                  setEditedUserSettings={setEditedUserSettings}
                  isCreateIdentitiesSuccess={isCreateIdentitiesSuccess}
                  isIdentitiesFetching={isIdentitiesFetching}
                  selectedOption={selectedOption}
                  setIsButtonDisabled={setIsButtonDisabled}
                  setEditing={setEditing}
                  editing={editing}
                />
              </AccordionPanel>
            </AccordionItem>
          </Accordion>
        </>
      );
    }
  }
);

InterfaceAndIdentitiesForm.displayName = 'InterfaceAndIdentitiesForm';

export default InterfaceAndIdentitiesForm;
