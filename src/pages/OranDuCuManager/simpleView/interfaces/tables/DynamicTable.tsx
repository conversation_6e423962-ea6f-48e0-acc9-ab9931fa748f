import { EditIcon, InfoIcon, SmallCloseIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  IconButton,
  Input,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalFooter,
  ModalHeader,
  ModalOverlay,
  Popover,
  PopoverArrow,
  PopoverBody,
  PopoverCloseButton,
  PopoverContent,
  PopoverHeader,
  PopoverTrigger,
  Text,
  Table,
  Tbody,
  Td,
  Th,
  Thead,
  Tooltip,
  Tr,
  useColorModeValue,
  useDisclosure,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import _, { some } from 'lodash';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';
import EmptyDataErrorBoundary from '../../../../../components/errorComponents/EmptyDataErrorBoundary';
import FileUpload, {
  DeploymentFormData,
  generateInterfaceSchema,
} from '../../../../../components/fileUpload/FileUpload';
import Loader from '../../../../../components/loader/Loader';
import { AUTH_TOKEN_KEY, FileUploadTypes, READ_WRITE_ACCESS_ROLES } from '../../../../../data/constants';
import useLogin from '../../../../../hooks/useLogin';
import { InterfaceFieldData } from '../../../../../types/duCuManager.type';
import { ipv4Regex, ipv6Regex } from '../../../../../utils/helpers';
import { defaultInterfaceFields } from '../../../hooks/interface/useTransformInterfaceTableData';
import useCustomResource from '../../../hooks/services/useCustomResource';
import useSiteStatus from '../../../hooks/useSiteStatus';

export type InitialNodeType = {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: {
    fields: { name: string; label: string; value: string }[];
    node: string;
    name: string;
  };
};

export type InterfaceGroupName = 'du' | 'cu_cp' | 'cu_up' | 'cu_cp2' | 'cu_up2';

export interface IdentityField {
  name: string;
  default: string;
  user_setting?: string;
  target?: string;
}

export type InterfaceFieldDataRow = InterfaceFieldData;

export type DynamicTableHandle = {
  validateAllIdentities(): Promise<boolean>;
  getIdentityValues(): Record<string, string>;
  getInterfaceValues(): Record<string, any>;
};

export type EditedUserSettings =
  | {
      shared?: Record<string, IdentityField>;
      du?: Record<string, IdentityField>;
    }
  | Partial<Record<InterfaceGroupName, InterfaceFieldDataRow[]>>
  | Partial<DeploymentFormData>;

type DynamicTableProps = {
  caller: 'interfaces' | 'identities';
  getInterfaceData?: Partial<Record<InterfaceGroupName, InterfaceFieldDataRow[]>>;
  getIdentitiesData?: { shared: Record<string, IdentityField>; du: Record<string, IdentityField> };
  editedUserSettings?: EditedUserSettings;
  setEditedUserSettings?: React.Dispatch<React.SetStateAction<any>>;
  editing: Record<string, boolean>;
  setEditing: React.Dispatch<React.SetStateAction<Record<string, boolean>>>;
  isCreateInterfaceSuccess?: boolean;
  isCreateIdentitiesSuccess?: boolean;
  isInterfaceListQueryFetching?: boolean;
  isIdentitiesFetching?: boolean;
  selectedOption?: any;
  setIsButtonDisabled?: any;
};

const DynamicTable = forwardRef<DynamicTableHandle, DynamicTableProps>(
  (
    {
      caller,
      getInterfaceData,
      getIdentitiesData,
      editedUserSettings,
      setEditedUserSettings,
      editing,
      setEditing,
      isCreateInterfaceSuccess,
      isCreateIdentitiesSuccess,
      isInterfaceListQueryFetching,
      isIdentitiesFetching,
      selectedOption,
      setIsButtonDisabled,
    },
    ref
  ) => {
    const [originalValues, setOriginalValues] = useState<Record<string, string>>({});
    const { checkEnabledConsistency } = useSiteStatus();

    const { checkNmsDevAccess } = useLogin(AUTH_TOKEN_KEY);
    const checkRoleAccess = checkNmsDevAccess(READ_WRITE_ACCESS_ROLES);

    const maxCell = selectedOption?.deployment_params?.max_cells;

    const {
      isOpen: maxCellInterfaceIsOpen,
      onOpen: maxCellInterfaceOnOpen,
      onClose: maxCellInterfaceOnClose,
    } = useDisclosure();
    const [modalMessage, setModalMessage] = useState<string>('');

    const isReady =
      !!selectedOption?.deployment_name &&
      !!selectedOption?.du_site_name &&
      !!selectedOption?.cucp_site_name &&
      !!selectedOption?.cuup_site_name;

    // API Calls
    const { getDeploymentCustomResources } = useCustomResource();

    // Drives cluster table
    const {
      data: DeploymentCustomResourcesData,
      isLoading: isDeploymentCustomResourcesDataLoading,
      error: deploymentCustomResourcesDataError,
      isFetching: isDeploymentCustomResourcesDataFetching,
    } = getDeploymentCustomResources(selectedOption?.deployment_name ?? '', isReady);

    const flattenedData = React.useMemo(() => {
      if (DeploymentCustomResourcesData) {
        return [
          ...DeploymentCustomResourcesData.du,
          ...DeploymentCustomResourcesData.cuup,
          ...DeploymentCustomResourcesData.cucp,
          ...(DeploymentCustomResourcesData.cucp2 || []),
          ...(DeploymentCustomResourcesData.cuup2 || []),
        ];
      }
      return [];
    }, [DeploymentCustomResourcesData]);

    //NOTE: below i had to do a workaround for disabling the edit button if more than 2 crs are enabled, just for the time being until the backend is consistent.
    // In the future we will just use allEnabled.
    // allCrsEnabled.allEnabled = areMoreThanTwoEnabled; is a temp fix
    const { allEnabled, statuses, isSelectedEnabled } = checkEnabledConsistency(flattenedData);
    const allCrsEnabled = checkEnabledConsistency(flattenedData);
    const crEnabledCount = Object.values(statuses).filter((status) => status === true).length;
    const areMoreThanTwoEnabled = crEnabledCount > 2;
    allCrsEnabled.allEnabled = areMoreThanTwoEnabled;

    const isEditable = (header: string) => {
      return header === 'user_setting';
    };
    const borderColor = useColorModeValue('rgba(237, 242, 247, 0.9)', 'rgba(45, 55, 72, 0.9)');

    const getBgColor = (
      header: string,
      item: { name: string; default: string; user_setting?: string; target?: string },
      cellKey: string,
      editing: Record<string, boolean>
    ) => {
      const currentValue = item.user_setting;
      const hasUserSetting = currentValue !== null && currentValue !== undefined && currentValue !== '';

      if (editing[cellKey]) {
        return 'rgba(255, 165, 0, 0.5)'; // orange
      }

      if (header === 'user_setting') {
        if (hasUserSetting) {
          return 'rgba(56, 161, 105, 0.5)'; // green
        } else {
          return 'rgba(113, 128, 150, 0.5)'; // grey
        }
      }

      if (header === 'default') {
        if (!item.default) {
          return 'rgba(113, 128, 150, 0.5)';
        }

        if (hasUserSetting) {
          return 'rgba(113, 128, 150, 0.5)';
        }

        return 'rgba(56, 161, 105, 0.5)';
      }

      return '';
    };

    const handleEditClick = (group: string, key: string, header: string, index: number, field: any) => {
      const cellKey = `${group}-${key}-${header}-${index}`;
      setEditing({ ...editing, [cellKey]: true });
      setOriginalValues((prev) => ({ ...prev, [cellKey]: field[header] }));
    };

    const handleCloseClick = (group: string, key: string, header: string, index: number) => {
      const cellKey = `${group}-${key}-${header}-${index}`;
      setEditing({ ...editing, [cellKey]: false });
      interfaceSetValue(cellKey, originalValues[cellKey] || '');
      setEditedUserSettings?.((prev: any) => {
        const { [cellKey]: _discard, ...rest } = prev;
        return rest;
      });
    };

    const handleInputChange = (
      group: string,
      key: string,
      header: string,
      index: number,
      value: string,
      item?: any
    ) => {
      const cellKey = `${group}-${key}-${header}-${index.toString()}`;
      const updatedItem = {
        ...item,
        [header]: value,
      };
      setEditedUserSettings?.((prev: Record<string, any>) => ({
        ...prev,
        [cellKey]: updatedItem,
      }));
    };

    const handleInputBlur = async (
      group: string,
      key: string,
      header: string,
      index: number,
      value: string,
      item?: any
    ) => {
      const cellKey = `${group}-${key}-${header}-${index.toString()}`;
      setEditedUserSettings?.((prev: Record<string, any>) => ({
        ...prev,
        [cellKey]: {
          ...item,
          [header]: value,
        },
      }));
      let isValid = true;
      if (caller === 'identities') {
        isValid = await identityTrigger(cellKey);
      } else {
        isValid = await interfaceTrigger(cellKey);
      }
      if (!isValid) {
        console.log(`Validation failed for ${cellKey}`);
      }
    };

    const generateSchema = () => {
      const schemaShape: Record<string, any> = {};

      if (caller === 'interfaces' && getInterfaceData) {
        (Object.keys(getInterfaceData) as InterfaceGroupName[]).forEach((group) => {
          const rows = getInterfaceData[group];
          if (!Array.isArray(rows)) return;

          rows.forEach((field: InterfaceFieldDataRow, index: number) => {
            const cellKey = `${group}-user_setting-${field.name}-${index}`;

            if (field.name === 'FH-C subNetMask') {
              schemaShape[cellKey] = z.string().refine(
                (val) => {
                  const num = Number(val);
                  return !isNaN(num) && num >= 0 && num <= 32;
                },
                { message: 'SubNetMask must be a number between 0 and 32' }
              );
            } else {
              schemaShape[cellKey] = z
                .string()
                .refine((val) => ipv4Regex.test(val) || ipv6Regex.test(val), {
                  message: 'Invalid IP address format',
                })
                .optional();
            }
          });
        });
      }

      if (caller === 'identities' && getIdentitiesData) {
        // (1) Hard-code the six “shared” keys in the exact order:
        const sharedKeyOrder: Array<keyof typeof getIdentitiesData.shared> = [
          'gnodeb_id', // → index 0
          'mcc', // → index 1
          'mnc', // → index 2
          'gnodeb_id2', // → index 3
          'mcc2', // → index 4
          'mnc2', // → index 5
        ];

        sharedKeyOrder.forEach((fieldKey, rowIndex) => {
          const fieldObj = getIdentitiesData.shared[fieldKey];
          if (!fieldObj) return;

          const cellKey = `shared-${fieldKey}-user_setting-${rowIndex}` as const;

          if (fieldObj.name === 'MCC') {
            schemaShape[cellKey] = z
              .string()
              .min(3, { message: 'MCC must be a 3-digit number' })
              .max(3, { message: 'MCC must be a 3-digit number' })
              .refine((v) => !isNaN(Number(v)), { message: 'MCC must be numeric' })
              .optional();
          } else if (fieldObj.name === 'MNC') {
            schemaShape[cellKey] = z
              .string()
              .min(2, { message: 'MNC must be a 2-digit number' })
              .max(2, { message: 'MNC must be a 2-digit number' })
              .refine((v) => !isNaN(Number(v)), { message: 'MNC must be numeric' })
              .optional();
          }
        });

        const duKeyOrder: Array<keyof typeof getIdentitiesData.du> = [
          'tracking_area_code', // → index 0
          'ran_area_code', // → index 1
          'physical_cell_id', // → index 2
          'tracking_area_code2', // → index 3
          'ran_area_code2', // → index 4
          'physical_cell_id2', // → index 5
        ];

        duKeyOrder.forEach((fieldKey, rowIndex) => {
          const fieldObj = getIdentitiesData.du[fieldKey];
          if (!fieldObj) return;

          const cellKey = `du-${fieldKey}-user_setting-${rowIndex}` as const;

          if (fieldObj.name === 'Tracking Area Code (TAC)') {
            schemaShape[cellKey] = z
              .string()
              .min(1, { message: 'Tracking Area Code (TAC) is required' })
              .refine((v) => /^[0-9]+$/.test(v), { message: 'TAC must be numeric' })
              .optional();
          } else {
            schemaShape[cellKey] = z
              .string()
              .refine((v) => /^[a-zA-Z0-9]+$/.test(v), { message: 'Invalid value, must be alphanumeric' })
              .optional();
          }
        });
      }

      return z.object(schemaShape);
    };

    const defaultSchema = z.object({});
    const schema = generateSchema() || defaultSchema;

    const identityForm = useForm<Record<string, string>>({
      resolver: zodResolver(generateSchema()),
      mode: 'onBlur',
      reValidateMode: 'onBlur',
    });
    const {
      control: identityControl,
      trigger: identityTrigger,
      getValues: getIdentityValues,
      setValue: setIdentityValues,
      formState: { errors: identityErrors, isValid: identityIsValid },
      reset: identityReset,
    } = identityForm;

    const interfaceForm = useForm<InterfaceFieldData>({
      resolver: zodResolver(schema),
      mode: 'onBlur',
      reValidateMode: 'onChange',
    });
    const {
      handleSubmit: interfaceHandleSubmit,
      reset: interfaceReset,
      control: interfaceControl,
      setValue: interfaceSetValue,
      trigger: interfaceTrigger,
      formState: { errors: interfaceErrors, isValid: interfaceIsValid },
      getValues: interfaceGetValues,
    } = interfaceForm;

    const getErrorMessage = (error: any): string => {
      if (typeof error?.message === 'string') {
        return error.message;
      }
      return 'Invalid value';
    };

    useEffect(() => {
      identityReset();
    }, [getIdentitiesData, identityReset]);

    useEffect(() => {
      if (isCreateInterfaceSuccess) {
        setEditing({});
        if (setEditedUserSettings) {
          setEditedUserSettings({});
        }
      }
    }, [isCreateInterfaceSuccess]);

    useEffect(() => {
      if (isCreateIdentitiesSuccess) {
        setEditing({});
        if (setEditedUserSettings) {
          setEditedUserSettings({});
        }
      }
    }, [isCreateIdentitiesSuccess]);

    useEffect(() => {
      const anyCellEditing = _.some(editing, (v) => v === true);
      setIsButtonDisabled(!anyCellEditing);
    }, [editing]);

    useImperativeHandle(ref, () => ({
      validateAllIdentities: async () => {
        const result = await identityTrigger();
        return result;
      },
      getIdentityValues: () => {
        const vals = getIdentityValues();
        return vals;
      },
      getInterfaceValues: () => {
        const vals = interfaceGetValues();
        return interfaceForm.getValues();
      },
    }));

    if (isInterfaceListQueryFetching || isIdentitiesFetching) return <Loader />;

    if (
      DeploymentCustomResourcesData &&
      _.every(
        ['du', 'cucp', 'cuup'],
        (key) => Array.isArray(DeploymentCustomResourcesData[key]) && _.isEmpty(DeploymentCustomResourcesData[key])
      )
    ) {
      return (
        <EmptyDataErrorBoundary
          data={DeploymentCustomResourcesData}
          message={
            new Error(
              `All keys (du, cucp, cuup) are empty arrays. Expected at least one key to contain data. Received: ${JSON.stringify(
                DeploymentCustomResourcesData,
                null,
                2
              )}`
            )
          }
        />
      );
    }

    const renderInterfaceTable = (
      data: InterfaceFieldData[],
      title: string,
      group: 'du' | 'cu_cp' | 'cu_up' | 'cu_cp2' | 'cu_up2'
    ) => {
      const isMoranDeployment =
        selectedOption?.deployment_params?.max_cells === 2 ||
        selectedOption?.cu2?.cucp_site_name2 ||
        selectedOption?.cu2?.cuup_site_name2 ||
        title.includes('CUCP2') ||
        title.includes('CUUP2');

      if (defaultInterfaceFields[group as keyof typeof defaultInterfaceFields]) {
        const defaults = defaultInterfaceFields[group as keyof typeof defaultInterfaceFields];
        const existingFieldNames = new Set(data.map((field) => field.name));

        defaults.forEach((defaultField) => {
          if (!existingFieldNames.has(defaultField.name)) {
            data.push({
              ...defaultField,
              user_setting: null,
            });
          }
        });

        if (isMoranDeployment && group === 'du' && defaultInterfaceFields.du_moran) {
          const moranDefaults = defaultInterfaceFields.du_moran;
          const existingMoranFieldNames = new Set(data.map((field) => field.name));

          moranDefaults.forEach((defaultField) => {
            if (!existingMoranFieldNames.has(defaultField.name)) {
              data.push({
                ...defaultField,
                user_setting: null,
              });
            }
          });
        }
      }

      const validFieldsByGroup: Record<'du' | 'cu_cp' | 'cu_up' | 'cu_cp2' | 'cu_up2', string[]> = {
        du: [
          'FH-C localIpAddress',
          'FH-C subNetMask',
          'FH-C gatewayAddress',
          'FH-U localIpAddress',
          'FH-U gatewayAddress',
          'F1-C remoteAddress (Cell 1)',
          'F1-U localIpAddress (Cell 1)',
          'F1-U gatewayAddress (Cell 1)',
          'F1-C remoteAddress (Cell 2)',
          'F1-U localIpAddress (Cell 2)',
          'F1-U gatewayAddress (Cell 2)',
        ],
        cu_cp: ['NgC remoteAddress', 'NgC gatewayAddress'],
        cu_up: [
          'NgU localIpAddress',
          'NgU gatewayAddress',
          'F1-U localIpAddress',
          'F1-U gatewayAddress',
          'E1 localIpAddress',
          'E1 remoteAddress',
        ],
        cu_cp2: ['NgC remoteAddress', 'NgC gatewayAddress'],
        cu_up2: [
          'NgU localIpAddress',
          'NgU gatewayAddress',
          'F1-U localIpAddress',
          'F1-U gatewayAddress',
          'E1 localIpAddress',
          'E1 remoteAddress',
        ],
      };

      return (
        <Box mb="4">
          <Box as="h2" fontSize="lg" mb="2">
            {title}
          </Box>
          <Table>
            <Thead>
              <Tr style={{ border: `0.1em solid ${borderColor}` }}>
                {['Name', 'Default', 'User Setting', 'Target'].map((header) => (
                  <Th key={header}>{header}</Th>
                ))}
              </Tr>
            </Thead>
            <Tbody>
              {data
                .filter((field) => {
                  const validFields = validFieldsByGroup[group] || [];
                  return validFields.includes(field.name);
                })
                .map((field: any, rowIndex: number) => {
                  if (group === 'cu_up2' || field.moran === 'c2') {
                    if (field.name.includes('F1-C') && !field.name.includes('(Cell 2)')) {
                      field.name += ' (Cell 2)';
                    }
                  } else if (group === 'du') {
                    if (field.name.includes('F1-C') && !field.name.includes('(Cell 1)')) {
                      field.name += ' (Cell 1)';
                    } else if (field.name.includes('F1-U') && !field.name.includes('(Cell 1)')) {
                      field.name += ' (Cell 1)';
                    }
                  }

                  return (
                    <Tr key={rowIndex}>
                      {['name', 'default', 'user_setting', 'target'].map((header: string) => {
                        const cellKey = `${group}-${header}-${field.name}-${rowIndex}`;
                        const editable = isEditable(header);

                        const bgColor = getBgColor(header, field, cellKey, editing);

                        const fieldWidth = header === 'user_setting' ? '30%' : '20%';

                        return (
                          <Td
                            key={cellKey}
                            position="relative"
                            width={fieldWidth}
                            p="4"
                            style={{ backgroundColor: bgColor, border: `0.1em solid ${borderColor}` }}
                          >
                            {header === 'name' ? (
                              <Tooltip
                                label={`${field?.name} - ${group} > ${field?.target}`}
                                aria-label="Name Tooltip"
                                placement="top"
                              >
                                <span>{field[header] || ''}</span>
                              </Tooltip>
                            ) : editing[cellKey] ? (
                              <Controller
                                name={`${group}-${header}-${field.name}-${rowIndex}`}
                                control={interfaceControl}
                                defaultValue={field[header]}
                                render={({ field: controllerField }) => {
                                  const errorKey =
                                    `${group}-${header}-${field.name}-${rowIndex}` as keyof InterfaceFieldData;
                                  return (
                                    <FormControl isInvalid={!!interfaceErrors[errorKey]}>
                                      <Box display="flex" alignItems="baseline">
                                        <FormLabel htmlFor={`${group}-${header}-${field.name}-${rowIndex}`} width="25%">
                                          {field?.user_setting ? `${field?.user_setting}` : `${field?.default}`}
                                        </FormLabel>
                                        <Input
                                          id={`${group}-${header}-${field.name}-${rowIndex}`}
                                          {...controllerField}
                                          style={{
                                            pointerEvents: 'all',
                                          }}
                                          type={field?.name === 'FH-C subNetMask' ? 'number' : 'text'}
                                          backgroundColor="white"
                                          color="black"
                                          width="60%"
                                          mr="10%"
                                          value={controllerField.value || ''}
                                          onChange={(e) => {
                                            controllerField.onChange(e.target.value);

                                            handleInputChange(
                                              group,
                                              field.name,
                                              'user_setting',
                                              rowIndex,
                                              e.target.value,
                                              field
                                            );
                                          }}
                                          onBlur={(e) => {
                                            controllerField.onBlur();
                                            handleInputBlur(
                                              group,
                                              field.name,
                                              'user_setting',
                                              rowIndex,
                                              controllerField.value || '',
                                              field
                                            );
                                          }}
                                        />
                                      </Box>
                                      {interfaceErrors[errorKey] && (
                                        <FormErrorMessage m="0">
                                          {(interfaceErrors[errorKey]?.message as React.ReactNode) || 'Invalid value'}
                                        </FormErrorMessage>
                                      )}
                                    </FormControl>
                                  );
                                }}
                              />
                            ) : (
                              <span>{field[header] || ''}</span>
                            )}
                            {checkRoleAccess && editable && (
                              <Popover>
                                <PopoverTrigger>
                                  <IconButton
                                    icon={
                                      !allCrsEnabled.allEnabled ? (
                                        editing[cellKey] ? (
                                          <SmallCloseIcon />
                                        ) : (
                                          <EditIcon data-testid="edit-icon" />
                                        )
                                      ) : (
                                        <InfoIcon data-testid="info-icon" />
                                      )
                                    }
                                    size="md"
                                    onClick={() => {
                                      if (!allCrsEnabled.allEnabled) {
                                        editing[cellKey]
                                          ? handleCloseClick(group, header, field.name, rowIndex)
                                          : handleEditClick(group, header, field.name, rowIndex, field);
                                      }
                                    }}
                                    aria-label={
                                      !allCrsEnabled.allEnabled
                                        ? editing[cellKey]
                                          ? 'Close field'
                                          : 'Edit field'
                                        : 'Info'
                                    }
                                    position="absolute"
                                    top="50%"
                                    right="4px"
                                    transform="translateY(-50%)"
                                  />
                                </PopoverTrigger>
                                {allCrsEnabled.allEnabled && (
                                  <PopoverContent>
                                    <PopoverArrow />
                                    <PopoverCloseButton />
                                    <PopoverHeader>Edit blocked</PopoverHeader>
                                    <PopoverBody>
                                      Please deactivate pod/custom resource <br />
                                      to edit this field
                                    </PopoverBody>
                                  </PopoverContent>
                                )}
                              </Popover>
                            )}
                          </Td>
                        );
                      })}
                    </Tr>
                  );
                })}
            </Tbody>
          </Table>
        </Box>
      );
    };

    const renderIdentitiesTable = (
      data: Record<string, { name: string; default: string; user_setting?: string; target?: string }>,
      title: string,
      group: string,
      editing: Record<string, boolean>,
      setEditing: (val: Record<string, boolean>) => void,
      allCrsEnabled: { allEnabled: boolean }
    ) => {
      if (!getIdentitiesData) {
        <Loader />;
      }

      return (
        <Box mb="4">
          <Box as="h2" fontSize="lg" mb="2">
            {title}
          </Box>
          <Table>
            <Thead>
              <Tr>
                {['Name', 'Default', 'User Setting', 'Target'].map((header) => {
                  const w = header === 'User Setting' ? '30%' : '20%';
                  return (
                    <Th key={header} width={w} p="4">
                      {header}
                    </Th>
                  );
                })}
              </Tr>
            </Thead>

            <Tbody>
              {Object.entries(data)
                .filter(([key]) => key !== 'node_type' && key !== 'gnodeb_du_name')
                .map(([key, value], rowIndex) => {
                  const originalValue =
                    getIdentitiesData?.[group as 'shared' | 'du']?.[key]?.user_setting?.toString() ?? '';
                  const editedValue = value.user_setting ?? '';

                  const cellKey = `${group}-${key}-user_setting-${rowIndex}`;

                  const hasUserSetting = originalValue !== '';

                  return (
                    <Tr key={rowIndex}>
                      <Td width="20%" p="4">
                        {key}
                      </Td>
                      <Td
                        width="20%"
                        p="4"
                        style={{
                          backgroundColor: getBgColor('default', value, `${group}-${key}-default-${rowIndex}`, editing),
                        }}
                      >
                        <span>{value.default ?? ''}</span>
                      </Td>

                      <Td
                        position="relative"
                        width="30%"
                        p="4"
                        style={{
                          backgroundColor: getBgColor('user_setting', value, cellKey, editing),
                        }}
                      >
                        {!editing[cellKey] && <span>{originalValue}</span>}

                        {editing[cellKey] && (
                          <Controller
                            name={cellKey}
                            control={identityControl}
                            defaultValue={editedValue}
                            render={({ field: controllerField }) => {
                              const errorKey = cellKey;
                              return (
                                <FormControl isInvalid={!!identityErrors[errorKey]}>
                                  <Box display="flex" alignItems="baseline">
                                    {hasUserSetting && <FormLabel width="25%">{originalValue}</FormLabel>}
                                    <Input
                                      id={cellKey}
                                      {...controllerField}
                                      type="text"
                                      backgroundColor="white"
                                      color="black"
                                      width="60%"
                                      value={controllerField.value || ''}
                                      onChange={(e) => {
                                        const newValue = e.target.value;
                                        controllerField.onChange(newValue);
                                        handleInputChange(group, key, 'user_setting', rowIndex, newValue, value);
                                      }}
                                      onBlur={async () => {
                                        controllerField.onBlur();
                                        await identityTrigger(errorKey);
                                      }}
                                    />
                                  </Box>
                                  {identityErrors[errorKey] && (
                                    <FormErrorMessage m="0">
                                      {identityErrors[errorKey]?.message as React.ReactNode}
                                    </FormErrorMessage>
                                  )}
                                </FormControl>
                              );
                            }}
                          />
                        )}

                        {checkRoleAccess && (
                          <Popover>
                            <PopoverTrigger>
                              <IconButton
                                position="absolute"
                                top="50%"
                                right="8px"
                                transform="translateY(-50%)"
                                size="sm"
                                aria-label={editing[cellKey] ? 'Close field' : 'Edit field'}
                                icon={
                                  !allCrsEnabled.allEnabled ? (
                                    editing[cellKey] ? (
                                      <SmallCloseIcon />
                                    ) : (
                                      <EditIcon />
                                    )
                                  ) : (
                                    <InfoIcon />
                                  )
                                }
                                onClick={() => {
                                  if (!allCrsEnabled.allEnabled) {
                                    setEditing({
                                      ...editing,
                                      [cellKey]: !editing[cellKey],
                                    });
                                  }
                                }}
                              />
                            </PopoverTrigger>
                            {allCrsEnabled.allEnabled && (
                              <PopoverContent>
                                <PopoverArrow />
                                <PopoverCloseButton />
                                <PopoverHeader>Edit blocked</PopoverHeader>
                                <PopoverBody>
                                  Please deactivate pod/custom resource <br />
                                  to edit this field
                                </PopoverBody>
                              </PopoverContent>
                            )}
                          </Popover>
                        )}
                      </Td>

                      <Td width="20%" p="4">
                        {value.target ?? ''}
                      </Td>
                    </Tr>
                  );
                })}
            </Tbody>
          </Table>
        </Box>
      );
    };

    return (
      <Box>
        {caller === 'interfaces' ? (
          <>
            {/* DU group */}
            {getInterfaceData?.du && renderInterfaceTable(getInterfaceData.du, 'DU Interfaces', 'du')}

            {/* CUCP group */}
            {getInterfaceData?.cu_cp && renderInterfaceTable(getInterfaceData.cu_cp, 'CUCP Interfaces', 'cu_cp')}

            {/* CUCP2 group */}
            {(() => {
              const ifaceSettings = editedUserSettings as Partial<Record<InterfaceGroupName, InterfaceFieldDataRow[]>>;
              const cuCp2Rows = ifaceSettings?.cu_cp2 ?? getInterfaceData?.cu_cp2;
              return cuCp2Rows ? renderInterfaceTable(cuCp2Rows, 'CUCP2 Interfaces', 'cu_cp2') : null;
            })()}

            {/* CUUP2 group */}
            {(() => {
              const ifaceSettings = editedUserSettings as
                | Partial<Record<InterfaceGroupName, InterfaceFieldDataRow[]>>
                | undefined;

              const cuUpRows = ifaceSettings?.cu_up ?? getInterfaceData?.cu_up;
              if (cuUpRows) {
                return renderInterfaceTable(cuUpRows, 'CUUP Interfaces', 'cu_up');
              }
              return null;
            })()}

            {/* CUUP2 group */}
            {(() => {
              const ifaceSettings = editedUserSettings as
                | Partial<Record<InterfaceGroupName, InterfaceFieldDataRow[]>>
                | undefined;
              const cuUp2Rows = ifaceSettings?.cu_up2 ?? getInterfaceData?.cu_up2;
              if (cuUp2Rows) {
                return renderInterfaceTable(cuUp2Rows, 'CUUP2 Interfaces', 'cu_up2');
              }

              return null;
            })()}

            <Flex justifyContent="space-between" alignItems="center" width="100%" mt="12" mb="8">
              <FileUpload
                formMethods={interfaceForm}
                caller="simpleViewInterfaces"
                uploadType={FileUploadTypes.import}
                text="Import Interfaces"
                setEditing={setEditing}
                editing={editing}
                setEditedUserSettings={setEditedUserSettings}
                selectedOption={selectedOption}
                getInterfaceData={getInterfaceData}
                maxCell={maxCell}
                onImportAttempt={async (importedData: any) => {
                  const hasSecondCell = !!importedData.du?.f1_c2 || !!importedData.cu_cp2 || !!importedData.cu_up2;

                  if (maxCell === 1 && hasSecondCell) {
                    setModalMessage(
                      'Cannot import: this interface form is single‐cell (maxCell=1) but JSON is two‐cell (max_cells=2).'
                    );
                    maxCellInterfaceOnOpen();
                    return false;
                  }

                  if (maxCell === 1 && !hasSecondCell) {
                    const getDeepString = (obj: any, path: string[]): string => {
                      let cursor = obj;
                      for (const seg of path) {
                        if (cursor == null || typeof cursor !== 'object') {
                          return '';
                        }
                        cursor = cursor[seg];
                      }
                      return cursor != null ? String(cursor) : '';
                    };

                    type InterfaceFieldDataRow = {
                      name: string;
                      default: string | number;
                      user_setting: string;
                      target: string;
                    };

                    const nested: Record<'du' | 'cu_cp' | 'cu_up', InterfaceFieldDataRow[]> = {
                      du: [],
                      cu_cp: [],
                      cu_up: [],
                    };

                    const baseDuFields = defaultInterfaceFields.du;
                    baseDuFields.forEach((defRow) => {
                      const name = defRow.name;
                      let userVal = '';

                      switch (name) {
                        case 'FH-C localIpAddress':
                          userVal = getDeepString(importedData.du, ['fh_c', 'local_ip_address']);
                          break;
                        case 'FH-C subNetMask':
                          userVal = getDeepString(importedData.du, ['fh_c', 'subnet_mask']);
                          break;
                        case 'FH-C gatewayAddress':
                          userVal = getDeepString(importedData.du, ['fh_c', 'gateway_address']);
                          break;
                        case 'FH-U localIpAddress':
                          userVal = getDeepString(importedData.du, ['fh_u', 'local_ip_address']);
                          break;
                        case 'FH-U gatewayAddress':
                          userVal = getDeepString(importedData.du, ['fh_u', 'gateway_address']);
                          break;
                        case 'F1-C remoteAddress (Cell 1)':
                          userVal = getDeepString(importedData.du, ['f1_c', 'remote_address']);
                          break;
                        case 'F1-U localIpAddress (Cell 1)':
                          userVal = getDeepString(importedData.du, ['f1_u', 'local_ip_address']);
                          break;
                        case 'F1-U gatewayAddress (Cell 1)':
                          userVal = getDeepString(importedData.du, ['f1_u', 'gateway_address']);
                          break;
                        default:
                          userVal = '';
                      }

                      nested.du.push({
                        name: defRow.name,
                        default: defRow.default,
                        user_setting: userVal,
                        target: defRow.target,
                      });
                    });

                    defaultInterfaceFields.cu_cp.forEach((defRow) => {
                      const name = defRow.name;
                      let userVal = '';

                      switch (name) {
                        case 'NgC remoteAddress':
                          userVal = getDeepString(importedData.cu_cp, ['ng_c', 'remote_address']);
                          break;
                        case 'NgC gatewayAddress':
                          userVal = getDeepString(importedData.cu_cp, ['ng_c', 'gateway_address']);
                          break;
                        default:
                          userVal = '';
                      }

                      nested.cu_cp.push({
                        name: defRow.name,
                        default: defRow.default,
                        user_setting: userVal,
                        target: defRow.target,
                      });
                    });

                    defaultInterfaceFields.cu_up.forEach((defRow) => {
                      const name = defRow.name;
                      let userVal = '';

                      if (name === 'NgU localIpAddress') {
                        userVal = getDeepString(importedData.cu_up, ['ng_u', 'local_ip_address']);
                      } else if (name === 'NgU gatewayAddress') {
                        userVal = getDeepString(importedData.cu_up, ['ng_u', 'gateway_address']);
                      } else {
                        return;
                      }

                      nested.cu_up.push({
                        name: defRow.name,
                        default: defRow.default,
                        user_setting: userVal,
                        target: defRow.target,
                      });
                    });

                    defaultInterfaceFields.cu_up.forEach((defRow) => {
                      const name = defRow.name;
                      let userVal = '';

                      if (name === 'E1 localIpAddress') {
                        userVal = getDeepString(importedData.cu_up, ['e1', 'local_ip_address']);
                      } else if (name === 'E1 remoteAddress') {
                        userVal = getDeepString(importedData.cu_up, ['e1', 'remote_address']);
                      } else {
                        return;
                      }

                      nested.cu_up.push({
                        name: defRow.name,
                        default: defRow.default,
                        user_setting: userVal,
                        target: defRow.target,
                      });
                    });

                    defaultInterfaceFields.cu_up.forEach((defRow) => {
                      const name = defRow.name;
                      let userVal = '';

                      if (name === 'F1-U localIpAddress') {
                        userVal = getDeepString(importedData.cu_up, ['f1_u', 'local_ip_address']);
                      } else if (name === 'F1-U gatewayAddress') {
                        userVal = getDeepString(importedData.cu_up, ['f1_u', 'gateway_address']);
                      } else {
                        return;
                      }

                      nested.cu_up.push({
                        name: defRow.name,
                        default: defRow.default,
                        user_setting: userVal,
                        target: defRow.target,
                      });
                    });

                    const flatMapForRHF: Record<string, string> = {};

                    nested.du.forEach((row, idx) => {
                      const cellKey = `du-user_setting-${row.name}-${idx}`;
                      flatMapForRHF[cellKey] = String(row.user_setting ?? '');
                    });

                    nested.cu_cp.forEach((row, idx) => {
                      const cellKey = `cu_cp-user_setting-${row.name}-${idx}`;
                      flatMapForRHF[cellKey] = String(row.user_setting ?? '');
                    });

                    nested.cu_up.forEach((row, idx) => {
                      const cellKey = `cu_up-user_setting-${row.name}-${idx}`;
                      flatMapForRHF[cellKey] = String(row.user_setting ?? '');
                    });

                    flatMapForRHF['deployment_name'] = importedData.deployment_name || '';

                    interfaceForm.reset(flatMapForRHF);

                    setEditedUserSettings?.({
                      du: nested.du,
                      cu_cp: nested.cu_cp,
                      cu_up: nested.cu_up,
                    });

                    const initialEditingState: Record<string, boolean> = {};
                    (Object.entries(nested) as Array<[string, InterfaceFieldDataRow[]]>).forEach(([group, rows]) => {
                      rows.forEach((row, idx) => {
                        const cellKey = `${group}-user_setting-${row.name}-${idx}`;
                        initialEditingState[cellKey] = true;
                      });
                    });

                    setEditing(initialEditingState);

                    await new Promise(requestAnimationFrame);
                    interfaceForm.control._options.resolver = zodResolver(generateInterfaceSchema(nested as any));

                    await interfaceForm.trigger();

                    return true;
                  }

                  if (maxCell === 2 && !hasSecondCell) {
                    const getDeepString = (obj: any, path: string[]): string => {
                      let cursor = obj;
                      for (const seg of path) {
                        if (cursor == null || typeof cursor !== 'object') return '';
                        cursor = cursor[seg];
                      }
                      return cursor != null ? String(cursor) : '';
                    };

                    const safeImportFlat: Record<string, string> = {};

                    defaultInterfaceFields.du.forEach((defRow, idx) => {
                      const name = defRow.name;
                      let userVal = '';
                      switch (name) {
                        case 'FH-C localIpAddress':
                          userVal = getDeepString(importedData.du, ['fh_c', 'local_ip_address']);
                          break;
                        case 'FH-C subNetMask':
                          userVal = getDeepString(importedData.du, ['fh_c', 'subnet_mask']);
                          break;
                        case 'FH-C gatewayAddress':
                          userVal = getDeepString(importedData.du, ['fh_c', 'gateway_address']);
                          break;
                        case 'FH-U localIpAddress':
                          userVal = getDeepString(importedData.du, ['fh_u', 'local_ip_address']);
                          break;
                        case 'FH-U gatewayAddress':
                          userVal = getDeepString(importedData.du, ['fh_u', 'gateway_address']);
                          break;
                        case 'F1-C remoteAddress (Cell 1)':
                          userVal = getDeepString(importedData.du, ['f1_c', 'remote_address']);
                          break;
                        case 'F1-U localIpAddress (Cell 1)':
                          userVal = getDeepString(importedData.du, ['f1_u', 'local_ip_address']);
                          break;
                        case 'F1-U gatewayAddress (Cell 1)':
                          userVal = getDeepString(importedData.du, ['f1_u', 'gateway_address']);
                          break;
                        default:
                          userVal = '';
                      }
                      safeImportFlat[`du-user_setting-${name}-${idx}`] = userVal;
                    });

                    defaultInterfaceFields.cu_cp.forEach((defRow, idx) => {
                      const name = defRow.name;
                      let userVal = '';
                      switch (name) {
                        case 'NgC remoteAddress':
                          userVal = getDeepString(importedData.cu_cp, ['ng_c', 'remote_address']);
                          break;
                        case 'NgC gatewayAddress':
                          userVal = getDeepString(importedData.cu_cp, ['ng_c', 'gateway_address']);
                          break;
                        default:
                          userVal = '';
                      }
                      safeImportFlat[`cu_cp-user_setting-${name}-${idx}`] = userVal;
                    });

                    defaultInterfaceFields.cu_up.forEach((defRow, idx) => {
                      const name = defRow.name;
                      let userVal = '';
                      if (name === 'NgU localIpAddress') {
                        userVal = getDeepString(importedData.cu_up, ['ng_u', 'local_ip_address']);
                      } else if (name === 'NgU gatewayAddress') {
                        userVal = getDeepString(importedData.cu_up, ['ng_u', 'gateway_address']);
                      } else if (name === 'F1-U localIpAddress') {
                        userVal = getDeepString(importedData.cu_up, ['f1_u', 'local_ip_address']);
                      } else if (name === 'F1-U gatewayAddress') {
                        userVal = getDeepString(importedData.cu_up, ['f1_u', 'gateway_address']);
                      } else if (name === 'E1 localIpAddress') {
                        userVal = getDeepString(importedData.cu_up, ['e1', 'local_ip_address']);
                      } else if (name === 'E1 remoteAddress') {
                        userVal = getDeepString(importedData.cu_up, ['e1', 'remote_address']);
                      } else {
                        return;
                      }
                      safeImportFlat[`cu_up-user_setting-${name}-${idx}`] = userVal;
                    });

                    safeImportFlat['deployment_name'] = importedData.deployment_name || '';

                    interfaceForm.reset(safeImportFlat);
                    return true;
                  }

                  if (maxCell === 2 && hasSecondCell) {
                    const getDeepString = (obj: any, path: string[]): string => {
                      let cursor = obj;
                      for (const seg of path) {
                        if (cursor == null || typeof cursor !== 'object') return '';
                        cursor = cursor[seg];
                      }
                      return cursor != null ? String(cursor) : '';
                    };

                    const safeImportFlat: Record<string, string> = {};

                    let duFieldsToUse = defaultInterfaceFields.du;
                    if (importedData.du?.f1_c2 && importedData.du?.f1_u2) {
                      duFieldsToUse = [...defaultInterfaceFields.du, ...(defaultInterfaceFields.du_moran || [])];
                    }

                    const nestedDu: InterfaceFieldDataRow[] = [];
                    duFieldsToUse.forEach((defRow, idx) => {
                      const name = defRow.name;
                      let userVal = '';

                      switch (name) {
                        case 'FH-C localIpAddress':
                          userVal = getDeepString(importedData.du, ['fh_c', 'local_ip_address']);
                          break;
                        case 'FH-C subNetMask':
                          userVal = getDeepString(importedData.du, ['fh_c', 'subnet_mask']);
                          break;
                        case 'FH-C gatewayAddress':
                          userVal = getDeepString(importedData.du, ['fh_c', 'gateway_address']);
                          break;
                        case 'FH-U localIpAddress':
                          userVal = getDeepString(importedData.du, ['fh_u', 'local_ip_address']);
                          break;
                        case 'FH-U gatewayAddress':
                          userVal = getDeepString(importedData.du, ['fh_u', 'gateway_address']);
                          break;
                        case 'F1-C remoteAddress (Cell 1)':
                          userVal = getDeepString(importedData.du, ['f1_c', 'remote_address']);
                          break;
                        case 'F1-U localIpAddress (Cell 1)':
                          userVal = getDeepString(importedData.du, ['f1_u', 'local_ip_address']);
                          break;
                        case 'F1-U gatewayAddress (Cell 1)':
                          userVal = getDeepString(importedData.du, ['f1_u', 'gateway_address']);
                          break;

                        case 'F1-C remoteAddress (Cell 2)':
                          userVal = getDeepString(importedData.du, ['f1_c2', 'remote_address']);
                          break;
                        case 'F1-U localIpAddress (Cell 2)':
                          userVal = getDeepString(importedData.du, ['f1_u2', 'local_ip_address']);
                          break;
                        case 'F1-U gatewayAddress (Cell 2)':
                          userVal = getDeepString(importedData.du, ['f1_u2', 'gateway_address']);
                          break;

                        default:
                          userVal = '';
                      }

                      nestedDu.push({
                        name: defRow.name,
                        default: defRow.default,
                        user_setting: userVal,
                        target: defRow.target,
                      });

                      safeImportFlat[`du-user_setting-${defRow.name}-${idx}`] = String(userVal);
                    });

                    const nestedCuCp: InterfaceFieldDataRow[] = [];
                    defaultInterfaceFields.cu_cp.forEach((defRow, idx) => {
                      let userVal = '';
                      switch (defRow.name) {
                        case 'NgC remoteAddress':
                          userVal = getDeepString(importedData.cu_cp, ['ng_c', 'remote_address']);
                          break;
                        case 'NgC gatewayAddress':
                          userVal = getDeepString(importedData.cu_cp, ['ng_c', 'gateway_address']);
                          break;
                      }
                      nestedCuCp.push({ ...defRow, user_setting: userVal });
                      safeImportFlat[`cu_cp-user_setting-${defRow.name}-${idx}`] = userVal;
                    });

                    const nestedCuCp2: InterfaceFieldDataRow[] = [];
                    defaultInterfaceFields.cu_cp2.forEach((defRow, idx) => {
                      let userVal = '';
                      switch (defRow.name) {
                        case 'NgC remoteAddress':
                          userVal = getDeepString(importedData.cu_cp2, ['ng_c', 'remote_address']);
                          break;
                        case 'NgC gatewayAddress':
                          userVal = getDeepString(importedData.cu_cp2, ['ng_c', 'gateway_address']);
                          break;
                      }
                      nestedCuCp2.push({ ...defRow, user_setting: userVal });
                      safeImportFlat[`cu_cp2-user_setting-${defRow.name}-${idx}`] = userVal;
                    });

                    const nestedCuUp: InterfaceFieldDataRow[] = [];
                    const nestedCuUp2: InterfaceFieldDataRow[] = [];
                    defaultInterfaceFields.cu_up!.forEach((defRow, idx) => {
                      let userVal = '';
                      switch (defRow.name) {
                        case 'NgU localIpAddress':
                          userVal = getDeepString(importedData.cu_up, ['ng_u', 'local_ip_address']);
                          break;
                        case 'NgU gatewayAddress':
                          userVal = getDeepString(importedData.cu_up, ['ng_u', 'gateway_address']);
                          break;
                        case 'F1-U localIpAddress':
                          userVal = getDeepString(importedData.cu_up, ['f1_u', 'local_ip_address']);
                          break;
                        case 'F1-U gatewayAddress':
                          userVal = getDeepString(importedData.cu_up, ['f1_u', 'gateway_address']);
                          break;
                        case 'E1 localIpAddress':
                          userVal = getDeepString(importedData.cu_up, ['e1', 'local_ip_address']);
                          break;
                        case 'E1 remoteAddress':
                          userVal = getDeepString(importedData.cu_up, ['e1', 'remote_address']);
                          break;
                      }
                      nestedCuUp.push({
                        name: defRow.name,
                        default: defRow.default,
                        user_setting: userVal,
                        target: defRow.target,
                      });
                      safeImportFlat[`cu_up-user_setting-${defRow.name}-${idx}`] = String(userVal);
                    });
                    (defaultInterfaceFields.cu_up2 || []).forEach((defRow, idx) => {
                      let userVal = '';
                      switch (defRow.name) {
                        case 'NgU localIpAddress':
                          userVal = getDeepString(importedData.cu_up2, ['ng_u', 'local_ip_address']);
                          break;
                        case 'NgU gatewayAddress':
                          userVal = getDeepString(importedData.cu_up2, ['ng_u', 'gateway_address']);
                          break;
                        case 'F1-U localIpAddress':
                          userVal = getDeepString(importedData.cu_up2, ['f1_u', 'local_ip_address']);
                          break;
                        case 'F1-U gatewayAddress':
                          userVal = getDeepString(importedData.cu_up2, ['f1_u', 'gateway_address']);
                          break;
                        case 'E1 localIpAddress':
                          userVal = getDeepString(importedData.cu_up2, ['e1', 'local_ip_address']);
                          break;
                        case 'E1 remoteAddress':
                          userVal = getDeepString(importedData.cu_up2, ['e1', 'remote_address']);
                          break;
                      }
                      nestedCuUp2.push({
                        name: defRow.name,
                        default: defRow.default,
                        user_setting: userVal,
                        target: defRow.target,
                      });
                      safeImportFlat[`cu_up2-user_setting-${defRow.name}-${idx}`] = String(userVal);
                    });
                    safeImportFlat['deployment_name'] = importedData.deployment_name || '';

                    interfaceForm.reset(safeImportFlat);
                    const initialEditingState: Record<string, boolean> = {};
                    const allNestedGroups: Record<string, InterfaceFieldDataRow[]> = {
                      du: nestedDu,
                      cu_cp: nestedCuCp,
                      cu_up: nestedCuUp,
                      cu_cp2: nestedCuCp2,
                      cu_up2: nestedCuUp2,
                    };

                    setEditedUserSettings?.({
                      du: allNestedGroups.du,
                      cu_cp: allNestedGroups.cu_cp,
                      cu_up: allNestedGroups.cu_up,
                      cu_cp2: allNestedGroups.cu_cp2,
                      cu_up2: allNestedGroups.cu_up2,
                    });

                    Object.entries(allNestedGroups).forEach(([groupName, rows]) => {
                      rows.forEach((row, idx) => {
                        const cellKey = `${groupName}-user_setting-${row.name}-${idx}`;
                        initialEditingState[cellKey] = true;
                      });
                    });
                    setEditing(initialEditingState);

                    await new Promise(requestAnimationFrame);
                    interfaceForm.control._options.resolver = zodResolver(
                      generateInterfaceSchema({
                        du: nestedDu,
                        cu_cp: nestedCuCp,
                        cu_up: nestedCuUp,
                      } as any)
                    );
                    await interfaceForm.trigger();

                    return true;
                  }

                  return false;
                }}
              />

              <FileUpload
                formMethods={interfaceForm}
                caller="simpleViewInterfaces"
                uploadType={FileUploadTypes.export}
                text="Export Interface"
                setEditing={setEditing}
                editing={editing}
                setEditedUserSettings={setEditedUserSettings}
                selectedOption={selectedOption}
                getInterfaceData={getInterfaceData}
              />
            </Flex>
          </>
        ) : (
          <>
            {/* IDENTITIES MODE: first DU‐identities, then Shared‐identities */}
            {/* DU */}
            {(() => {
              const idSettings = editedUserSettings as
                | { du?: Record<string, IdentityField>; shared?: Record<string, IdentityField> }
                | undefined;

              if (idSettings?.du) {
                return renderIdentitiesTable(idSettings.du, 'DU Identities', 'du', editing, setEditing, allCrsEnabled);
              } else if (getIdentitiesData?.du) {
                return renderIdentitiesTable(
                  getIdentitiesData.du,
                  'DU Identities',
                  'du',
                  editing,
                  setEditing,
                  allCrsEnabled
                );
              }
              return null;
            })()}
            {/* SHARED */}
            {(() => {
              const idSettings = editedUserSettings as
                | { du?: Record<string, IdentityField>; shared?: Record<string, IdentityField> }
                | undefined;

              if (idSettings?.shared) {
                return renderIdentitiesTable(
                  idSettings.shared,
                  'Shared Identities',
                  'shared',
                  editing,
                  setEditing,
                  allCrsEnabled
                );
              } else if (getIdentitiesData?.shared) {
                return renderIdentitiesTable(
                  getIdentitiesData.shared,
                  'Shared Identities',
                  'shared',
                  editing,
                  setEditing,
                  allCrsEnabled
                );
              }
              return null;
            })()}

            <Flex justifyContent="space-between" alignItems="center" width="100%" mt="12" mb="8">
              {getIdentitiesData && (
                <FileUpload
                  formMethods={identityForm}
                  caller="simpleViewIdentities"
                  uploadType={FileUploadTypes.import}
                  text="Import Identities"
                  setEditing={setEditing}
                  editing={editing}
                  setEditedUserSettings={setEditedUserSettings}
                  selectedOption={selectedOption}
                  getIdentitiesData={getIdentitiesData}
                  maxCell={maxCell}
                  editedUserSettings={editedUserSettings}
                />
              )}
              <FileUpload
                formMethods={identityForm}
                caller="simpleViewIdentities"
                uploadType={FileUploadTypes.export}
                text="Export Identities"
                setEditing={setEditing}
                editing={editing}
                setEditedUserSettings={setEditedUserSettings}
                selectedOption={selectedOption}
                getIdentitiesData={getIdentitiesData}
                identityControl={identityForm}
                maxCell={maxCell}
                editedUserSettings={editedUserSettings}
              />
            </Flex>
          </>
        )}
        {isCreateInterfaceSuccess ? (
          <Heading as="h2" size="md" mt="12" mb="8" textAlign="center">
            {caller} have been updated successfully
          </Heading>
        ) : null}

        {/* Modal for Max cell Interface */}
        <Modal isOpen={maxCellInterfaceIsOpen} onClose={maxCellInterfaceOnClose}>
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>Import Error</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <Text>{modalMessage}</Text>
            </ModalBody>
            <ModalFooter>
              <Button onClick={maxCellInterfaceOnClose}>Close</Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </Box>
    );
  }
);

DynamicTable.displayName = 'DynamicTable';

export default DynamicTable;
