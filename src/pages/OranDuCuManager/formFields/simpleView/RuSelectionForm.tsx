// RuSelectionForm.tsx
import React, { useEffect, useState } from 'react';
import { Box, Button, Flex, Heading, Text, useColorModeValue } from '@chakra-ui/react';
import { useForm, FormProvider } from 'react-hook-form';
import SelectFormField from '../../../../components/form/oranDuCuManager/select/SelectFormField';
import useRus from '../../hooks/services/useRU';
import { isNil } from 'lodash';

export type Ru = {
  ru_mac_address: string;
  ru_serial_number: string;
  ru_ip_address: string;
  ru_params: {
    max_power: number[];
    cbrs_locked: boolean;
    trp_id: number[] | null;
  };
  deployment_name: string | null;
  du_ru_index: number | null;
  created: string;
  updated: string;
  user_email: string;
  id: number;
};

export type RuManifest = {
  serial_no: string;
  mac_address: string;
  change_no: number;
  model: string;
  version: string;
  product_status: string;
  manifest: {
    mainboard: Record<string, any>;
    nic: Record<string, any>;
    rffe: Record<string, any>[];
  };
  supported_bands: any[];
  created_at: string;
  updated_at?: string;
  discovery?: {
    ip_address: string;
    first_discovery: string;
    last_discovery: string;
    last_started_at: string;
    last_heartbeat: string | null;
    health_info: {
      app: string;
      app_version: string;
      app_up_time: number;
      firmware_version: string;
      host_agent_version: string;
      device_start_time: string;
    };
  };
};

type RuFormData = {
  ru_1?: string;
  ru_2?: string;
};

type RuSelectionFormProps = {
  deploymentName: string;
  deploymentType: string;
  isCompEnabled: boolean;
  onRuSubmit?: (payload: { deployment_name: string; ru_cells: any[] }) => void;
};

const RuSelectionForm: React.FC<RuSelectionFormProps> = ({
  deploymentName,
  deploymentType,
  isCompEnabled,
  onRuSubmit,
}) => {
  const ruMethods = useForm<RuFormData>({
    defaultValues: {
      ru_1: '',
      ru_2: '',
    },
  });

  const colorModeValue = useColorModeValue('sm', 'sm-dark');

  const { getDeploymentRus, getAllRus, getAllRusManifest, setRusForDeployment } = useRus();

  const { data: ruDeployments } = getDeploymentRus(deploymentName, deploymentType);

  const { data: allRus } = getAllRus(deploymentType);
  const { data: allRusManifest } = getAllRusManifest(deploymentType);
  const { mutate: setRusForDeploymentMutation } = useRus().setRusForDeployment();

  const filteredRusManifest: RuManifest[] = allRusManifest
    ? allRusManifest.filter((ru: RuManifest) => {
        const ruDeployment = ruDeployments?.find(
          (d: Ru) => d.ru_serial_number === ru.serial_no && d.ru_mac_address === ru.mac_address
        );
        if (!ruDeployment) return true;
        const fullyAttached = !isNil(ruDeployment.deployment_name) && !isNil(ruDeployment.du_ru_index);
        return !fullyAttached;
      })
    : [];

  const defaultRu1Value =
    ruDeployments && ruDeployments.length > 0
      ? `${ruDeployments[0].ru_serial_number}|${ruDeployments[0].ru_mac_address}`
      : '';
  const defaultRu2Value =
    ruDeployments && ruDeployments.length > 1
      ? `${ruDeployments[1].ru_serial_number}|${ruDeployments[1].ru_mac_address}`
      : '';

  useEffect(() => {
    if (ruDeployments && ruDeployments.length > 0) {
      ruMethods.setValue('ru_1', defaultRu1Value);
      if (ruDeployments.length > 1) {
        ruMethods.setValue('ru_2', defaultRu2Value);
      }
    }
  }, [ruDeployments, ruMethods, defaultRu1Value, defaultRu2Value]);

  const ru1Current = ruMethods.watch('ru_1');

  const ru2RawOptions: { value: string; label: string }[] =
    filteredRusManifest.map((ru: RuManifest) => ({
      value: `${ru.serial_no}|${ru.mac_address}`,
      label: `${ru.serial_no} - ${ru.mac_address}`,
    })) || [];

  const ru2AttachedOption =
    ruDeployments && ruDeployments.length > 1
      ? {
          value: `${ruDeployments[1].ru_serial_number}|${ruDeployments[1].ru_mac_address}`,
          label: `${ruDeployments[1].ru_serial_number} - ${ruDeployments[1].ru_mac_address}`,
        }
      : null;

  const ru2Options = ru2RawOptions.filter((opt) => opt.value !== ru1Current);

  if (
    ru2AttachedOption &&
    ru2AttachedOption.value !== ru1Current &&
    !ru2Options.find((opt) => opt.value === ru2AttachedOption.value)
  ) {
    ru2Options.unshift(ru2AttachedOption);
  }

  const ru1AttachedOption =
    ruDeployments && ruDeployments.length > 0
      ? {
          value: `${ruDeployments[0].ru_serial_number}|${ruDeployments[0].ru_mac_address}`,
          label: `${ruDeployments[0].ru_serial_number} - ${ruDeployments[0].ru_mac_address}`,
        }
      : null;
  const ru1Options = filteredRusManifest.map((ru) => ({
    value: `${ru.serial_no}|${ru.mac_address}`,
    label: `${ru.serial_no} - ${ru.mac_address}`,
  }));

  if (ru1AttachedOption && !ru1Options.find((opt) => opt.value === ru1AttachedOption.value)) {
    ru1Options.unshift(ru1AttachedOption);
  }

  const ruOnSubmit = (ruData: RuFormData) => {
    const finalRu1Value =
      ruData.ru_1 && ruData.ru_1.trim() !== ''
        ? ruData.ru_1
        : ruDeployments && ruDeployments[0]
        ? `${ruDeployments[0].ru_serial_number}|${ruDeployments[0].ru_mac_address}`
        : '';
    const finalRu2Value =
      ruData.ru_2 && ruData.ru_2.trim() !== ''
        ? ruData.ru_2
        : ruDeployments && ruDeployments.length > 1
        ? `${ruDeployments[1].ru_serial_number}|${ruDeployments[1].ru_mac_address}`
        : '';

    const ruCells: {
      ru_mac_address: string;
      ru_serial_number: string;
      ru_ip_address: string;
      ru_params: {
        max_power: number[];
        cbrs_locked: boolean;
        trp_id: number[];
      };
    }[] = [];

    // Process RU 1:
    const [serialNo1, macAddress1] = finalRu1Value.split('|');
    // find RU 1 in the manifest (RuManifest type)
    const ruManifestEntry = filteredRusManifest.find(
      (ru: RuManifest) => ru.serial_no === serialNo1 && ru.mac_address === macAddress1
    );
    if (ruManifestEntry) {
      ruCells.push({
        ru_mac_address: ruManifestEntry.mac_address,
        ru_serial_number: ruManifestEntry.serial_no,
        ru_ip_address: ruManifestEntry.discovery?.ip_address || '',
        ru_params: {
          max_power: [24],
          cbrs_locked: true,
          trp_id: [0],
        },
      });
    } else {
      // Fall back to ruDeployments (Ru type)
      const ruDeploymentEntry = ruDeployments?.find(
        (d: Ru) => d.ru_serial_number === serialNo1 && d.ru_mac_address === macAddress1
      );
      if (ruDeploymentEntry) {
        ruCells.push({
          ru_mac_address: ruDeploymentEntry.ru_mac_address,
          ru_serial_number: ruDeploymentEntry.ru_serial_number,
          ru_ip_address: ruDeploymentEntry.ru_ip_address,
          ru_params: ruDeploymentEntry.ru_params,
        });
      }
    }

    // Process RU 2 if CoMP is enabled:
    if (isCompEnabled) {
      const [serialNo2, macAddress2] = finalRu2Value.split('|');
      const ruManifestEntry2 = filteredRusManifest.find(
        (ru: RuManifest) => ru.serial_no === serialNo2 && ru.mac_address === macAddress2
      );
      if (ruManifestEntry2) {
        ruCells.push({
          ru_mac_address: ruManifestEntry2.mac_address,
          ru_serial_number: ruManifestEntry2.serial_no,
          ru_ip_address: ruManifestEntry2.discovery?.ip_address || '',
          ru_params: {
            max_power: [24],
            cbrs_locked: true,
            trp_id: [0],
          },
        });
      } else {
        const ruDeploymentEntry2 = ruDeployments?.find(
          (d: Ru) => d.ru_serial_number === serialNo2 && d.ru_mac_address === macAddress2
        );
        if (ruDeploymentEntry2) {
          ruCells.push({
            ru_mac_address: ruDeploymentEntry2.ru_mac_address,
            ru_serial_number: ruDeploymentEntry2.ru_serial_number,
            ru_ip_address: ruDeploymentEntry2.ru_ip_address,
            ru_params: ruDeploymentEntry2.ru_params,
          });
        }
      }
    }

    const payload = {
      deployment_name: deploymentName,
      ru_cells: ruCells,
    };

    if (onRuSubmit) {
      onRuSubmit(payload);
    } else {
      setRusForDeploymentMutation(payload);
    }
  };

  return (
    <FormProvider {...ruMethods}>
      <Box mt="20" p="8" boxShadow={colorModeValue} borderRadius="xl" data-testid="pod-table">
        <Heading textAlign="center" as="h2" fontSize="20px" pt="2">
          Radio Units
        </Heading>
        <Heading textAlign="center" as="h2" fontSize="20px" pb="4" pt="4">
          There {ruDeployments?.length === 1 ? 'is' : 'are'} {ruDeployments?.length ?? 0} Radio Unit
          {ruDeployments?.length === 1 ? '' : 's'} attached to the deployment
        </Heading>
        <Text textAlign="center" pb="4">
          Select a Radio unit to attach to the deployment
        </Text>
        <SelectFormField
          name="ru_1"
          label="Radio unit 1"
          defaultValue={defaultRu1Value}
          options={ru1Options}
          tooltip="Select a Radio unit to attach to the deployment"
        />
        {isCompEnabled && (
          <SelectFormField
            name="ru_2"
            label="Radio unit 2"
            defaultValue={defaultRu2Value}
            options={ru2Options}
            tooltip="Select a Radio unit to attach to the deployment"
          />
        )}
        <Flex justifyContent="right">
          <Button
            variant="primary"
            type="button"
            onClick={() => {
              const ruData = ruMethods.getValues();
              ruOnSubmit(ruData);
            }}
          >
            Attach Radio Unit
          </Button>
        </Flex>
      </Box>
    </FormProvider>
  );
};

export default RuSelectionForm;
