import {
  Box,
  Button,
  Divider,
  Flex,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  <PERSON>dal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  Switch,
  Text,
  Tooltip,
  useColorModeValue,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import axios from 'axios';
import { useEffect, useRef, useState } from 'react';
import { useForm, FormProvider, UseFormReturn, useWatch } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import Loader from '../../../../components/loader/Loader';
import useCustomResource from '../../hooks/services/useCustomResource';
import { isEmpty, toNumber } from 'lodash';
import { SelectedOption } from '../../../../types/duCuManager.type';
import CustomResourceDeploymentButtonGroup from './CustomResourceDeploymentButtonGroup';
import PodsDeploymentButtonGroup from './PodsDeploymentButtonGroup';
import usePods from '../../hooks/services/usePods';
import { validateIP } from '../../../../utils/helpers';
import DeploymentCrTable from '../../tables/DeploymentCrTable';
import { useNavigate, useLocation } from 'react-router';
import DeploymentNameField from './DeploymentNameField';
import DeploymentF1E1Fields from './DeploymentF1E1Fields';
import DeploymentSiteClusters from './DeploymentSiteClusters';
import DeploymentSiteNameFields from './DeploymentSiteNameFields';
import DeploymentPodTable from '../../tables/DeploymentPodTable';
import useLogin from '../../../../hooks/useLogin';
import { AUTH_TOKEN_KEY, FileUploadTypes, READ_WRITE_ACCESS_ROLES } from '../../../../data/constants';
import FileUpload, { DeploymentFormData } from '../../../../components/fileUpload/FileUpload';
import QueryError from '../../../../components/errorComponents/QueryError';
import useRus from '../../hooks/services/useRU';
import RuSelectionForm from './RuSelectionForm';
import MoranDeploymentSiteAndClusters from './MoranDeploymentSiteAndClusters';

type DeploymentFormProps = {
  isDisabled: boolean | undefined;
  DeploymentCustomResourcesData?: any;
  selectedOption: SelectedOption;
  setSelectedOption: React.Dispatch<React.SetStateAction<SelectedOption>>;
  caller?: string;
  podList?: any;
  deployment_type?: string;
  setEditing: React.Dispatch<React.SetStateAction<Record<string, boolean>>>;
  editing: Record<string, boolean>;
  maxCell: number;
};

type BandInfo = {
  min: number;
  max: number;
  step: number;
};

type BandData = {
  [key: string]: BandInfo;
};

type DeploymentExists = {
  exists: boolean;
  obj: SelectedOption | null;
};

const DeploymentForm = ({
  isDisabled,
  DeploymentCustomResourcesData,
  selectedOption,
  setSelectedOption,
  caller,
  podList,
  deployment_type,
  editing,
  setEditing,
  maxCell,
}: DeploymentFormProps) => {
  //const toast = useToast();
  const [showErrorComponent, setShowErrorComponent] = useState(false);
  const [moranDeployment, setMoranDeployment] = useState(false);
  const [editedUserSettings, setEditedUserSettings] = useState<any>(null);

  const [siteCreationError, setSiteCreationError] = useState(null);
  const [deploymentExists, setDeploymentExists] = useState<DeploymentExists>({ exists: false, obj: null });
  const colorModeValue = useColorModeValue('sm', 'sm-dark');

  const navigate = useNavigate();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const {
    isOpen: maxCellDeploymentIsOpen,
    onOpen: maxCellDeploymentOnOpen,
    onClose: maxCellDeploymentOnClose,
  } = useDisclosure();
  const location = useLocation();

  const locationState = location.state as any;

  const isMoranFromUseCaseForm = locationState?.isMoranDeployment;
  const hasMultipleSSBArfcns =
    locationState?.ssbArfcn && Array.isArray(locationState.ssbArfcn) && locationState.ssbArfcn.length === 2;

  useEffect(() => {
    if (isMoranFromUseCaseForm || hasMultipleSSBArfcns) {
      setMoranDeployment(true);
    }
  }, [isMoranFromUseCaseForm, hasMultipleSSBArfcns]);

  const { checkNmsDevAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkNmsDevAccess(READ_WRITE_ACCESS_ROLES);

  const interfaceButtonRef = useRef<HTMLButtonElement>(null);
  const ruFormRef = useRef<HTMLDivElement>(null);
  const { scrollTo } = location.state || {};

  // API calls
  const { getClusterList, createCustomResourcesForSite, deleteAllCustomResourceForSite, getAllDeployments } =
    useCustomResource();
  const { deActivateAllSiteCustomResources, activateAllSiteCustomResources } = usePods();

  // Mutations
  const {
    mutate: activatePodMutation,
    isLoading: isActivatingPod,
    isError: isActivatingPodError,
    isSuccess: isActivatingPodSuccess,
  } = activateAllSiteCustomResources();
  const {
    mutate: deActivatePodMutation,
    isLoading: isDeActivatingPod,
    isError: isDeActivatingPodError,
    isSuccess: isDeActivatingPodSuccess,
  } = deActivateAllSiteCustomResources();
  const { mutate: deleteAllCustomResourceForDeploymentMutation, isLoading: isDeleteAllCustomResourceForDeployment } =
    deleteAllCustomResourceForSite();
  const {
    mutate: createCustomResourcesForSiteMutation,
    isLoading: createCustomResourcesForSiteIsCreating,
    isError: createCustomResourcesForSiteIsError,
    isSuccess: createCustomResourcesForSiteIsSuccess,
  } = createCustomResourcesForSite();

  const { data: clusterList, isLoading: isClusterLoading, error: isClusterError } = getClusterList();

  // Get all Deployments
  const {
    data: allDeployments,
    isLoading: isAllDeploymentsLoading,
    error: allDeploymentsError,
  } = getAllDeployments(deployment_type);

  // Get Ru's
  const { mutate: setRusForDeploymentMutation, isLoading: isRuAttaching } = useRus().setRusForDeployment();

  // Find deployment
  const findDeploymentMatch = (dataArray: SelectedOption[], selectedOption: SelectedOption) => {
    if (!dataArray) return { exists: false, obj: null };
    for (const item of dataArray) {
      if (item?.deployment_name === selectedOption?.deployment_name) {
        return { exists: true, obj: item };
      }
    }
    return { exists: false, obj: null };
  };

  const nameRegex = /^[a-z0-9]([-/.a-z0-9]*?[a-z0-9])$/;
  const deploymentNameRegex = /^[A-Z]{2}[A-Za-z0-9]{4}[LN][SB]\d{2}\d{4}$/;

  const schema = z.object({
    deployment_name: z
      .string()
      .min(2, { message: 'Name must be at least 2 characters' })
      .regex(deploymentNameRegex, { message: 'Invalid format' }),
    e1_ip: z
      .string()
      .min(1, { message: 'E1 IP address is required' })
      .refine((val) => validateIP(val), {
        message: 'Invalid IP address format',
      }),
    f1_ip: z
      .string()
      .min(1, { message: 'F1 IP address is required' })
      .refine((val) => validateIP(val), {
        message: 'Invalid IP address format',
      }),
    ru_vendor: z.union([z.string().min(1, 'RU vendor is required'), z.null()]),
    du_site_name: z
      .string()
      .min(2, { message: 'Name must be at least 2 characters' })
      .regex(nameRegex, { message: 'Invalid format' }),
    cucp_site_name: z
      .string()
      .min(2, { message: 'Name must be at least 2 characters' })
      .regex(nameRegex, { message: 'Invalid format' }),
    cuup_site_name: z
      .string()
      .min(2, { message: 'Name must be at least 2 characters' })
      .regex(nameRegex, { message: 'Invalid format' }),
    du_cluster: z
      .union([z.string(), z.number()])
      .transform((val) => Number(val))
      .nullable(),
    cucp_cluster: z
      .union([z.string(), z.number()])
      .transform((val) => Number(val))
      .nullable(),
    cuup_cluster: z
      .union([z.string(), z.number()])
      .transform((val) => Number(val))
      .nullable(),
    deployment_params: z.object({
      is_comp: z.boolean().default(false),
      comp_trp_count: z.number().default(0),
      rf_use_case: z.number().default(0),
      max_cells: z.number().default(2),
      ssb_arfcns: z.array(z.number()).optional(),
      du_fh_vlans: z.array(z.number()).optional(),
      cu2: z
        .object({
          cucp_site_name2: z.string().optional(),
          cuup_site_name2: z.string().optional(),
          f1_ip2: z.string().optional(),
          e1_ip2: z.string().optional(),
        })
        .optional(),
    }),
  });

  type FormData = z.infer<typeof schema>;

  const defaultValues: FormData = {
    deployment_name: selectedOption?.deployment_name || '',
    du_site_name: selectedOption?.du_site_name || '',
    cucp_site_name: selectedOption?.cucp_site_name || '',
    cuup_site_name: selectedOption?.cuup_site_name || '',
    du_cluster: selectedOption?.du_cluster ?? -1,
    cucp_cluster: selectedOption?.cucp_cluster ?? -1,
    cuup_cluster: selectedOption?.cuup_cluster ?? -1,
    ru_vendor: selectedOption?.ru_vendor ?? null,
    f1_ip: selectedOption?.f1_ip || '',
    e1_ip: selectedOption?.e1_ip || '',
    deployment_params: {
      is_comp: selectedOption?.deployment_params?.is_comp || false,
      comp_trp_count: selectedOption?.deployment_params?.comp_trp_count || 0,
      rf_use_case: selectedOption?.deployment_params?.rf_use_case || 0,
      max_cells: isMoranFromUseCaseForm || hasMultipleSSBArfcns ? 2 : selectedOption?.deployment_params?.max_cells || 1,
      ssb_arfcns:
        locationState?.ssbArfcn ||
        (Array.isArray(selectedOption?.deployment_params?.ssb_arfcns)
          ? selectedOption?.deployment_params?.ssb_arfcns
          : []),
      du_fh_vlans:
        locationState?.duFhVlans ||
        (Array.isArray(selectedOption?.deployment_params?.du_fh_vlans)
          ? selectedOption?.deployment_params?.du_fh_vlans
          : []),
      cu2: {
        cucp_site_name2: selectedOption?.deployment_params?.cu2?.cucp_site_name2 || '',
        cuup_site_name2: selectedOption?.deployment_params?.cu2?.cuup_site_name2 || '',
        f1_ip2: selectedOption?.deployment_params?.cu2?.f1_ip2 || '',
        e1_ip2: selectedOption?.deployment_params?.cu2?.e1_ip2 || '',
      },
    },
  };

  const methods = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues,
    mode: 'onBlur',
  });

  const isComp = methods.watch('deployment_params.is_comp');

  const isReady = !!selectedOption?.deployment_name;

  const shouldShowCRTable =
    createCustomResourcesForSiteIsSuccess ||
    (selectedOption?.deployment_name &&
      !isEmpty(allDeployments) &&
      allDeployments.some((d: any) => d.deployment_name === selectedOption.deployment_name));

  const deploymentOnSubmit = (data: FormData) => {
    const patchedDeploymentParams = {
      ...data.deployment_params,
      ssb_arfcns: selectedOption?.deployment_params?.ssb_arfcns ?? [],
      du_fh_vlans: selectedOption?.deployment_params?.du_fh_vlans ?? [],
      cu2: data.deployment_params.cu2,
    };

    const cu2 = patchedDeploymentParams.cu2;
    const moranHasValues =
      moranDeployment && cu2 && (cu2.cucp_site_name2 || cu2.cuup_site_name2 || cu2.f1_ip2 || cu2.e1_ip2);

    if (moranHasValues) {
      patchedDeploymentParams.max_cells = 2;
    }

    if (!moranDeployment) {
      delete patchedDeploymentParams.cu2;
    }

    const createPayloadData: SelectedOption = {
      ...selectedOption,
      ...data,
      deployment_name: data.deployment_name,
      cucp_site_name: data.cucp_site_name,
      cuup_site_name: data.cuup_site_name,
      du_site_name: data.du_site_name,
      du_cluster: toNumber(data.du_cluster),
      cucp_cluster: toNumber(data.cucp_cluster),
      cuup_cluster: toNumber(data.cuup_cluster),
      f1_ip: data.f1_ip,
      e1_ip: data.e1_ip,
      ru_vendor: data.ru_vendor,
      deployment_params: patchedDeploymentParams,
    };

    createCustomResourcesForSiteMutation(createPayloadData, {
      onSuccess: (data) => {
        setSelectedOption(createPayloadData);
      },
      onError: (error) => {
        if (
          axios.isAxiosError(error) &&
          error?.response?.status &&
          error.response?.status >= 400 &&
          error.response?.status < 500
        ) {
          setShowErrorComponent(true);
          setSiteCreationError(error?.response?.data);
        }
      },
    });
  };

  const handleActivatePod = () => {
    if (!isEmpty(selectedOption)) {
      const activatePayloadData = selectedOption?.deployment_name as string;
      activatePodMutation(activatePayloadData, {
        onError: (error) => {
          console.error('Error submitting data:', error);
        },
      });
    } else {
      console.log('Activation not allowed.');
    }
  };

  const handleDeactivatePod = () => {
    if (!isEmpty(selectedOption)) {
      const deActivatePayloadData = selectedOption?.deployment_name as string;
      deActivatePodMutation(deActivatePayloadData, {
        onError: (error) => {
          console.error('Error submitting data:', error);
        },
      });
    } else {
      console.log('Activation not allowed.');
    }
  };

  const handleDeleteAllCRs = () => {
    const deletePayloadData = selectedOption?.deployment_name;

    deleteAllCustomResourceForDeploymentMutation(deletePayloadData, {
      onSuccess: (data) => {
        methods.reset({
          deployment_name: '',
          du_site_name: '',
          cucp_site_name: '',
          cuup_site_name: '',
          du_cluster: -1,
          cucp_cluster: -1,
          cuup_cluster: -1,
          f1_ip: '',
          e1_ip: '',
        });

        setSelectedOption(null);

        navigate('/oran-du-cu-manager/simple-view', {
          state: {},
        });
      },
      onError: (error) => {
        console.error('Error submitting data:', error);
      },
    });
  };

  const currentParams = methods.getValues('deployment_params');

  useEffect(() => {
    if (editedUserSettings) {
      Object.keys(editedUserSettings).forEach((key) => {
        if (key !== 'deployment_name') {
          methods.setValue(key as keyof FormData, editedUserSettings[key], { shouldValidate: false });
        }
      });
      methods.trigger();
    }
  }, [editedUserSettings, methods]);

  useEffect(() => {
    const updatedDeploymentParams = {
      ...currentParams,
      is_comp: isComp,
      comp_trp_count: isComp ? 2 : 0,
    };
    methods.setValue('deployment_params', updatedDeploymentParams, {
      shouldValidate: true,
    });
  }, [isComp, methods]);

  useEffect(() => {
    if (allDeployments) {
      const match = findDeploymentMatch(allDeployments, selectedOption);
      setDeploymentExists(match);
      if (match.exists && match.obj) {
        setSelectedOption(match.obj);
        methods.reset({
          deployment_name: match.obj.deployment_name || '',
          du_site_name: match.obj.du_site_name || '',
          cucp_site_name: match.obj.cucp_site_name || '',
          cuup_site_name: match.obj.cuup_site_name || '',
          du_cluster: match.obj.du_cluster ?? -1,
          cucp_cluster: match.obj.cucp_cluster ?? -1,
          cuup_cluster: match.obj.cuup_cluster ?? -1,
          deployment_params: {
            is_comp: match.obj.deployment_params?.is_comp || false,
            comp_trp_count: match.obj.deployment_params?.comp_trp_count || 0,
            rf_use_case: match.obj.deployment_params?.rf_use_case || 0,
            max_cells: match.obj.deployment_params?.max_cells,
            ssb_arfcns: Array.isArray(match.obj.deployment_params?.ssb_arfcns)
              ? match.obj.deployment_params?.ssb_arfcns
              : [],
            du_fh_vlans: Array.isArray(match.obj.deployment_params?.du_fh_vlans)
              ? match.obj.deployment_params?.du_fh_vlans
              : [],
            cu2: match.obj.deployment_params?.cu2 || {
              cucp_site_name2: '',
              cuup_site_name2: '',
              f1_ip2: '',
              e1_ip2: '',
            },
          },
          e1_ip: match.obj.e1_ip || '',
          f1_ip: match.obj.f1_ip || '',
          ru_vendor: match.obj.ru_vendor ?? null,
        });
      }
    }
  }, [selectedOption, allDeployments, methods, setSelectedOption]);

  const cu2 = useWatch({ control: methods.control, name: 'deployment_params.cu2' });

  useEffect(() => {
    if ((cu2 && (cu2.cucp_site_name2 || cu2.cuup_site_name2 || cu2.f1_ip2 || cu2.e1_ip2)) || moranDeployment) {
      setMoranDeployment(true);
      methods.setValue('deployment_params.max_cells', 2, { shouldValidate: true });
    } else {
      methods.setValue('deployment_params.max_cells', 1, { shouldValidate: true });
    }
  }, [cu2, methods, moranDeployment]);

  useEffect(() => {
    if (deployment_type === 'edit' && selectedOption?.deployment_params?.max_cells === 2) {
      setMoranDeployment(true);
    }
  }, [selectedOption, deployment_type]);

  useEffect(() => {
    if ((isMoranFromUseCaseForm || hasMultipleSSBArfcns) && deployment_type === 'new') {
      setMoranDeployment(true);

      methods.setValue('deployment_params.max_cells', 2, { shouldValidate: true });

      if (locationState?.ssbArfcn && Array.isArray(locationState.ssbArfcn)) {
        methods.setValue('deployment_params.ssb_arfcns', locationState.ssbArfcn, { shouldValidate: true });
      }

      if (locationState?.duFhVlans && Array.isArray(locationState.duFhVlans)) {
        methods.setValue('deployment_params.du_fh_vlans', locationState.duFhVlans, { shouldValidate: true });
      }
    }
  }, [isMoranFromUseCaseForm, hasMultipleSSBArfcns, deployment_type, locationState, methods]);

  useEffect(() => {
    if (moranDeployment) {
      methods.setValue('deployment_params.max_cells', 2, { shouldValidate: true });
    } else if (deployment_type === 'new') {
      methods.setValue('deployment_params.max_cells', 1, { shouldValidate: true });
    }
  }, [moranDeployment, methods, deployment_type]);

  // NOTE: after set SUCCESS, scroll into view
  useEffect(() => {
    if (createCustomResourcesForSiteIsSuccess && !isAllDeploymentsLoading && interfaceButtonRef.current) {
      interfaceButtonRef.current!.scrollIntoView({ behavior: 'smooth' });
    }
  }, [createCustomResourcesForSiteIsSuccess, isAllDeploymentsLoading]);

  useEffect(() => {
    if (scrollTo === 'ruSelection' && ruFormRef.current) {
      ruFormRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, [scrollTo]);

  if (isClusterLoading) return <Loader />;

  if (isClusterError) return <QueryError error={isClusterError} />;

  return (
    <FormProvider {...methods}>
      <Box>
        {/* Deployment form */}
        <>
          <DeploymentNameField
            deploymentType={deployment_type ?? ''}
            isDisabled={isDisabled ?? false}
            createCustomResourcesForSiteIsSuccess={createCustomResourcesForSiteIsSuccess}
            cellRef={selectedOption?.deployment_name ?? undefined}
          />
          <DeploymentF1E1Fields
            isDisabled={isDisabled ?? false}
            createCustomResourcesForSiteIsSuccess={createCustomResourcesForSiteIsSuccess}
          />
          <DeploymentSiteNameFields
            isDisabled={isDisabled ?? false}
            createCustomResourcesForSiteIsSuccess={createCustomResourcesForSiteIsSuccess}
          />
          <DeploymentSiteClusters
            isDisabled={isDisabled ?? false}
            createCustomResourcesForSiteIsSuccess={createCustomResourcesForSiteIsSuccess}
            clusterList={clusterList}
          />

          {/* Moran(multiple) CU's */}
          {moranDeployment && (
            <Flex align="center" mb="8">
              <Switch
                isChecked={moranDeployment}
                isDisabled={deployment_type === 'edit' ? true : moranDeployment ?? false}
                onChange={() => {
                  setMoranDeployment((prev) => {
                    const newValue = !prev;
                    methods.setValue('deployment_params.max_cells', newValue ? 2 : 1, { shouldValidate: true });
                    return newValue;
                  });
                }}
                mr={2}
                id="moran-switch"
              />
              <Tooltip label="Enable this to add a second RU for MORAN deployment (requires 2 SSB ARFCNs)">
                <Text as="label" htmlFor="moran-switch" cursor="pointer" mb="0">
                  MORAN deployment {moranDeployment}
                </Text>
              </Tooltip>
            </Flex>
          )}
          {/* Moran Site & Clusters */}
          {moranDeployment && (
            <Box width="100%" mb="4">
              <MoranDeploymentSiteAndClusters
                isDisabled={isDisabled ?? false}
                createCustomResourcesForSiteIsSuccess={createCustomResourcesForSiteIsSuccess}
                clusterList={clusterList}
              />
            </Box>
          )}
        </>

        {/* File upload */}
        {deployment_type === 'new' && caller === 'simpleViewCustomResource' && (
          <Flex justifyContent="space-between" alignItems="center" width="100%" mt="12" mb="8">
            <FileUpload
              formMethods={methods as UseFormReturn<DeploymentFormData>}
              caller="simpleViewCustomResource"
              uploadType={FileUploadTypes.import}
              text="Import deployment"
              setEditedUserSettings={setEditedUserSettings}
              setEditing={setEditing}
              editing={editing}
              selectedOption={selectedOption}
              maxCell={maxCell}
              onImportAttempt={(importedData: any) => {
                const incomingMax = importedData?.deployment_params?.max_cells ?? 1;
                if (maxCell === 1 && incomingMax === 2) {
                  maxCellDeploymentOnOpen();
                  return false;
                }
                if (maxCell === 2 && incomingMax === 1) {
                  const safeImport: any = {
                    deployment_name: importedData.deployment_name,
                    du_site_name: importedData.du_site_name,
                    cucp_site_name: importedData.cucp_site_name,
                    cuup_site_name: importedData.cuup_site_name,
                    du_cluster: importedData.du_cluster,
                    cucp_cluster: importedData.cucp_cluster,
                    cuup_cluster: importedData.cuup_cluster,
                    f1_ip: importedData.f1_ip,
                    e1_ip: importedData.e1_ip,
                    ru_vendor: importedData.ru_vendor,
                    deployment_params: {
                      rf_use_case: importedData.deployment_params.rf_use_case,
                      max_cells: 1,
                      ssb_arfcns: importedData.deployment_params.ssb_arfcns ?? [],
                      du_fh_vlans: importedData.deployment_params.du_fh_vlans ?? [],
                    },
                  };
                  methods.reset(safeImport);
                  return true;
                }

                methods.reset(importedData);
                return true;
              }}
            />
            <FileUpload
              formMethods={methods as UseFormReturn<DeploymentFormData>}
              caller="simpleViewCustomResource"
              uploadType={FileUploadTypes.export}
              text="Export deployment"
              setEditedUserSettings={setEditedUserSettings}
              setEditing={setEditing}
              editing={editing}
              selectedOption={selectedOption}
            />
          </Flex>
        )}

        {/* File upload */}
        {deployment_type === 'edit' && caller === 'simpleViewCustomResource' && (
          <Flex justifyContent="flex-end" alignItems="end" width="100%" mt="12" mb="8">
            <FileUpload
              formMethods={methods as UseFormReturn<DeploymentFormData>}
              caller="simpleViewCustomResource"
              uploadType={FileUploadTypes.export}
              text="Export deployment"
            />
          </Flex>
        )}

        {/* RU section refactor */}
        {deployment_type === 'edit' && (
          <Box ref={ruFormRef}>
            {isRuAttaching ? (
              <Loader />
            ) : (
              <RuSelectionForm
                deploymentName={selectedOption?.deployment_name || ''}
                deploymentType={deployment_type}
                isCompEnabled={deploymentExists?.obj?.deployment_params?.is_comp ?? false}
                onRuSubmit={(payload) => {
                  setRusForDeploymentMutation(payload);
                }}
              />
            )}
          </Box>
        )}

        {/* --- view table data for page(cr || pod) --- */}
        {selectedOption?.deployment_name && caller !== 'simpleViewCustomResource' ? (
          <DeploymentPodTable
            selectedOption={selectedOption}
            colorModeValue={colorModeValue}
            showTable={selectedOption?.deployment_name ? true : false}
          />
        ) : null}

        {/* Deployment CR table */}
        {shouldShowCRTable && (
          <DeploymentCrTable
            deploymentName={selectedOption?.deployment_name ?? ''}
            du_cluster={selectedOption?.du_cluster ?? null}
            cucp_cluster={selectedOption?.cucp_cluster ?? null}
            cuup_cluster={selectedOption?.cuup_cluster ?? null}
            colorModeValue={colorModeValue}
            showTable={true}
            isReady={isReady}
          />
        )}

        {/* Button logic */}
        <Divider />
        {deployment_type === 'new' && caller === 'simpleViewCustomResource' ? (
          <>
            {createCustomResourcesForSiteIsCreating || isDeleteAllCustomResourceForDeployment ? (
              <Loader />
            ) : (
              <CustomResourceDeploymentButtonGroup
                ref={interfaceButtonRef}
                deploymentExists={deploymentExists.exists}
                isCreating={createCustomResourcesForSiteIsCreating}
                IsDeploymentSuccess={createCustomResourcesForSiteIsSuccess}
                isDeleting={isDeleteAllCustomResourceForDeployment}
                selectedOption={selectedOption}
                clusterList={clusterList}
                methods={methods}
                onSubmit={deploymentOnSubmit}
                deployment_type={deployment_type}
                onOpen={onOpen}
              />
            )}
          </>
        ) : null}
        {deployment_type === 'edit' && caller === 'simpleViewCustomResource' ? (
          <>
            {createCustomResourcesForSiteIsCreating || isDeleteAllCustomResourceForDeployment ? (
              <Loader />
            ) : (
              <CustomResourceDeploymentButtonGroup
                deploymentExists={deploymentExists.exists}
                isCreating={createCustomResourcesForSiteIsCreating}
                IsDeploymentSuccess={createCustomResourcesForSiteIsSuccess}
                isDeleting={isDeleteAllCustomResourceForDeployment}
                selectedOption={selectedOption}
                clusterList={clusterList}
                methods={methods}
                onSubmit={deploymentOnSubmit}
                deployment_type={deployment_type}
                onOpen={onOpen}
              />
            )}
          </>
        ) : null}
        {caller === 'simpleViewPods' ? (
          <>
            {isActivatingPod || isDeActivatingPod || isDeleteAllCustomResourceForDeployment ? (
              <Loader />
            ) : (
              <PodsDeploymentButtonGroup
                deploymentExists={deploymentExists}
                handleActivate={handleActivatePod}
                handleDeactivate={handleDeactivatePod}
                DeploymentCustomResourcesData={DeploymentCustomResourcesData}
                onOpen={onOpen}
                isOpen={isOpen}
                onClose={onClose}
              />
            )}
          </>
        ) : null}

        {/* Max cell Modal */}
        <Modal isOpen={maxCellDeploymentIsOpen} onClose={maxCellDeploymentOnClose} size="5xl">
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>Import Skipped</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <Text>
                Your current form is configured for a single-cell deployment (maxCell=1), but the JSON you tried to
                import is a two-cell deployment (max_cells=2). As a result, we have skipped the import. If you intended
                to work with two cells, please create a two cell deployment.
              </Text>
              <Button mt={4} colorScheme="blue" onClick={maxCellDeploymentOnClose}>
                Close
              </Button>
            </ModalBody>
          </ModalContent>
        </Modal>

        {/* Delete Modal */}
        <Modal isOpen={isOpen} onClose={onClose}>
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>Confirm Deletion</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              Are you sure you want to delete this deployment? This will delete the associated pod, custom resources and
              deployment. This action cannot be undone.
            </ModalBody>

            <ModalFooter>
              {checkRoleAccess && (
                <Button colorScheme="red" onClick={handleDeleteAllCRs}>
                  Confirm
                </Button>
              )}

              <Button variant="ghost" onClick={onClose} ml={3}>
                Cancel
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* Error component */}
        {showErrorComponent && (
          <Box bg="red.500" p="4" borderRadius="md" overflow="auto" maxW="100%" maxH="300px" my="8">
            <Text as="pre" whiteSpace="pre-wrap" color="white">
              Error - {JSON.stringify(siteCreationError, null, 2)}
            </Text>
          </Box>
        )}
      </Box>
    </FormProvider>
  );
};

export default DeploymentForm;
