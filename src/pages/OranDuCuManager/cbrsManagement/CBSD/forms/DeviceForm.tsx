import React, { useEffect } from 'react';
import {
  Stack,
  Input,
  Button,
  Box,
  Flex,
  FormControl,
  FormLabel,
  FormErrorMessage,
  Icon,
  Text,
  SimpleGrid,
  Select,
} from '@chakra-ui/react';
import { useFormContext, useWatch } from 'react-hook-form';
import { RepeatIcon } from '@chakra-ui/icons';
import { FormValues } from '../../../../../types/duCuManager.type';

const DeviceForm = ({
  clustersData,
  ruData = [],
  isEdit = false,
}: {
  clustersData: any;
  ruData?: any;
  isEdit?: boolean;
}) => {
  const {
    register,
    reset,
    setValue,
    getValues,
    control,
    formState: { errors },
  } = useFormContext<FormValues>();

  const selectedSerialNumber = useWatch({
    control,
    name: 'deviceFormData.serialNumber',
  });

  useEffect(() => {
    const formData = getValues('deviceFormData');
    Object.entries(formData).forEach(([key, value]) => {
      setValue(`deviceFormData.${key as keyof typeof formData}`, value);
    });
  }, [getValues, setValue]);

  useEffect(() => {
    if (selectedSerialNumber && ruData) {
      const selectedRu = ruData.find((ru: any) => ru.serial_no === selectedSerialNumber);
      if (selectedRu && selectedRu.mac_address) {
        setValue('deviceFormData.macAddress', selectedRu.mac_address);
      }
    } else {
      // Clear macAddress when serialNumber is empty or cleared
      setValue('deviceFormData.macAddress', '');
    }
  }, [selectedSerialNumber, ruData, setValue]);

  const getClusterOptions = clustersData.map(({ cluster_id }: { cluster_id: string }) => cluster_id);
  const getSerialNumberOptions = ruData.map(({ serial_no }: { serial_no: string }) => serial_no);

  return (
    <Stack>
      <Box marginX="auto" width="full" bg="bg-surface">
        <Flex display="flex" padding="4" justifyContent="space-between">
          <Text fontSize="3xl" fontWeight="bold" textAlign="center">
            Device
          </Text>
          {!isEdit && (
            <Button
              variant="primary"
              leftIcon={<Icon as={RepeatIcon} marginStart="-1" />}
              onClick={() =>
                reset({
                  deviceFormData: {
                    displayName: '',
                    serialNumber: '',
                    macAddress: '',
                    fccId: '',
                    clusterId: '',
                    userId: 'DenseAir',
                    category: 'A',
                  },
                  configFormData: getValues('configFormData'),
                  installationFormData: getValues('installationFormData'),
                })
              }
              data-testid="device-reset-form"
            >
              Reset Device
            </Button>
          )}
        </Flex>
        <SimpleGrid
          columns={{ base: 1, md: 2 }}
          spacing="12"
          p={{
            base: '7',
            md: '8',
          }}
        >
          <FormControl isInvalid={Boolean(errors?.deviceFormData?.displayName)} isRequired>
            <FormLabel htmlFor="displayName">Display name</FormLabel>
            <Input
              id="displayName"
              placeholder="Please enter a display name"
              data-testid="display-name"
              {...register('deviceFormData.displayName', { required: true })}
            />
            <FormErrorMessage>This field is required</FormErrorMessage>
          </FormControl>

          {!isEdit && (
            <FormControl isInvalid={Boolean(errors?.deviceFormData?.serialNumber)} isRequired>
              <FormLabel htmlFor="serialNumber">Serial number</FormLabel>
              <Select
                id="serialNumber"
                disabled={isEdit}
                data-testid="serial-number"
                placeholder="Please select a serial number"
                {...register('deviceFormData.serialNumber', { required: true })}
              >
                {getSerialNumberOptions.map((value: string) => (
                  <option key={value} value={value}>
                    {value}
                  </option>
                ))}
              </Select>
              <FormErrorMessage>This field is required</FormErrorMessage>
            </FormControl>
          )}

          <FormControl isInvalid={Boolean(errors?.deviceFormData?.fccId)} isRequired>
            <FormLabel htmlFor="fccId">FCC ID</FormLabel>
            <Input
              id="fccId"
              data-testid="fcc-id"
              placeholder="Please add fcc id"
              {...register('deviceFormData.fccId', { required: true })}
            />
            <FormErrorMessage>This field is required</FormErrorMessage>
          </FormControl>

          {!isEdit && (
            <FormControl>
              <FormLabel htmlFor="macAddress">MAC Address</FormLabel>
              <Input
                id="macAddress"
                data-testid="mac-address"
                placeholder="MAC address will be populated automatically"
                readOnly
                bg="gray.50"
                {...register('deviceFormData.macAddress')}
              />
            </FormControl>
          )}

          <FormControl isInvalid={Boolean(errors?.deviceFormData?.clusterId)} isRequired>
            <FormLabel htmlFor="clusterId">Cluster ID</FormLabel>
            <Select
              id="clusterId"
              data-testid="cluster-id"
              placeholder="Please select a cluster id"
              {...register('deviceFormData.clusterId', { required: true })}
            >
              {getClusterOptions.map((value: string) => (
                <option key={value} value={value}>
                  {value}
                </option>
              ))}
            </Select>

            <FormErrorMessage>This field is required</FormErrorMessage>
          </FormControl>
          {!isEdit && (
            <FormControl isInvalid={Boolean(errors?.deviceFormData?.category)} isRequired>
              <FormLabel htmlFor="category">Category</FormLabel>
              <Select disabled={isEdit} id="category" data-testid="category" {...register('deviceFormData.category')}>
                <option value="A">A</option>
              </Select>
            </FormControl>
          )}
        </SimpleGrid>
      </Box>
    </Stack>
  );
};

export default DeviceForm;
