import React, { useEffect, useState } from 'react';
import { Box, Button, Checkbox, Divider, Flex, Stack, useColorModeValue, useToast } from '@chakra-ui/react';
import StepperComponent from '../../../../../components/Stepper/Stepper';
import ConfigForm from '../forms/ConfigForm';
import DeviceForm from '../forms/DeviceForm';
import { useNavigate } from 'react-router-dom';
import { FormValues } from '../../../../../types/duCuManager.type';
import { useForm, FormProvider, useWatch } from 'react-hook-form';
import InstallationForm from '../forms/InstallationForm';
import { installationFormSchema } from '../forms/schema';
import { zodResolver } from '@hookform/resolvers/zod';
import { createCBSD } from '../../../../../services/orchestrator';
import { useMutation } from '@tanstack/react-query';
import { useGetCluster, useGetRuManifest } from '../../../hooks/services/use_GetCBSD';
import Loader from '../../../../../components/loader/Loader';
import QueryError from '../../../../../components/errorComponents/QueryError';
import { formatData } from '../../helper';

const CreateCBSDForm = () => {
  const [activeStep, setActiveStep] = useState(0);
  const navigate = useNavigate();
  const boxShadow = useColorModeValue('sm', 'sm-dark');
  const [isDeviceFormValid, setIsDeviceFormValid] = useState(false);
  const toast = useToast();
  const [cpiSigned, setCpiSigned] = useState(false);
  const steps = [
    { title: 'Device', description: '' },
    { title: 'Config', description: '' },
    { title: 'Installation', description: '' },
  ];

  const methods = useForm<FormValues>({
    defaultValues: {
      deviceFormData: {
        displayName: '',
        serialNumber: '',
        macAddress: '',
        fccId: '',
        clusterId: '',
        userId: 'DenseAir',
        category: 'A',
      },
      configFormData: {
        vendor: '',
        model: '',
        softwareVersion: '',
        hardwareVersion: '',
        firmwareVersion: '',
        radioTechnology: 'NR',
        supportedSpec: '',
      },
    },
  });

  const installationMethods = useForm<FormValues['installationFormData']>({
    resolver: zodResolver(installationFormSchema),
    mode: 'onChange',
    defaultValues: {
      latitude: null,
      longitude: null,
      height: null,
      heightType: 'HEIGHT_TYPE_AGL',
      horizontalAccuracy: null,
      verticalAccuracy: null,
      indoorDeployment: false,
      antennaModel: '',
      eirpCapability: null,
      antennaBeamwidth: null,
      antennaAzimuth: null,
      antennaDowntilt: null,
      antennaGain: null,
    },
  });

  const deviceFormData = useWatch({ control: methods.control, name: 'deviceFormData' });

  useEffect(() => {
    const isDeviceFormDataValid: boolean = Object.values(deviceFormData).every((field) => field !== '');
    setIsDeviceFormValid(isDeviceFormDataValid);
  }, [deviceFormData]);

  const { mutateAsync: CBSDMutation, isLoading: isCBSDPosting } = useMutation({
    mutationFn: createCBSD,
  });

  const { isLoading: isClusterLoading, error: clusterError, data: clustersData } = useGetCluster();
  const { isLoading: isRuLoading, error: ruError, data: ruData } = useGetRuManifest();
  const error = clusterError || ruError;
  if (isClusterLoading || isRuLoading) return <Loader />;
  if (error) return <QueryError error={error} />;

  const handleNext = () => {
    if (activeStep < steps.length - 1) {
      setActiveStep(activeStep + 1);
    }
  };

  const handlePrevious = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
    }
  };

  const handleFormSubmit = async () => {
    const isMethodsValid = await methods.trigger();

    if (isMethodsValid) {
      const mainFormData = methods.getValues();
      const installationFormData = installationMethods.getValues();
      const combinedData = { ...mainFormData, installationFormData };
      const formattedData = formatData(combinedData);

      await CBSDMutation(
        { cbsd: formattedData, cpiSigned },
        {
          onSuccess: () => {
            toast({
              title: 'CBSD created.',
              description: 'The CBSD has been successfully created.',
              status: 'success',
              duration: 5000,
              isClosable: true,
              position: 'top',
            });
            navigate('/oran-du-cu-manager/cbrs-management/cbsd');
          },
          onError: () => {
            toast({
              title: 'Error.',
              description: 'There was an error creating the CBSD.',
              status: 'error',
              duration: 5000,
              isClosable: true,
              position: 'top',
            });
          },
        }
      );
    }
  };

  const getChildComponent = (activeStep: number) => {
    switch (activeStep) {
      case 0:
        return <DeviceForm clustersData={clustersData} ruData={ruData} />;
      case 1:
        return <ConfigForm />;
      case 2:
        return (
          <FormProvider {...installationMethods}>
            <InstallationForm />
          </FormProvider>
        );
    }
  };

  return (
    <Box
      marginX="auto"
      as="form"
      width="full"
      maxW={{
        lg: '8xl',
      }}
      onSubmit={(e) => {
        e.preventDefault();
        handleFormSubmit();
      }}
      p="12"
    >
      <FormProvider {...methods}>
        <StepperComponent
          colorScheme="teal"
          size="lg"
          steps={steps}
          activeStep={activeStep}
          setActiveStep={setActiveStep}
        />
        <Stack
          spacing={{
            base: '5',
            lg: '6',
          }}
          padding="8"
        >
          <Box
            boxShadow={{
              base: 'none',
              md: boxShadow,
            }}
            borderRadius="lg"
            borderWidth="1px"
            padding="6"
          >
            {getChildComponent(activeStep)}
            <Flex justifyContent="space-between">
              <Button
                onClick={handlePrevious}
                variant="outline"
                colorScheme="brand.600"
                isDisabled={activeStep === 0}
                mr="2"
              >
                Previous
              </Button>
              <Button
                onClick={handleNext}
                variant="outline"
                colorScheme="brand.600"
                isDisabled={activeStep === steps.length - 1}
              >
                Next
              </Button>
            </Flex>
          </Box>
        </Stack>
        <Divider />
        <Flex
          direction="row-reverse"
          justifyContent="space-between"
          py="4"
          px={{
            base: '4',
            md: '6',
          }}
        >
          <Button
            data-testid="create-cbsd"
            colorScheme="teal"
            type="submit"
            isLoading={isCBSDPosting}
            loadingText="Creating..."
            isDisabled={!isDeviceFormValid}
          >
            Create
          </Button>
          <Checkbox isChecked={cpiSigned} onChange={(e) => setCpiSigned(e.target.checked)} colorScheme="teal">
            CPI Sign device
          </Checkbox>
          <Button
            colorScheme="teal"
            variant="outline"
            onClick={() => navigate('/oran-du-cu-manager/cbrs-management/cbsd')}
          >
            Cancel
          </Button>
        </Flex>
      </FormProvider>
    </Box>
  );
};

export default CreateCBSDForm;
