import React, { useMemo } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { CBSDHeader } from '../../../../types/duCuManager.type';
import { MdCheckCircle, MdHighlightOff } from 'react-icons/md';
import CBSDConfigMenu from './CBSDConfigMenu';
import useLogin from '../../../../hooks/useLogin';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../../data/constants';

export default function useCBSDColumns() {
  const getStatusIcon = (status: string) =>
    status === 'true' ? <MdCheckCircle size="25" color="green" /> : <MdHighlightOff size="25" color="red" />;

  const { checkNmsDevAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkNmsDevAccess(READ_WRITE_ACCESS_ROLES);

  return useMemo<ColumnDef<CBSDHeader>[]>(
    () => [
      {
        header: 'Name',
        accessorKey: 'displayName',
        id: 'displayName',
      },
      {
        header: 'CBRS Id',
        accessorKey: 'cbrsId',
        id: 'cbrsId',
        cell: ({ row }) => {
          const isRegistered = row.original.registered === 'true';
          const cbrsId = row.original.cbrsId;
          return isRegistered ? cbrsId : 'Not registered';
        },
      },
      {
        header: 'FCC ID',
        accessorKey: 'fccId',
        id: 'fccId',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Serial',
        accessorKey: 'serial',
        id: 'serial',
      },
      {
        header: 'Registered',
        accessorKey: 'registered',
        id: 'registered',
        cell: ({ getValue }) => getStatusIcon(getValue() as string),
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Seek Grant',
        accessorKey: 'grant',
        id: 'grant',
        cell: ({ getValue }) => getStatusIcon(getValue() as string),
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Heartbeat',
        accessorKey: 'heartbeat',
        id: 'heartbeat',
        cell: ({ getValue }) => getStatusIcon(getValue() as string),
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: '',
        accessorKey: 'state',
        id: 'state',
        enableSorting: false,
        cell: ({ row }) => (
          <>
            {checkRoleAccess && (
              <CBSDConfigMenu
                data={row}
                state={row.original.state as string}
                serial={row.original.serial as string}
                grant={row.original.grant}
              />
            )}
          </>
        ),
      },
    ],
    []
  );
}
