import { FormValues } from '../../../types/duCuManager.type';

const toNumberOrZero = (value: any): number => {
  return value == null || value === '' ? 0 : Number(value);
};

export const formatData = (data: FormValues) => {
  return {
    clusterId: data.deviceFormData.clusterId,
    serialNumber: data.deviceFormData.serialNumber,
    macAddress: data.deviceFormData.macAddress,
    fccId: data.deviceFormData.fccId,
    category: data.deviceFormData.category,
    installationParam: {
      latitude: toNumberOrZero(data.installationFormData.latitude),
      longitude: toNumberOrZero(data.installationFormData.longitude),
      height: toNumberOrZero(data.installationFormData.height),
      horizontalAccuracy: toNumberOrZero(data.installationFormData.horizontalAccuracy),
      verticalAccuracy: toNumberOrZero(data.installationFormData.verticalAccuracy),
      indoorDeployment: data.installationFormData.indoorDeployment,
      antennaAzimuth: toNumberOrZero(data.installationFormData.antennaAzimuth),
      antennaDowntilt: toNumberOrZero(data.installationFormData.antennaDowntilt),
      antennaGain: toNumberOrZero(data.installationFormData.antennaGain),
      eirpCapability: toNumberOrZero(data.installationFormData.eirpCapability),
      antennaBeamwidth: toNumberOrZero(data.installationFormData.antennaBeamwidth),
      antennaModel: data.installationFormData.antennaModel,
      heightType: data.installationFormData.heightType,
    },
    cbsdInfo: {
      vendor: data.configFormData.vendor,
      model: data.configFormData.model,
      softwareVersion: data.configFormData.softwareVersion,
      hardwareVersion: data.configFormData.hardwareVersion,
      firmwareVersion: data.configFormData.firmwareVersion,
    },
    airInterface: {
      radioTechnology: data.configFormData.radioTechnology,
      supportedSpec: data.configFormData.supportedSpec,
    },
    displayName: data.deviceFormData.displayName,
    userId: data.deviceFormData.userId,
  };
};
