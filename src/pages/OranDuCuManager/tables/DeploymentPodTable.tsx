import { Box, Button, Heading, Stack, Text } from '@chakra-ui/react';
import { ColumnDef } from '@tanstack/react-table';
import React, { useEffect, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import {
  ErrorBoundaryFallback,
  ErrorBoundaryLogError,
} from '../../../components/errorComponents/ErrorBoundaryFallback';
import QueryError from '../../../components/errorComponents/QueryError';
import StatusComponent from '../../../components/icons/StatusIcon';
import Loader from '../../../components/loader/Loader';
import { getStatusColor } from '../../CellOverview/hooks/useStatus';
import { DataTable } from '../../MetricsCollector/components/DataTable';
import usePods from '../hooks/services/usePods';

type DeploymentPodTableConfigMenuProps = {
  selectedOption: any;
  colorModeValue: string;
  showTable: boolean;
};

const DeploymentPodTable = ({ selectedOption, colorModeValue, showTable }: DeploymentPodTableConfigMenuProps) => {
  const columns = React.useMemo<ColumnDef<any>[]>(
    () => [
      {
        header: 'Name',
        accessorKey: 'name',
        id: 'tableClusterList',
      },
      {
        header: 'IP',
        accessorKey: 'ip',
        id: 'ip',
      },
      {
        header: 'Site',
        accessorKey: 'site',
        id: 'site',
      },
      {
        header: 'Status',
        accessorKey: 'status.status',
        id: 'status',
        cell: (props) => {
          const cellStatus = props.row.original.status.status as string;

          return (
            <>
              <StatusComponent
                dataTestId="cell-main-table-status-icon"
                boxSize="sm"
                color={getStatusColor(cellStatus)}
                status={cellStatus}
              />
            </>
          );
        },
      },
    ],
    []
  );

  // API Calls
  const { getDeploymentPods } = usePods();

  // Drives cluster table
  const {
    data: DeploymentPodsData,
    isLoading: isDeploymentPodsDataLoading,
    error: DeploymentPodsDataError,
    isFetching: isDeploymentPodsDataFetching,
  } = getDeploymentPods(selectedOption?.deployment_name, showTable);

  const flattenedData = React.useMemo(() => {
    if (DeploymentPodsData) {
      return [...DeploymentPodsData.du, ...DeploymentPodsData.cuup, ...DeploymentPodsData.cucp];
    }
    return [];
  }, [DeploymentPodsData]);

  if (DeploymentPodsDataError) return <QueryError error={DeploymentPodsDataError} />;

  return (
    <Box mt="16" p="8" boxShadow={colorModeValue} borderRadius="xl" data-testid="pod-table">
      <Heading textAlign="center" as="h2" fontSize="20px" mb="8">
        Pods for deployment {selectedOption?.deployment_name}
      </Heading>

      {showTable && (isDeploymentPodsDataLoading || isDeploymentPodsDataFetching) ? (
        <Loader />
      ) : (
        <Box>
          {/* empty‐state */}
          {flattenedData.length === 0 ? (
            <Stack spacing={4} align="center" py={8}>
              <Text textAlign="center" color="gray.500">
                No pods found for this deployment. Please activate the custom resources below.
              </Text>
              <Button
                variant="primary"
                onClick={() => {
                  window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
                }}
              >
                Activate Custom Resources
              </Button>
            </Stack>
          ) : (
            <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
              <DataTable
                columns={columns}
                data={flattenedData}
                isExpandable={false}
                defaultPageSize={50}
                hasEmptyResult={false}
                size="sm"
              />
            </ErrorBoundary>
          )}
        </Box>
      )}
    </Box>
  );
};

export default DeploymentPodTable;
