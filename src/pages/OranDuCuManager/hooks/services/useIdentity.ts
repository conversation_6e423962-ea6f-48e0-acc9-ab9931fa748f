import { useToast } from '@chakra-ui/react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getIdentities, PayloadData, postIdentity } from '../../../../services/duCuManager';

export default function useIdentity() {
  return {
    createIdentities: useCreateIdentities,
    getIdentitiesList: useIdentitiesList,
  };
}

const useIdentitiesList = (payloadData: string, enableQuery: boolean) => {
  const { data, error, isLoading, refetch, isFetching } = useQuery(
    ['interfacesList', payloadData],
    () => getIdentities(payloadData),
    {
      enabled: enableQuery,
    }
  );
  return { data, error, isLoading, refetch, isFetching };
};

const useCreateIdentities = () => {
  const queryClient = useQueryClient();
  const toast = useToast();
  return useMutation({
    mutationFn: (simpleViewIdentityData: any) => postIdentity(simpleViewIdentityData),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['identityPost'] });
      toast({
        title: 'Identities created.',
        description: 'Identities have been created successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      console.error('--- API error --- :', error);
      toast({
        title: `Error ${error}`,
        description: 'Something went wrong with the Identities creation.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
};
