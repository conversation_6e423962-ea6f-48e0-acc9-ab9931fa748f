import { useToast } from '@chakra-ui/react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import {
  deleteAllCustomResourceForSite,
  deleteCustomResource,
  getAllDeployments,
  getCluster,
  getCustomResource,
  getCustomResourceBySite,
  getCustomResourcePath,
  getDeploymentCustomResources,
  getSingleCustomResource,
  patchCustomResourceUnnamed,
  postConfigCustomResource,
  postCreateCustomResourceForSite,
  postDeploymentCustomResource,
} from '../../../../services/duCuManager';
import { SelectedOption } from '../../advancedView/CustomResources';

type CR = {
  name: string;
  created: string;
  kind: string;
  enabled: string;
  site: string;
};

type CustomResource = {
  sites: string[];
  crs: CR[];
};

const useCustomResource = () => {
  return {
    getClusterList: useClusterList,
    getAllDeployments: useGetAllDeployments,
    getDeploymentCustomResources: useGetDeploymentCustomResources,
    getCustomResourceBySite: useCustomResourceBySite,
    getCustomResourceList: useCustomResourceList,
    getSingleCustomResource: useGetSingleCustomResource,
    getCustomResourcePath: useGetCustomResourcePath,
    patchCustomResourceUnnamed: usePatchCustomResourceUnnamed,
    createConfigCustomResource: useCreateConfigCustomResource,
    createDeploymentCustomResource: useCreateDeploymentCustomResource,
    deleteCustomResource: useDeleteCustomResource,
    createCustomResourcesForSite: useCreateCustomResourcesForSite,
    deleteAllCustomResourceForSite: useDeleteAllCustomResourceForSite,
  };
};

const useClusterList = () => {
  const { data, error, isLoading } = useQuery(['clusterListCRs'], getCluster, {
    enabled: true,
  });

  return { data, error, isLoading };
};

const useGetAllDeployments = (deployment_type: string | undefined) => {
  const { data, error, isLoading } = useQuery(['AllDeployments'], getAllDeployments, {
    enabled: deployment_type === 'new' ? false : true,
  });

  return { data, error, isLoading };
};

const useGetDeploymentCustomResources = (deployment_name: string, isReady: boolean) => {
  const { data, error, isLoading, isFetching, refetch } = useQuery(
    ['GetDeploymentCustomResources', deployment_name],
    () => getDeploymentCustomResources(deployment_name),
    {
      enabled: !!deployment_name && isReady,
    }
  );

  return { data, error, isLoading, isFetching, refetch };
};

const useCustomResourceBySite = (payloadData: SelectedOption) => {
  const { data, error, isLoading, refetch } = useQuery(
    ['customResourceBySite', payloadData],
    () => getCustomResourceBySite(payloadData),
    {
      enabled: !!payloadData?.cluster,
    }
  );

  return { data, error, isLoading, refetch };
};

const useCustomResourceList = (cluster_id: number | null) => {
  const { data, error, isLoading } = useQuery(['customResourceList', cluster_id], () => getCustomResource(cluster_id), {
    enabled: cluster_id !== null,
  });

  return { data, error, isLoading };
};

const useGetSingleCustomResource = (cluster_id: number | null, assetName: string, isQueryEnabled: boolean) => {
  const { data, error, isLoading } = useQuery(
    ['singleCustomResourceList', cluster_id, assetName],
    () => getSingleCustomResource(cluster_id, assetName),
    {
      enabled: isQueryEnabled,
    }
  );

  return { data, error, isLoading };
};

const useGetCustomResourcePath = (
  cluster_id: number | null,
  cr_name: string | undefined,
  path_str: string,
  isQueryEnabled: boolean
) => {
  const { data, error, isLoading } = useQuery(
    ['customResourcePath', cluster_id, cr_name, path_str],
    () => getCustomResourcePath(cluster_id, cr_name, path_str),
    {
      enabled: isQueryEnabled,
    }
  );

  return { data, error, isLoading };
};

const usePatchCustomResourceUnnamed = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: async (payloadData: any) => patchCustomResourceUnnamed(payloadData),
    onSuccess: (data, variables) => {
      console.log('Query invalidated. Refetching...');
      queryClient.invalidateQueries({ queryKey: ['customResourcePath'] });
      toast({
        title: 'Custom resource parameter created.',
        description: 'Custom resource parameter has been updated successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      toast({
        title: `Error ${error}`,
        description: 'Something went wrong with the custom resource parameter update.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
};

const useCreateConfigCustomResource = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const toast = useToast();
  return useMutation({
    mutationFn: (payloadData: any) => postConfigCustomResource(payloadData),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['clusterListCRs'] });
      toast({
        title: 'Custom resources created.',
        description: 'Custom resources have been created successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
      navigate('/oran-du-cu-manager/advanced-view/custom-resources', {
        state: { payloadData: variables },
      });
    },
    onError: (error) => {
      toast({
        title: `Error ${error}`,
        description: 'Something went wrong with the custom resources creation.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
};

const useCreateDeploymentCustomResource = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const toast = useToast();
  return useMutation({
    mutationFn: (payloadData: any) => postDeploymentCustomResource(payloadData),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['clusterListCRs'] });
      toast({
        title: 'Deployment custom resources created.',
        description: 'Deployment custom resources have been created successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
      navigate('/oran-du-cu-manager/advanced-view/custom-resources', {
        state: { payloadData: variables },
      });
    },
    onError: (error) => {
      console.error('--- API error --- :', error);
      toast({
        title: `Error ${error}`,
        description: 'Something went wrong with the deployment custom resources creation.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
};

const useDeleteCustomResource = () => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (payloadData: any) => deleteCustomResource(payloadData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customResourceList'] });
      queryClient.invalidateQueries({ queryKey: ['AllDeployments'] });
      queryClient.invalidateQueries({ queryKey: ['customResourceBySite'] });
      queryClient.invalidateQueries({ queryKey: ['podList'] });
      toast({
        title: 'Custom resources deleted.',
        description: 'Custom resources has been deleted successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      toast({
        title: `Error ${error}`,
        description: 'Something went wrong with the Custom resources deletion.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
};

const useCreateCustomResourcesForSite = () => {
  const queryClient = useQueryClient();
  const toast = useToast();
  return useMutation({
    mutationFn: (payloadData: any) => postCreateCustomResourceForSite(payloadData),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['customResourceList'] });
      queryClient.invalidateQueries({ queryKey: ['AllDeployments'] });
      queryClient.invalidateQueries({ queryKey: ['GetDeploymentCustomResources'] });
      toast({
        title: 'Deployment custom resources created.',
        description: 'Deployment custom resources has been created successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      console.error('--- API error --- :', error);
      toast({
        title: `Error ${error}`,
        description: 'Something went wrong with the deployment and custom resources creation.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
      throw error;
    },
  });
};

const useDeleteAllCustomResourceForSite = () => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (payloadData: any) => deleteAllCustomResourceForSite(payloadData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['GetDeploymentPods'] });
      queryClient.invalidateQueries({ queryKey: ['AllDeployments'] });
      queryClient.invalidateQueries({ queryKey: ['GetDeploymentCustomResources'] });
      queryClient.invalidateQueries({ queryKey: ['podList'] });
      toast({
        title: 'Custom resources deleted for the deployment.',
        description: 'Custom resources has been deleted successfully.',
        status: 'success',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      toast({
        title: `Error ${error}`,
        description: 'Something went wrong with the Custom resource deletion for the deployment.',
        status: 'error',
        duration: 9000,
        isClosable: true,
        position: 'top',
      });
    },
  });
};

export default useCustomResource;
