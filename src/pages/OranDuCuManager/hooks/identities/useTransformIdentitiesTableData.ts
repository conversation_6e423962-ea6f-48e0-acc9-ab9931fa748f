type RawIdentityField = {
  name: string;
  default: string;
  user_setting?: string;
  target?: string;
};

interface OriginalIdentityGroup {
  [fieldKey: string]: RawIdentityField;
}

export function transformIdentitiesForApi(flatValues: Record<string, string>, selectedOption: any) {
  const isMultiCell = Number(selectedOption?.deployment_params?.max_cells) === 2;

  const deployment_name = selectedOption?.deployment_name ?? '';

  const sharedApi: any = {
    gNodeB: {
      id: Number(flatValues['shared-gnodeb_id-user_setting-0'] || 0),
    },
    PLMN: {
      MCC: String(flatValues['shared-mcc-user_setting-1'] || ''),
      MNC: String(flatValues['shared-mnc-user_setting-2'] || ''),
    },
    node_type: 'shared',
  };

  if (isMultiCell) {
    sharedApi.gNodeB2 = {
      id: String(flatValues['shared-gnodeb_id2-user_setting-3'] || 0),
    };
    sharedApi.PLMN2 = {
      MCC: String(flatValues['shared-mcc2-user_setting-4'] || ''),
      MNC: String(flatValues['shared-mnc2-user_setting-5'] || ''),
    };
  }

  const duApi: any = {
    tracking_area_code: Number(flatValues['du-tracking_area_code-user_setting-0'] || 0),
    ran_area_code: Number(flatValues['du-ran_area_code-user_setting-1'] || 0),
    physical_cell_id: Number(flatValues['du-physical_cell_id-user_setting-2'] || 0),
    node_type: 'du',
  };

  if (isMultiCell) {
    duApi.tracking_area_code2 = Number(flatValues['du-tracking_area_code2-user_setting-3'] || 0);
    duApi.ran_area_code2 = Number(flatValues['du-ran_area_code2-user_setting-4'] || 0);
    duApi.physical_cell_id2 = Number(flatValues['du-physical_cell_id2-user_setting-5'] || 0);
  }

  return {
    deployment_name,
    shared: sharedApi,
    du: duApi,
  };
}

function transformIdentitiesTableData(
  settings: Record<string, any>,
  selectedOption: any,
  originalData: {
    shared: OriginalIdentityGroup;
    du: OriginalIdentityGroup;
  },
  ruVendor: string[]
) {
  const isMultiCell = Number(selectedOption?.deployment_params?.max_cells) === 2;

  const nestedShared: Record<string, RawIdentityField> = {};

  // NOTE: originalData.shared keys: "gnodeb_id", "mcc", "gnodeb_id2", "mcc2", "mnc", "mnc2", "node_type"

  nestedShared.gnodeb_id = {
    name: originalData.shared.gnodeb_id.name,
    default: originalData.shared.gnodeb_id.default.toString(),

    user_setting:
      settings.shared?.gNodeB?.id?.toString() ?? originalData.shared.gnodeb_id.user_setting?.toString() ?? '',
    target: originalData.shared.gnodeb_id.target,
  };
  nestedShared.mcc = {
    name: originalData.shared.mcc.name,
    default: originalData.shared.mcc.default.toString(),

    user_setting: settings.shared?.PLMN?.MCC?.toString() ?? originalData.shared.mcc.user_setting?.toString() ?? '',
    target: originalData.shared.mcc.target,
  };
  nestedShared.mnc = {
    name: originalData.shared.mnc.name,
    default: originalData.shared.mnc.default.toString(),

    user_setting: settings.shared?.PLMN?.MNC?.toString() ?? originalData.shared.mnc.user_setting?.toString() ?? '',
    target: originalData.shared.mnc.target,
  };

  if (isMultiCell) {
    // TWO-CELL shared fields (“2” suffix):
    nestedShared.gnodeb_id2 = {
      name: originalData.shared.gnodeb_id2.name,
      default: originalData.shared.gnodeb_id2.default.toString(),

      user_setting:
        settings.shared?.gNodeB2?.id?.toString() ?? originalData.shared.gnodeb_id2?.user_setting?.toString() ?? '',
      target: originalData.shared.gnodeb_id2.target,
    };
    nestedShared.mcc2 = {
      name: originalData.shared.mcc2.name,
      default: originalData.shared.mcc2.default.toString(),

      user_setting: settings.shared?.PLMN2?.MCC?.toString() ?? originalData.shared.mmc2?.user_setting?.toString() ?? '',
      target: originalData.shared.mcc2.target,
    };
    nestedShared.mnc2 = {
      name: originalData.shared.mnc2.name,
      default: originalData.shared.mnc2.default.toString(),

      user_setting: settings.shared?.PLMN2?.MNC?.toString() ?? originalData.shared.mnc2?.user_setting?.toString() ?? '',
      target: originalData.shared.mnc2.target,
    };
  }

  const nestedDu: Record<string, RawIdentityField> = {};

  nestedDu.tracking_area_code = {
    name: originalData.du.tracking_area_code.name,
    default: originalData.du.tracking_area_code.default.toString(),

    user_setting:
      settings.du?.tracking_area_code?.toString() ??
      originalData.du?.tracking_area_code?.user_setting?.toString() ??
      '',
    target: originalData.du.tracking_area_code.target,
  };
  nestedDu.ran_area_code = {
    name: originalData.du.ran_area_code.name,
    default: originalData.du.ran_area_code.default.toString(),
    user_setting:
      settings.du?.ran_area_code?.toString() ?? originalData.du?.ran_area_code?.user_setting?.toString() ?? '',
    target: originalData.du.ran_area_code.target,
  };
  nestedDu.physical_cell_id = {
    name: originalData.du.physical_cell_id.name,
    default: originalData.du.physical_cell_id.default.toString(),

    user_setting:
      settings.du?.physical_cell_id?.toString() ?? originalData.du?.physical_cell_id.user_setting?.toString() ?? '',
    target: originalData.du.physical_cell_id.target,
  };

  if (isMultiCell) {
    nestedDu.tracking_area_code2 = {
      name: originalData.du.tracking_area_code2.name,
      default: originalData.du.tracking_area_code2.default.toString(),

      user_setting:
        settings.du?.tracking_area_code2?.toString() ??
        originalData.du?.tracking_area_code2?.user_setting?.toString() ??
        '',
      target: originalData.du.tracking_area_code2.target,
    };
    nestedDu.ran_area_code2 = {
      name: originalData.du.ran_area_code2.name,
      default: originalData.du.ran_area_code2.default.toString(),

      user_setting:
        settings.du?.ran_area_code2?.toString() ?? originalData.du?.ran_area_code2?.user_setting?.toString() ?? '',
      target: originalData.du.ran_area_code2.target,
    };
    nestedDu.physical_cell_id2 = {
      name: originalData.du.physical_cell_id2.name,
      default: originalData.du.physical_cell_id2.default.toString(),

      user_setting:
        settings.du?.physical_cell_id2?.toString() ??
        originalData.du?.physical_cell_id2?.user_setting?.toString() ??
        '',
      target: originalData.du.physical_cell_id2.target,
    };
  }

  const flatMapForRHF: Record<string, string> = {};

  const sharedKeyOrder: Array<keyof typeof originalData.shared> = [
    'gnodeb_id', // rowIndex 0
    'mcc', // rowIndex 1
    'mnc', // rowIndex 2
    'gnodeb_id2', // rowIndex 3
    'mcc2', // rowIndex 4
    'mnc2', // rowIndex 5
  ];

  sharedKeyOrder.forEach((fieldKey, index) => {
    if (!nestedShared[fieldKey]) return;
    flatMapForRHF[`shared-${fieldKey}-user_setting-${index}`] = nestedShared[fieldKey].user_setting?.toString() ?? '';
  });

  const duKeyOrder: Array<keyof typeof originalData.du> = [
    'tracking_area_code', // rowIndex 0
    'ran_area_code', // rowIndex 1
    'physical_cell_id', // rowIndex 2
    'tracking_area_code2', // rowIndex 3
    'ran_area_code2', // rowIndex 4
    'physical_cell_id2', // rowIndex 5
  ];

  duKeyOrder.forEach((fieldKey, index) => {
    if (!nestedDu[fieldKey]) return;
    flatMapForRHF[`du-${fieldKey}-user_setting-${index}`] = nestedDu[fieldKey].user_setting?.toString() ?? '';
  });

  return {
    nested: { shared: nestedShared, du: nestedDu },
    flatMapForRHF,
  };
}

export default transformIdentitiesTableData;
