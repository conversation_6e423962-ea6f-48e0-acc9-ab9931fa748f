export const fieldMappings: Record<GroupKeys, Record<string, string>> = {
  du: {
    0: 'fh_c.local_ip_address',
    1: 'fh_c.subnet_mask',
    2: 'fh_c.gateway_address',
    3: 'fh_u.local_ip_address',
    4: 'fh_u.gateway_address',
    5: 'f1_c.remote_address',
    6: 'f1_u.local_ip_address',
    7: 'f1_u.gateway_address',
    8: 'f1_c2.remote_address',
    9: 'f1_u2.local_ip_address',
    10: 'f1_u2.gateway_address',
  },
  cu_cp: {
    0: 'ng_c.remote_address',
    1: 'ng_c.gateway_address',
  },
  cu_cp2: {
    0: 'ng_c.remote_address',
    1: 'ng_c.gateway_address',
  },
  cu_up: {
    0: 'ng_u.local_ip_address',
    1: 'ng_u.gateway_address',
    2: 'f1_u.local_ip_address',
    3: 'f1_u.gateway_address',
    4: 'e1.remote_address',
    5: 'e1.local_ip_address',
  },
  cu_up2: {
    0: 'ng_u.local_ip_address',
    1: 'ng_u.gateway_address',
    2: 'f1_u.local_ip_address',
    3: 'f1_u.gateway_address',
    4: 'e1.remote_address',
    5: 'e1.local_ip_address',
  },
};

export const defaultInterfaceFields = {
  du: [
    { name: 'FH-C localIpAddress', default: '************', target: 'Ru' },
    { name: 'FH-C subNetMask', default: 24, target: 'Ru' },
    { name: 'FH-C gatewayAddress', default: '0.0.0.0', target: 'Fronthaul Gateway' },
    { name: 'FH-U localIpAddress', default: '************', target: 'Ru' },
    { name: 'FH-U gatewayAddress', default: '0.0.0.0', target: 'Fronthaul Gateway' },
    { name: 'F1-C remoteAddress (Cell 1)', default: '***********', target: 'CuCp' },
    { name: 'F1-U localIpAddress (Cell 1)', default: '***********', target: 'CuUp' },
    { name: 'F1-U gatewayAddress (Cell 1)', default: '0.0.0.0', target: 'Midhaul Gateway' },
  ],
  du_moran: [
    { name: 'F1-C remoteAddress (Cell 2)', default: '***********', target: 'CuCp2', moran: 'c2' },
    { name: 'F1-U localIpAddress (Cell 2)', default: '***********', target: 'CuUp2', moran: 'c2' },
    { name: 'F1-U gatewayAddress (Cell 2)', default: '0.0.0.0', target: 'Midhaul Gateway', moran: 'c2' },
  ],
  cu_cp: [
    { name: 'NgC remoteAddress', default: '***********', target: 'Core' },
    { name: 'NgC gatewayAddress', default: '0.0.0.0', target: 'Backhaul Gateway' },
  ],
  cu_up: [
    { name: 'NgU localIpAddress', default: '*******', target: 'Core' },
    { name: 'NgU gatewayAddress', default: '0.0.0.0', target: 'Backhaul Gateway' },
    { name: 'F1-U localIpAddress', default: '***********', target: 'Du' },
    { name: 'F1-U gatewayAddress', default: '0.0.0.0', target: 'Midhaul Gateway' },
    { name: 'E1 remoteAddress', default: '***********', target: 'CuCp' },
    { name: 'E1 localIpAddress', default: '***********', target: 'CuCp' },
  ],
  cu_cp2: [
    { name: 'NgC remoteAddress', default: '***********', target: 'Core2' },
    { name: 'NgC gatewayAddress', default: '0.0.0.0', target: 'Backhaul Gateway' },
  ],
  cu_up2: [
    { name: 'NgU localIpAddress', default: '*******', target: 'Core2' },
    { name: 'NgU gatewayAddress', default: '0.0.0.0', target: 'Backhaul Gateway' },
    { name: 'F1-U localIpAddress', default: '***********', target: 'Du' },
    { name: 'F1-U gatewayAddress', default: '0.0.0.0', target: 'Midhaul Gateway' },
    { name: 'E1 remoteAddress', default: '***********', target: 'CuCp2' },
    { name: 'E1 localIpAddress', default: '***********', target: 'CuCp2' },
  ],
};

type GroupKeys = 'du' | 'cu_cp' | 'cu_up' | 'cu_cp2' | 'cu_up2';

const transformInterfaceTableData = (settings: Record<string, any>, selectedOption: any) => {
  const setNestedValue = (obj: any, path: string[], value: any) => {
    let current = obj;
    for (let i = 0; i < path.length - 1; i++) {
      const key = path[i];
      if (!current[key]) {
        current[key] = {};
      }
      current = current[key];
    }
    current[path[path.length - 1]] = value;
  };

  const isTwoCellDeployment =
    selectedOption?.deployment_params?.max_cells === 2 ||
    (selectedOption?.cu2?.cucp_site_name2 && selectedOption?.cu2?.cuup_site_name2);

  const initialFinalFormData: any = {
    deployment_name: selectedOption?.deployment_name ?? null,
    version: 'v1beta1',
    du: {
      cluster_id: selectedOption?.du_cluster ?? 0,
      fh_c: { local_ip_address: '', gateway_address: '', subnet_mask: 0 },
      fh_u: { local_ip_address: '', gateway_address: '' },
      f1_c: { remote_address: '' },
      f1_u: { local_ip_address: '', gateway_address: '' },
      node_type: 'du',
    },
    cu_cp: {
      cluster_id: selectedOption?.cucp_cluster ?? 0,
      ng_c: { remote_address: '', gateway_address: '' },
      node_type: 'cu_cp',
    },
    cu_up: {
      cluster_id: selectedOption?.cuup_cluster ?? 0,
      f1_u: { local_ip_address: '', gateway_address: '' },
      ng_u: { local_ip_address: '', gateway_address: '' },
      e1: { remote_address: '', local_ip_address: '' },
      node_type: 'cu_up',
    },
  };

  if (isTwoCellDeployment) {
    initialFinalFormData.du.f1_c2 = { remote_address: '' };
    initialFinalFormData.du.f1_u2 = { local_ip_address: '', gateway_address: '' };

    // CUCP2:
    initialFinalFormData.cu_cp2 = {
      cluster_id: selectedOption?.cucp_cluster ?? 0,
      ng_c: { remote_address: '', gateway_address: '' },
      node_type: 'cu_cp',
    };

    // CUUP2:
    initialFinalFormData.cu_up2 = {
      cluster_id: selectedOption?.cuup_cluster ?? 0,
      f1_u: { local_ip_address: '', gateway_address: '' },
      ng_u: { local_ip_address: '', gateway_address: '' },
      e1: { remote_address: '', local_ip_address: '' },
      node_type: 'cu_up',
    };
  }

  Object.keys(settings).forEach((key) => {
    const lastDash = key.lastIndexOf('-');
    if (lastDash < 0) return;

    const indexStr = key.slice(lastDash + 1);
    const index = parseInt(indexStr, 10);
    if (isNaN(index)) return;

    const prefix = key.slice(0, lastDash);

    const firstDash = prefix.indexOf('-');
    if (firstDash < 0) return;

    const group = prefix.slice(0, firstDash) as GroupKeys;

    if (!isTwoCellDeployment && (group === 'cu_cp2' || group === 'cu_up2')) {
      return;
    }

    if (!isTwoCellDeployment && group === 'du' && (index === 8 || index === 9 || index === 10)) {
      return;
    }

    const mappingForThisGroup = fieldMappings[group];
    if (!mappingForThisGroup) return;

    const fieldPath = mappingForThisGroup[indexStr];
    if (!fieldPath) {
      return;
    }

    if (!isTwoCellDeployment && group === 'du' && (fieldPath.includes('f1_c2') || fieldPath.includes('f1_u2'))) {
      return;
    }

    let finalValue: any;
    const settingValue = settings[key];
    if (settingValue != null && typeof settingValue === 'object') {
      finalValue = settingValue.user_setting !== undefined ? settingValue.user_setting : settingValue.default;
    } else {
      finalValue = settingValue;
    }

    if (fieldPath === 'fh_c.subnet_mask') {
      finalValue = parseInt(finalValue, 10);
    }

    const pathSegments = fieldPath.split('.');
    if (!initialFinalFormData[group]) {
      return;
    }
    setNestedValue(initialFinalFormData[group], pathSegments, finalValue);
  });

  if (!isTwoCellDeployment) {
    if (initialFinalFormData.du) {
      delete initialFinalFormData.du.f1_c2;
      delete initialFinalFormData.du.f1_u2;
    }
    delete initialFinalFormData.cu_cp2;
    delete initialFinalFormData.cu_up2;
  }

  if (!isTwoCellDeployment) {
    delete initialFinalFormData.cu_cp;
    delete initialFinalFormData.cu_up;
  }

  return initialFinalFormData;
};

export default transformInterfaceTableData;
