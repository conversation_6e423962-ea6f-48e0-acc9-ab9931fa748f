import { useColorModeValue } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../../components/errorComponents/ErrorBoundaryFallback';
import Loader from '../../components/loader/Loader';
import { Node } from '../../types/InventoryManager.type';
import { DataTable } from '../MetricsCollector/components/DataTable';
import useGetNodeList from './hooks/services/use_Inv_GetNodeList';
import useNodeColumns from './hooks/useNodeColumns';
import NodeComponents from './NodeComponents';
import useMCActiveAlarms from './hooks/services/use_mc_ActiveAlarms';
import { isEmpty } from 'lodash';
import { LIFE_CYCLE } from '../../data/constants';
type NodesProps = {
  alternativeViewId?: string;
  urlNodeId?: string;
  lifecycle?: LIFE_CYCLE[] | null;
};

const Nodes = ({ alternativeViewId, urlNodeId, lifecycle }: NodesProps) => {
  const [updatedNodes, setUpdatedNodes] = useState<Node[]>([]);
  const [nodeLimit, setNodeLimit] = useState<number>(1000);

  const [nodeIds, setNodeIds] = useState<string[]>([]);
  const { status, isLoading, isFetching, error, data } = useGetNodeList(undefined, undefined, nodeLimit, lifecycle);
  const nodeColumns = useNodeColumns();

  const {
    isLoading: activeAlarmsIsLoading,
    error: ActiveAlarmsError,
    data: activeAlarms,
  } = useMCActiveAlarms(undefined, nodeIds);

  const mergeAlarmsIntoNodesData = (nodesData: any, alarmData: any) => {
    const updatedNodesData = nodesData?.map((cell: any) => {
      const alarmDetails = alarmData?.node_ids[cell?.node_id] || {};
      return {
        ...cell,
        node_alarms: ActiveAlarmsError ? { error: ActiveAlarmsError } : alarmDetails,
      };
    });

    return updatedNodesData;
  };

  useEffect(() => {
    if (!isEmpty(data)) {
      setUpdatedNodes(data);
      setNodeIds(data.map((item: Node) => item.node_id));
    }

    if (!isEmpty(data) && !isEmpty(activeAlarms)) {
      const updatedNodesData = mergeAlarmsIntoNodesData(data, activeAlarms);
      setUpdatedNodes(updatedNodesData);
    }
  }, [data, activeAlarms]);

  const renderNodeComponents = (props: any) => {
    return (
      <NodeComponents row={props} node={data} nodeOpen={true} caller="Nodes" nodeType={props.row.original.node_type} />
    );
  };

  if (isLoading) return <Loader />;

  return updatedNodes ? (
    <>
      <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
        <DataTable
          isExpandable={true}
          enableFilter={true}
          columns={nodeColumns ?? []}
          data={updatedNodes}
          isLoading={isLoading && isFetching}
          defaultPageSize={100}
          renderSubComponent={(props) => renderNodeComponents(props)}
          alternativeViewId={alternativeViewId}
          urlNodeId={urlNodeId}
          limit={nodeLimit}
          setLimit={setNodeLimit}
          count={updatedNodes?.length}
          hasEmptyResult={updatedNodes?.length == 0}
          version={'v2'}
        />
      </ErrorBoundary>
    </>
  ) : null;
};

export default Nodes;
