import {
  Avatar,
  AvatarGroup,
  Box,
  Button,
  Flex,
  <PERSON>ack,
  Table,
  Tbody,
  Td,
  Th,
  The<PERSON>,
  <PERSON><PERSON><PERSON>,
  Tr,
  Divider,
} from '@chakra-ui/react';
import QueryError from '../../components/errorComponents/QueryError';
import Loader from '../../components/loader/Loader';
import { Country, Status, StatusKeys } from '../../types/InventoryManager.type';
import { getStatusColor } from './hooks/useStatus';
import useCountryList from '../SiteManager/hooks/useCountry';
import { useQueries, UseQueryResult } from '@tanstack/react-query';
import { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon } from '@chakra-ui/icons';
import { getRegionStatsList } from '../../services/inventoryManager';
import { LIFE_CYCLE } from '../../data/constants';

const CellSummary = ({
  setSelectedFilter,
  lifecycle,
}: {
  setSelectedFilter: React.Dispatch<React.SetStateAction<Record<string, string> | undefined>>;
  lifecycle?: LIFE_CYCLE[] | null;
}) => {
  const countryCodes = ['GBR', 'USA'];
  const [showDetails, setShowDetails] = useState<{ [countryCode: string]: boolean }>({});

  // Fetch the country list
  const {
    isLoading: countryListLoading,
    error: countryListError,
    data: countryList = [],
  } = useCountryList() as UseQueryResult<Country[], Error>;

  // Map country codes to country names
  const countryCodeToName: Record<string, string> = countryList.reduce((acc, country) => {
    acc[country.country_code] = country.country_name;
    return acc;
  }, {} as Record<string, string>);

  // Fetch region stats for each country using useQueries
  const regionStatsQueries = useQueries({
    queries: countryCodes.map((countryCode) => ({
      queryKey: ['regionStatsList', countryCode, lifecycle],
      queryFn: () => getRegionStatsList(countryCode, lifecycle),
      refetchInterval: 30000,
      retry: 2,
    })),
  });

  // Check if any query is loading or has an error
  const isAnyLoading = regionStatsQueries.some((query) => query.isLoading);
  const isAnyError = regionStatsQueries.some((query) => query.error);

  if (countryListLoading || isAnyLoading) return <Loader />;
  if (countryListError || isAnyError)
    return <QueryError error={countryListError || (regionStatsQueries.find((q) => q.error)?.error as any)} />;

  const regionStatsListByCountry = regionStatsQueries.map((query, index) => ({
    countryCode: countryCodes[index],
    data: query.data || [],
  }));

  const statusCategories: { [key: string]: StatusKeys } = {
    CRITICAL: 'critical_count',
    ERROR: 'error_count',
    WARNING: 'warning_count',
    OK: 'ok_count',
    SHUTDOWN: 'shutdown_count',
    UNKNOWN: 'unknown_count',
  };

  const TooltipAvatar: typeof Avatar = ({ name, src, ...props }) => (
    <Tooltip label={src}>
      <Avatar getInitials={(name) => name} name={name} {...props}></Avatar>
    </Tooltip>
  );

  // Function to calculate totals per country
  const calculateTotals = (data: any[]) => {
    const totals: Record<Status, number> = {
      CRITICAL: 0,
      ERROR: 0,
      WARNING: 0,
      OK: 0,
      SHUTDOWN: 0,
      UNKNOWN: 0,
    };

    data.forEach((item: any) => {
      totals.CRITICAL += item.critical_count;
      totals.ERROR += item.error_count;
      totals.WARNING += item.warning_count;
      totals.OK += item.ok_count;
      totals.SHUTDOWN += item.shutdown_count;
      totals.UNKNOWN += item.unknown_count;
    });
    return totals;
  };

  return (
    <Stack
      data-testid={`cell_summary`}
      width="100%"
      direction={{
        base: 'column',
        lg: 'row',
      }}
      shadow={{
        md: 'base',
      }}
      p="1"
      bg="bg-surface"
      display="flex"
      flexDirection="column"
      spacing={{
        base: '1',
        lg: '2',
      }}
      rounded="1"
    >
      {regionStatsListByCountry.map(({ countryCode, data }, index) => {
        const countryName = countryCodeToName[countryCode] || countryCode;
        const totalCounts = calculateTotals(data);
        const sortedRegions = data.sort((a: any, b: any) => a.region_name.localeCompare(b.region_name));

        const showMoreDetailsBtn = Object.values(totalCounts).some((count) => count > 0);

        return (
          <Box key={countryCode}>
            <Stack padding="2">
              <Flex justifyContent="space-between" alignItems="center">
                <Box data-testid={`cell_summary_country_${countryCode}`}>
                  <Box fontSize="lg" fontWeight="bold">
                    {countryName}
                  </Box>
                </Box>
                <Flex alignItems="center" gap="4">
                  <Box data-testid={`cell_summary_status_group_box_${countryCode}`}>
                    <AvatarGroup size="md" max={6} data-testid={`cell_summary_status_group_${countryCode}`}>
                      {Object.keys(totalCounts).map((status, index) => {
                        const key = status as Status;
                        const count = totalCounts[key].toString();
                        return (
                          <TooltipAvatar
                            data-testid={`cell_summary_status_${countryCode}_${status}`}
                            key={index}
                            src={status}
                            name={count}
                            backgroundColor={getStatusColor(status)}
                            fontSize="xs"
                          />
                        );
                      })}
                    </AvatarGroup>
                  </Box>
                  <Button
                    visibility={showMoreDetailsBtn ? 'visible' : 'hidden'}
                    pointerEvents={showMoreDetailsBtn ? 'auto' : 'none'}
                    onClick={() => setShowDetails((prev) => ({ ...prev, [countryCode]: !prev[countryCode] }))}
                    data-testid={`cell_summary_details_button_${countryCode}`}
                  >
                    {showDetails[countryCode] ? (
                      <>
                        Hide Details <ChevronUpIcon boxSize="5" />
                      </>
                    ) : (
                      <>
                        Show Details <ChevronDownIcon boxSize="5" />
                      </>
                    )}
                  </Button>
                </Flex>
              </Flex>
              {showDetails[countryCode] && (
                <Stack
                  p="8"
                  bg="bg-surface"
                  spacing={{
                    base: '5',
                    lg: '6',
                  }}
                  data-testid={`cell_summary_status_region_table_${countryCode}`}
                >
                  <Table
                    shadow={{
                      md: 'base',
                    }}
                    variant="simple"
                  >
                    <Thead>
                      <Tr>
                        <Th
                          width="6rem"
                          whiteSpace="normal"
                          data-testid="cell_summary_status_region_table_header"
                          wordBreak="break-word"
                        >
                          {'Regions / Status'}
                        </Th>
                        {Object.keys(statusCategories).map((status) => (
                          <Th
                            data-testid={`cell_summary_status_region_table_header_${status}`}
                            width="7rem"
                            key={status}
                          >
                            {status}
                          </Th>
                        ))}
                      </Tr>
                    </Thead>
                    <Tbody>
                      {sortedRegions.map((region: any) => (
                        <Tr key={region.region_id}>
                          <Td
                            cursor="pointer"
                            onClick={() => {
                              setSelectedFilter({ region_name: region.region_name });
                              setShowDetails((prev) => ({ ...prev, [countryCode]: false }));
                            }}
                          >
                            {region.region_name}
                          </Td>
                          {Object.entries(statusCategories).map(([status, key]) => (
                            <Td alignItems="center" key={status}>
                              <Box
                                as="span"
                                cursor={region[key]! > 0 ? 'pointer' : 'default'}
                                backgroundColor={region[key]! > 0 ? getStatusColor(status) : 'gray.300'}
                                color="white"
                                width="24px"
                                display="flex"
                                justifyContent="center"
                                padding="2px 5px"
                                borderRadius="5px"
                                data-testid={`cell_summary_status_region_table_cell_${region[key]}`}
                                onClick={() => {
                                  if (region[key]! > 0) {
                                    setSelectedFilter({ region_name: region.region_name, status });
                                    setShowDetails((prev) => ({ ...prev, [countryCode]: false }));
                                  }
                                }}
                              >
                                {region[key] ?? 0}
                              </Box>
                            </Td>
                          ))}
                        </Tr>
                      ))}
                    </Tbody>
                  </Table>
                </Stack>
              )}
            </Stack>
            {index < regionStatsListByCountry.length - 1 && <Divider />}
          </Box>
        );
      })}
    </Stack>
  );
};

export default CellSummary;
