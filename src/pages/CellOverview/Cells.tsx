import { FC, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../../components/errorComponents/ErrorBoundaryFallback';
import QueryError from '../../components/errorComponents/QueryError';
import Loader from '../../components/loader/Loader';
import { LIFE_CYCLE } from '../../data/constants';
import { DataTable } from '../MetricsCollector/components/DataTable';
import CellNodes from './CellNodes';
import useGetCellList from './hooks/services/use_Inv_GetCellList';
import useMCActiveAlarms from './hooks/services/use_mc_ActiveAlarms';
import useCellColumns from './hooks/useCellColumns';

const RenderNodes = ({ row }: any) => {
  return <CellNodes row={row} />;
};

type CellsProps = {
  alternativeViewId: string;
  urlCellRef?: string;
  selectedFilter?: Record<string, string>;
  lifecycle?: LIFE_CYCLE[] | null;
};

const Cells: FC<CellsProps> = ({ alternativeViewId, urlCellRef, selectedFilter, lifecycle }) => {
  const [cellLimit, setCellLimit] = useState<number>(1000);

  const { status, isLoading, isFetching, error, data: CellOverviewData } = useGetCellList(cellLimit, lifecycle);

  const columns = useCellColumns();
  const cell_refs = CellOverviewData?.map(({ cell_ref }: { cell_ref: string }) => cell_ref);
  const {
    isLoading: activeAlarmsIsLoading,
    error: ActiveAlarmsError,
    data: activeAlarms,
  } = useMCActiveAlarms(cell_refs);

  const mergeAlarmsIntoCellOverview = (cellOverviewData: any, alarmData: any) => {
    const updatedOverviewData = cellOverviewData?.map((cell: any) => {
      const alarmDetails = alarmData?.cell_refs[cell?.cell_ref] || {};
      return {
        ...cell,
        cell_alarms: ActiveAlarmsError ? { error: ActiveAlarmsError } : alarmDetails,
      };
    });

    return updatedOverviewData;
  };
  const preUpdatedOverviewData = mergeAlarmsIntoCellOverview(CellOverviewData, activeAlarms);
  const updatedOverviewData = preUpdatedOverviewData || [];

  if (isLoading || activeAlarmsIsLoading) return <Loader />;
  if (ActiveAlarmsError) return <QueryError error={ActiveAlarmsError} />;

  return (
    <>
      <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
        <DataTable
          isExpandable={true}
          enableFilter={true}
          columns={columns ?? []}
          data={updatedOverviewData}
          isLoading={isLoading && isFetching}
          defaultPageSize={100}
          renderSubComponent={(row) => <RenderNodes row={row} />}
          alternativeViewId={alternativeViewId}
          limit={cellLimit}
          setLimit={setCellLimit}
          count={CellOverviewData?.length}
          hasEmptyResult={updatedOverviewData?.length == 0}
          urlCellRef={urlCellRef}
          version={'v2'}
          selectedFilter={selectedFilter}
        />
      </ErrorBoundary>
    </>
  );
};

export default Cells;
