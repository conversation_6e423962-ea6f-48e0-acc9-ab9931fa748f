import { AddIcon, BellIcon, DeleteIcon, EditIcon, LinkIcon, SettingsIcon } from '@chakra-ui/icons';
import {
  IconButton,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Text,
  useDisclosure,
  Button,
  Icon,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  Tabs,
  Heading,
  Tab,
  TabList,
  TabPanels,
  Flex,
  Box,
} from '@chakra-ui/react';
import { MouseEvent, useState } from 'react';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { useNavigate } from 'react-router-dom';
import { CustomModal } from '../../components/modal';
import NodeIpAddressModal from '../../components/nodeComponents/NodeIpAddressModal';
import StreetCellConfigPortal from '../../components/portal/Portal';
import { AUTH_TOKEN_KEY, MON<PERSON>OR_STATE, NODE_TYPE, READ_WRITE_ACCESS_ROLES } from '../../data/constants';
import useLogin from '../../hooks/useLogin';
import { StreetCellConfiguration } from '../../types/InventoryManager.type';
import { compactObject } from '../../utils/helpers';
import { useNodeConfig } from './hooks/useNodeConfig';
import { FaFileAlt, FaWifi } from 'react-icons/fa';
import { useManifestData } from '../Manifests/useManifestData';
import { useQuery } from '@tanstack/react-query';
import { getNodesManifestData } from '../../services/inventoryManager';
import Loader from '../../components/loader/Loader';
import NodeAlarms from './hooks/NodeAlarms';
import { PingModal } from '../../components/nodeComponents/ping';

type NodeConfigMenuProps = {
  dataTestId?: string;
  cellStatus: string;
  cellRef: string | undefined;
  nodeId: string;
  nodeType: string;
  node_serial_no: string;
};

export type SNMPConfigType = {
  nodeId: string;
  componentType: 'FIBROLAN' | 'BLUWIRELESS';
  snmpConfiguration: MONITOR_STATE.Active | MONITOR_STATE.Passive | MONITOR_STATE.Ignore;
};

const NodeConfigMenu = (props: NodeConfigMenuProps) => {
  const validEditConfig = ['Server', 'Server-VM', 'GCP-VM', 'MeshRoot', 'Switch', 'Firewall', 'GDCV', 'Power'];
  const navigate = useNavigate();
  const { cellStatus, cellRef, nodeId, nodeType, node_serial_no } = props;
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isConfirmOpen, onOpen: onConfirmOpen, onClose: onConfirmClose } = useDisclosure();
  const { isOpen: isManifestOpen, onOpen: onManifestOpen, onClose: onManifestClose } = useDisclosure();
  const { isOpen: isAlarmsOpen, onOpen: onAlarmsOpen, onClose: onAlarmsClose } = useDisclosure();
  const { isOpen: isPingOpen, onOpen: onPingOpen, onClose: onPingClose } = useDisclosure();
  const [selectedNode, setSelectedNode] = useState<string>('');
  const { deleteNodeFromNodeMutation, nodeConfigurationUpdates } = useNodeConfig(nodeId, onClose);

  const { isLoading, refetch: refetchNodesManifestData } = useQuery({
    queryKey: ['getNodesManifestData', 1000, node_serial_no],
    queryFn: () => getNodesManifestData(1000, node_serial_no),
    enabled: false,
  });

  const { handleExpandedChange, renderSubComponent, resetExpandRows } = useManifestData();

  const handleViewManifest = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onManifestOpen();
    const refetch = await refetchNodesManifestData();

    if (refetch.data && refetch.data.length > 0) {
      const nodeInfo = refetch.data[0];

      handleExpandedChange({
        original: {
          node_serial_no,
          manifest_type: nodeInfo.manifest_type,
        },
        getIsExpanded: true,
      });
    } else {
      alert('Node data not found');
    }
  };

  const handelDeleteNode = async (nodeId: any) => {
    onConfirmOpen();
  };

  const handleModalSubmit = async (nodeId: string, values: StreetCellConfiguration) => {
    const ipConfig = {
      fibrolan_ip_address: values.fibrolan_ip_address,
      bluwireless_ip_address: values.bluwireless_ip_address,
    };
    const snmpConfigList: SNMPConfigType[] = [];

    if (values?.fibrolan_switch_monitored) {
      snmpConfigList.push({
        componentType: 'FIBROLAN',
        snmpConfiguration: values?.fibrolan_switch_monitored as MONITOR_STATE,
        nodeId,
      });
    }

    if (values?.bw_mmwave_monitored) {
      snmpConfigList.push({
        componentType: 'BLUWIRELESS',
        snmpConfiguration: values?.bw_mmwave_monitored as MONITOR_STATE,
        nodeId,
      });
    }

    await nodeConfigurationUpdates({
      nodeId,
      streetCellConfiguration: compactObject(values),
      snmpConfigList,
    });
    await deleteNodeFromNodeMutation({});
  };

  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);

  function handleViewAlarms(e: React.MouseEvent<HTMLButtonElement>): void {
    e.preventDefault();
    e.stopPropagation();
    onAlarmsOpen();
  }

  return (
    <>
      {(nodeType === NODE_TYPE['STREETCELL'] || NODE_TYPE['SWITCH']) && checkRoleAccess ? (
        <>
          <Menu data-testid="node-menu-items">
            <MenuButton
              data-testid={props.dataTestId}
              onClick={(e) => {
                e.stopPropagation();
              }}
              as={IconButton}
              aria-label="Options"
              icon={<BsThreeDotsVertical />}
              variant="outline"
              border="none"
              isRound={true}
            />
            <MenuList>
              {nodeType === NODE_TYPE['STREETCELL'] && (
                <MenuItem
                  data-testid="edit-node"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onOpen();
                  }}
                >
                  <SettingsIcon mr="1rem" />
                  Configure node
                </MenuItem>
              )}

              <MenuItem
                data-testid="edit-nodes"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  localStorage.removeItem('cellRefId');
                  navigate('/cell-overview/editnode', { state: { nodeType, nodeId } });
                }}
              >
                <EditIcon mr="1rem" />
                Edit Node
              </MenuItem>

              <MenuItem
                data-testid="delete-node"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setSelectedNode(nodeId);
                  onConfirmOpen();
                  handelDeleteNode(nodeId);
                }}
              >
                <DeleteIcon mr="1rem" />
                Delete node
              </MenuItem>
              {nodeType !== NODE_TYPE['STREETCELL'] && (
                <MenuItem
                  data-testid="add-node-to-cell"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    navigate(`/cell-overview/nodes/manage-cell-node`, { state: { nodeId } });
                  }}
                >
                  <AddIcon mr="1rem" />
                  Add node to cells
                </MenuItem>
              )}
              <MenuItem
                data-testid="view-cells"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  navigate(`/cell-overview/cells/${cellRef}`);
                }}
              >
                <LinkIcon mr="1rem" color="green" />
                View Cells
              </MenuItem>
              <MenuItem data-testid="alarms-nodes" onClick={(e) => handleViewAlarms(e)}>
                <BellIcon mr="1rem" color="red" />
                View Alarms
              </MenuItem>
              <MenuItem
                data-testid="alarms-events-nodes"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  navigate(`/alarms-and-events/events/${nodeId}`);
                }}
              >
                <BellIcon mr="1rem" color="green" />
                View Events
              </MenuItem>
              <MenuItem data-testid="manifest-nodes" onClick={(e) => handleViewManifest(e)}>
                <Icon as={FaFileAlt} mr="1rem" />
                View Manifest
              </MenuItem>
              <MenuItem
                data-testid="ping-node"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onPingOpen();
                }}
              >
                <Icon as={FaWifi} mr="1rem" color="blue" />
                Ping Device
              </MenuItem>
            </MenuList>
          </Menu>
          <StreetCellConfigPortal>
            <CustomModal
              isOpen={isConfirmOpen}
              onClose={onConfirmClose}
              title="Delete node"
              submitText="Confirm"
              size="xl"
              //onSubmit={() => deleteNodeByNodeId(nodeId)}
              onSubmit={() =>
                deleteNodeFromNodeMutation({
                  node_id: selectedNode,
                })
              }
            >
              <Text>
                Are you sure you want to{' '}
                <Text as="span" fontWeight="bold">
                  DELETE
                </Text>{' '}
                the{' '}
                <Text as="em" fontWeight="bold">
                  {selectedNode}
                </Text>{' '}
                node?
              </Text>
            </CustomModal>
          </StreetCellConfigPortal>

          <StreetCellConfigPortal>
            <NodeIpAddressModal isOpen={isOpen} onClose={onClose} nodeId={nodeId} onSubmit={handleModalSubmit} />
          </StreetCellConfigPortal>

          <StreetCellConfigPortal>
            <Modal
              isOpen={isManifestOpen}
              onClose={() => {
                onManifestClose(), resetExpandRows(node_serial_no);
              }}
              size="4xl"
            >
              <ModalOverlay />
              <ModalContent maxW="6xl" w="100%" maxH="100vh">
                <ModalHeader data-testid="modal_heade_manifest">Manifest Details</ModalHeader>
                <ModalCloseButton />
                <ModalBody overflowY="auto">
                  {isLoading ? <Loader /> : renderSubComponent(node_serial_no, true)}
                </ModalBody>
                <ModalFooter>
                  <Button
                    colorScheme="teal"
                    data-testid="modal_close_manifest"
                    onClick={() => {
                      onManifestClose(), resetExpandRows(node_serial_no);
                    }}
                  >
                    Close
                  </Button>
                </ModalFooter>
              </ModalContent>
            </Modal>
          </StreetCellConfigPortal>

          <StreetCellConfigPortal>
            <Modal isOpen={isAlarmsOpen} onClose={onAlarmsClose} size="8xl">
              <ModalOverlay />
              <ModalContent>
                <ModalHeader data-testid="modal_header_alarms" fontSize="3xl">
                  <Box width="100%" textAlign="center" data-testid="modal_header_alarms_text">
                    Alarms {nodeId}
                  </Box>
                </ModalHeader>
                <ModalCloseButton data-testid="modal_close_alarms" size="lg" />
                <ModalBody>
                  <NodeAlarms nodeId={nodeId} />
                </ModalBody>
                <ModalFooter>
                  <Button onClick={onAlarmsClose}>Close</Button>
                </ModalFooter>
              </ModalContent>
            </Modal>
          </StreetCellConfigPortal>

          <PingModal isOpen={isPingOpen} onClose={onPingClose} nodeId={nodeId} nodeType={nodeType} />
        </>
      ) : null}
    </>
  );
};

export default NodeConfigMenu;
