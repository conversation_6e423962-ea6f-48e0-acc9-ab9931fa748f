import { useToast } from '@chakra-ui/toast';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import {
  deleteNodeFromCell,
  deleteNodeFromNodeList,
  updateStreetCellConfiguration,
} from '../../../services/inventoryManager';
import { patchSNMPByNodeType } from '../../../services/orchestrator';
import { StreetCellConfiguration } from '../../../types/InventoryManager.type';
import { SNMPConfigType } from '../NodeConfigMenu';

type useDeleteNodeReturnType = {
  isDeleting: boolean;
  nodeDeleted: boolean;
  setNodeDeleted: (nodeDeleted: boolean) => void;
  deleteNodeHandler: (node_id?: string) => Promise<void>;
};

export const useNodeConfig = (node_id: string, onClose: () => void): any => {
  const queryClient = useQueryClient();
  const toast = useToast();

  const { mutateAsync: deleteNodeFromCellMutation } = useMutation({
    mutationFn: ({ cell_ref, node_id }: { cell_ref: string; node_id: string }) => {
      return deleteNodeFromCell(cell_ref, node_id);
    },
    onSuccess: (node_id) => {
      queryClient.invalidateQueries({ queryKey: ['getNodesForCell'] });
      toast({
        title: 'Success',
        description: `Node has been deleted successfully.`,
        status: 'success',
        duration: 5000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `${(error as AxiosError<Error>)?.response?.data?.message}`,
        status: 'error',
        duration: 5000,
        isClosable: true,
        position: 'top',
      });
    },
  });

  const { mutateAsync: deleteNodeFromNodeMutation } = useMutation({
    mutationFn: ({ node_id }: { node_id: string }) => {
      return deleteNodeFromNodeList(node_id);
    },
    onSuccess: (node_id) => {
      queryClient.invalidateQueries({ queryKey: ['getNodesForCell'] });
      toast({
        title: 'Success',
        description: `Node has been deleted successfully.`,
        status: 'success',
        duration: 5000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `${(error as AxiosError<Error>)?.response?.data?.message}`,
        status: 'error',
        duration: 5000,
        isClosable: true,
        position: 'top',
      });
    },
  });

  const { mutateAsync: snmpMutation, isLoading: isCellPosting } = useMutation(
    (variables: { snmpConfiguration: SNMPConfigType; nodeId: string }) =>
      patchSNMPByNodeType(variables.snmpConfiguration),
    {
      onSuccess: async (data: any, variables: { snmpConfiguration: SNMPConfigType; nodeId: string }) => {
        queryClient.invalidateQueries({
          queryKey: ['getNodeCompByNodeId', variables.nodeId],
        });
        toast({
          title: 'Success',
          description: `Patch SNMP for component ${data[0].device_type} (id: ${data[0].component_id}) has been updated successfully for Node: ${variables.nodeId}`,
          status: 'success',
          duration: 9000,
          isClosable: true,
          position: 'top',
        });
        onClose();
      },
      onError: (error: AxiosError<Error>) => {
        console.log('🚀 ~ file: CellNodes.tsx:129 ~ CellNodes ~ error:', error);
        toast({
          title: 'Error',
          description: `Patch SNMP ${error?.response?.data?.message}`,
          status: 'error',
          duration: 9000,
          isClosable: true,
          position: 'top',
        });
      },
    }
  );

  const { mutateAsync: nodeConfigurationUpdates } = useMutation(
    async (variables: {
      nodeId: string;
      streetCellConfiguration: StreetCellConfiguration;
      snmpConfigList: SNMPConfigType[];
    }): Promise<{
      nodeId: string;
      streetCellConfiguration: StreetCellConfiguration;
      snmpConfigList: SNMPConfigType[];
    }> => {
      await updateStreetCellConfiguration(variables.nodeId, variables.streetCellConfiguration);
      await Promise.all(
        // variables.snmpConfigList.map((snmpConfig) => snmpMutation(snmpConfig))
        variables.snmpConfigList.map((snmpConfig) =>
          snmpMutation({
            snmpConfiguration: snmpConfig,
            nodeId: variables.nodeId,
          })
        )
      );
      return variables;
    },
    {
      // Options object starts here
      onSuccess: async (variables: {
        nodeId: string;
        streetCellConfiguration: StreetCellConfiguration;
        snmpConfigList: SNMPConfigType[];
      }) => {
        queryClient.invalidateQueries({
          queryKey: ['getNodeCompByNodeId', variables.nodeId],
        });
        toast({
          title: 'Success',
          description: `Node ${variables.nodeId} has been updated successfully.`,
          status: 'success',
          duration: 9000,
          isClosable: true,
          position: 'top',
        });
        onClose();
      },
      onError: (error: AxiosError<Error>) => {
        console.log('🚀 ~ file: CellNodes.tsx:189 ~ CellNodes ~ error:', error);
        toast({
          title: 'Error',
          description: `Node ${error?.response?.data?.message}`,
          status: 'error',
          duration: 9000,
          isClosable: true,
          position: 'top',
        });
      },
      // End of options object
    }
  );

  return {
    deleteNodeFromCellMutation,
    deleteNodeFromNodeMutation,
    nodeConfigurationUpdates,
  };
};
