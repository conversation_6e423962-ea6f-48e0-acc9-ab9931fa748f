import { useQuery } from '@tanstack/react-query';
import { LIFE_CYCLE } from '../../../../data/constants';
import { getNodeList } from '../../../../services/inventoryManager';

export default function useGetNodeList(
  node_id?: string,
  cellRef?: string,
  nodeLimit?: number,
  lifecycle?: LIFE_CYCLE[] | null
) {
  const cell_Ref = cellRef ? true : false;
  const { status, isLoading, isFetching, error, data } = useQuery(
    ['getNodesForCell', node_id, cellRef, 'nodeLimit', lifecycle],
    () => getNodeList(node_id, nodeLimit, lifecycle),
    {
      refetchInterval: 10000,
      retry: false,
    }
  );
  return { status, isLoading, isFetching, error, data };
}
