import { useQuery } from '@tanstack/react-query';
import { LIFE_CYCLE } from '../../../../data/constants';
import { getCellList } from '../../../../services/inventoryManager';

export default function useGetCellList(cellLimit: number, lifecycle?: LIFE_CYCLE[] | null) {
  return useQuery({
    queryKey: ['getCellList', cellLimit, lifecycle],
    queryFn: () => getCellList(undefined, cellLimit, lifecycle),
    refetchInterval: 10000,
    retry: false,
  });
}
