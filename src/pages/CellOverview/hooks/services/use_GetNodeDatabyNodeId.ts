import { useQuery } from '@tanstack/react-query';
import { getNodeData } from '../../../../services/inventoryManager';

export default function useGetNodeDataByNodeId(node_id?: string) {
  const { status, isLoading, isFetching, error, data } = useQuery(
    ['getNodeData', node_id],
    () => getNodeData(node_id),
    {
      refetchInterval: 10000,
      retry: false,
    }
  );
  return { status, isLoading, isFetching, error, data };
}
