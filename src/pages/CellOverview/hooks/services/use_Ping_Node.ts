import { useMutation, useQuery } from '@tanstack/react-query';
import { pingNode, PingResponse, PingErrorResponse } from '../../../../services/orchestrator';

/**
 * Hook for pinging a single node
 * @param nodeId - The ID of the node to ping
 * @param enabled - Whether the query should be enabled (default: false for manual trigger)
 */
export const usePingNode = (nodeId: string, enabled = false) =>
  useQuery(['pingNode', nodeId], () => pingNode(nodeId), {
    enabled,
    retry: false,
    staleTime: 0,
  });

/**
 * Hook for manual ping mutations
 * @returns Mutation hook for pinging nodes
 */
export const usePingNodeMutation = () => {
  return useMutation({
    mutationFn: pingNode,
    retry: false,
  });
};

/**
 * Hook for batch pinging multiple nodes
 * @param nodeIds - Array of node IDs to ping
 * @param enabled - Whether the query should be enabled
 */
export const useBatchPingNodes = (nodeIds: string[], enabled = false) => {
  return useQuery(
    ['batchPingNodes', nodeIds],
    async (): Promise<PingResponse[]> => {
      const pingPromises = nodeIds.map((nodeId) => pingNode(nodeId));
      const results = await Promise.allSettled(pingPromises);
      return results.map((result, index) => {
        if (result.status === 'fulfilled') {
          return result.value;
        } else {
          const errorResponse: PingErrorResponse = {
            utc_stamp: new Date().toISOString(),
            origin: 'ping-hook',
            message: `Network error for node ${nodeIds[index]}: ${result.reason?.message || 'Ping failed'}`,
            status: 'FAILED',
            object: null,
          };
          return errorResponse;
        }
      });
    },
    {
      enabled: enabled && nodeIds.length > 0,
      retry: false,
      staleTime: 0,
    }
  );
};

export default usePingNode;
