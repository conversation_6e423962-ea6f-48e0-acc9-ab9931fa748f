import { useToast } from '@chakra-ui/toast';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { useState } from 'react';
import { deleteCell } from '../../../services/inventoryManager';
import { useDeleteNodeFromCell } from './services/use_Inv_DeleteNodeFromCell';
import useGetNodesByCellRef from './services/use_Inv_GetNodesForCell';

type UseDeleteCellReturnType = {
  isDeleting: boolean;
  cellDeleted: boolean;
  setCellDeleted: (cellDeleted: boolean) => void;
  deleteCellHandler: (cell_ref?: string) => Promise<void>;
};

const useCellConfig = (cell_ref: string): UseDeleteCellReturnType => {
  const toast = useToast();
  const queryClient = useQueryClient();
  const cellNodeList = useGetNodesByCellRef(cell_ref ?? '').data;
  const hasCellNodes = cellNodeList && cellNodeList.length > 0;
  const { deleteNodeFromCellMutation } = useDeleteNodeFromCell();
  const { mutateAsync: deleteCellMutation } = useMutation({
    mutationFn: deleteCell,
  });
  const [isDeleting, setIsDeleting] = useState(false);
  const [cellDeleted, setCellDeleted] = useState(false);

  const deleteCellHandler = async (cell_ref?: string) => {
    if (!cell_ref) return;
    setIsDeleting(true);
    if (hasCellNodes) {
      // Loop through each node and delete it
      for (const node of cellNodeList) {
        await deleteNodeFromCellMutation({
          node_id: node.node_id,
          cell_ref: cell_ref,
        });
      }
    }

    await deleteCellMutation(cell_ref, {
      onSuccess: (cell) => {
        queryClient.invalidateQueries({ queryKey: ['getCellList'] });
        toast({
          title: 'Success',
          description: `Cell ${cell.cell_ref} has been deleted successfully.`,
          status: 'success',
          duration: 5000,
          isClosable: true,
          position: 'top',
        });
        setCellDeleted(true);
      },
      onError: (error) => {
        toast({
          title: `Error deleting cell ${cell_ref}`,
          description: `${(error as AxiosError<Error>)?.response?.data?.message}`,
          status: 'error',
          duration: 5000,
          isClosable: true,
          position: 'top',
        });
      },
    });
    setIsDeleting(false);
  };

  return { isDeleting, cellDeleted, setCellDeleted, deleteCellHandler };
};

export default useCellConfig;
