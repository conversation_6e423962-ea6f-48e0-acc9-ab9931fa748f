import {
  Box,
  Card,
  Flex,
  Text,
  useColorModeValue,
  Divider,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Icon,
  Tooltip,
  useClipboard,
  IconButton,
  SimpleGrid,
  HStack,
  VStack,
  Tag,
  TagLabel,
  TagLeftIcon,
} from '@chakra-ui/react';
import {
  AirspanManifest,
  BwManifest,
  CellManifest,
  DalManifest,
  FibrolanManifest,
  RadioManifest,
} from '../../types/InventoryManager.type';
import {
  FiCopy,
  FiCheck,
  FiInfo,
  FiAlertCircle,
  FiCheckCircle,
  FiHardDrive,
  FiWifi,
  FiServer,
  FiCpu,
  FiCalendar,
  FiHash,
  FiBarChart2,
} from 'react-icons/fi';
import { useMemo } from 'react';
import _ from 'lodash';
import { formatDate } from '../../components/nodeComponents/server/server/utils';

type ManifestType = CellManifest | AirspanManifest | BwManifest | DalManifest | FibrolanManifest | RadioManifest;

const formatKey = (key: string): string => {
  const parts = key.split('_');
  parts[0] = parts[0].length <= 3 ? parts[0].toUpperCase() : parts[0].charAt(0).toUpperCase() + parts[0].slice(1);
  if (parts[1]) {
    parts[1] = parts[1].charAt(0).toUpperCase() + parts[1].slice(1);
  }
  return parts.join(' ');
};

const getIconForKey = (key: string) => {
  if (key.includes('serial') || key.includes('id')) return FiHash;
  if (key.includes('version')) return FiBarChart2;
  if (key.includes('mac')) return FiServer;
  if (key.includes('test')) return FiCheckCircle;
  if (key.includes('date') || key.includes('created') || key.includes('updated')) return FiCalendar;
  if (key.includes('cpu') || key.includes('processor')) return FiCpu;
  if (key.includes('wifi') || key.includes('network')) return FiWifi;
  if (key.includes('hardware') || key.includes('device')) return FiHardDrive;
  return FiInfo;
};

const getStatusColor = (value: string): string => {
  const lowerValue = String(value).toLowerCase();
  if (lowerValue.includes('pass') || lowerValue.includes('ok') || lowerValue === 'done' || lowerValue === 'Done')
    return 'green';
  if (lowerValue.includes('fail') || lowerValue.includes('error')) return 'red';
  if (lowerValue.includes('warn')) return 'orange';
  return 'gray';
};

const CopyableValue = ({ value }: { value: string }) => {
  const { hasCopied, onCopy } = useClipboard(value);

  return (
    <HStack spacing={1}>
      <Text flex="1" overflowWrap="break-word" whiteSpace="pre-wrap" wordBreak="break-all">
        {value}
      </Text>
      <Tooltip label={hasCopied ? 'Copied!' : 'Copy to clipboard'}>
        <IconButton
          aria-label="Copy to clipboard"
          icon={hasCopied ? <FiCheck /> : <FiCopy />}
          size="xs"
          variant="ghost"
          onClick={onCopy}
        />
      </Tooltip>
    </HStack>
  );
};

type ManifestValue = string | number | boolean | null | undefined | Record<string, any> | ManifestValue[];

const KeyValuePair = ({
  keyName,
  value,
  parentKey = '',
}: {
  keyName: string;
  value: ManifestValue;
  parentKey?: string;
}) => {
  const formattedKey = formatKey(keyName);
  const KeyIcon = getIconForKey(keyName);

  const isStatusField = keyName.includes('status') || keyName.includes('test') || keyName === 'product_status';
  const statusValue = typeof value === 'string' ? value : null;

  return (
    <Flex
      direction="row"
      data-testid="manifestCard"
      alignItems="flex-start"
      paddingY="2"
      borderBottom="1px solid"
      borderColor="gray.100"
      _hover={{ bg: 'gray.50' }}
      transition="background 0.2s"
    >
      <HStack minWidth="180px" spacing={2}>
        <Icon as={KeyIcon} color="gray.500" />
        <Text fontWeight="medium" data-testid="manifestCard-key" color="gray.700">
          {formattedKey}:
        </Text>
      </HStack>

      <Box flex="1" paddingLeft="2">
        {isStatusField && statusValue ? (
          <Tag size="md" colorScheme={getStatusColor(statusValue)} variant="subtle">
            <TagLeftIcon
              as={
                statusValue.toLowerCase().includes('pass') || statusValue === 'done' || statusValue === 'Done'
                  ? FiCheckCircle
                  : FiAlertCircle
              }
            />
            <TagLabel>{statusValue}</TagLabel>
          </Tag>
        ) : typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean' ? (
          <CopyableValue value={String(value ?? 'N/A')} />
        ) : Array.isArray(value) ? (
          value.length > 0 ? (
            <Box pl={2} borderLeft="2px" borderColor="teal.200">
              {renderArrayItems(value, `${parentKey}-${keyName}`)}
            </Box>
          ) : (
            <Text color="gray.500">N/A</Text>
          )
        ) : typeof value === 'object' && value !== null ? (
          <Box pl={2} borderLeft="2px" borderColor="purple.200">
            {renderKeyValuePairs(value, keyName)}
          </Box>
        ) : (
          <Text color="gray.500">N/A</Text>
        )}
      </Box>
    </Flex>
  );
};

const renderArrayItems = (data: ManifestValue[], parentKey = '') => {
  if (data.length === 0) return <Text color="gray.500">N/A</Text>;

  if (typeof data[0] === 'object' && data[0] !== null) {
    return (
      <Tabs variant="soft-rounded" colorScheme="teal" size="sm" mt={2}>
        <TabList>
          {data.map((_, index) => (
            <Tab key={`tab-${index}`}>Item {index + 1}</Tab>
          ))}
        </TabList>
        <TabPanels>
          {data.map((item, index) => {
            if (typeof item !== 'object' || item === null) return null;

            return (
              <TabPanel key={`panel-${index}`} p={2}>
                <Card variant="outline" size="sm" p={3}>
                  {Object.entries(item as Record<string, any>).map(([key, value], idx) => {
                    if (
                      (typeof value === 'object' && value !== null && Object.keys(value).length === 0) ||
                      (Array.isArray(value) && value.length === 0)
                    ) {
                      return null;
                    }

                    return (
                      <KeyValuePair
                        key={`${parentKey}-${index}-${idx}`}
                        keyName={key}
                        value={value}
                        parentKey={`${parentKey}-${index}`}
                      />
                    );
                  })}
                </Card>
              </TabPanel>
            );
          })}
        </TabPanels>
      </Tabs>
    );
  }

  return (
    <SimpleGrid columns={2} spacing={2}>
      {data.map((item, index) => (
        <Text key={`${parentKey}-item-${index}`}>{typeof item === 'object' ? JSON.stringify(item) : String(item)}</Text>
      ))}
    </SimpleGrid>
  );
};

const renderKeyValuePairs = (data: Record<string, any> | ManifestValue[], parentKey = '') => {
  if (Array.isArray(data)) {
    return renderArrayItems(data, parentKey);
  }

  return Object.entries(data)
    .map(([key, value], index) => {
      return <KeyValuePair key={`${parentKey}-${index}`} keyName={key} value={value} parentKey={parentKey} />;
    })
    .filter(Boolean);
};

const groupManifestData = (manifest: Record<string, ManifestValue> | ManifestValue) => {
  if (typeof manifest !== 'object' || manifest === null || Array.isArray(manifest)) {
    return {};
  }

  const groups: Record<string, Record<string, ManifestValue>> = {
    'Basic Information': {},
    'Discovery Information': {},
    'Hardware Details': {},
    'Status & Tests': {},
    'Network Information': {},
    'Location Information': {},
    Timestamps: {},
    Other: {},
  };

  Object.entries(manifest).forEach(([key, value]) => {
    if (
      key.includes('serial') ||
      key.includes('model') ||
      key.includes('version') ||
      key.includes('id') ||
      key.includes('name') ||
      key.includes('instance') ||
      key.includes('owner') ||
      key.includes('label') ||
      key.includes('node_type') ||
      key.includes('component_type') ||
      key.includes('app')
    ) {
      groups['Basic Information'][key] = value;
    } else if (key.includes('discovery') || key.includes('heartbeat') || key.includes('started')) {
      groups['Discovery Information'][key] = value;
    } else if (key.includes('hardware') || key.includes('board') || key.includes('cpu') || key.includes('revision')) {
      groups['Hardware Details'][key] = value;
    } else if (key.includes('test') || key.includes('status')) {
      groups['Status & Tests'][key] = value;
    } else if (key.includes('mac') || key.includes('ip') || key.includes('address') || key.includes('network')) {
      groups['Network Information'][key] = value;
    } else if (
      key.includes('placement') ||
      key.includes('latitude') ||
      key.includes('longitude') ||
      key.includes('location') ||
      key.includes('orientation')
    ) {
      groups['Location Information'][key] = value;
    } else if (key.includes('created') || key.includes('updated') || key.includes('date') || key.includes('time')) {
      groups['Timestamps'][key] = formatDate(value as string);
    } else {
      groups['Other'][key] = value;
    }
  });

  return Object.fromEntries(Object.entries(groups).filter(([, groupData]) => Object.keys(groupData).length > 0));
};

const ManifestCard = ({ header, manifest }: { header: string; manifest: ManifestType }) => {
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const headerBg = useColorModeValue('teal.50', 'teal.900');

  const groupedData = useMemo(() => groupManifestData(manifest), [manifest]);

  const hasNestedObjects = useMemo(() => {
    return Object.values(manifest).some(
      (value) => typeof value === 'object' && value !== null && !Array.isArray(value) && Object.keys(value).length > 0
    );
  }, [manifest]);

  if (hasNestedObjects) {
    return (
      <Card
        borderTop="4px solid"
        borderTopColor="teal.400"
        height="100%"
        data-testid="manifestCard-card"
        overflow="hidden"
        boxShadow="md"
      >
        <Box as="section" bg="bg-surface" borderRadius="lg" height="100%">
          <Flex
            alignItems="center"
            paddingX="6"
            paddingY="4"
            bg={headerBg}
            borderBottom="1px solid"
            borderColor={borderColor}
          >
            <Text data-testid="manifestCard-heading" fontWeight="bold" fontSize="xl">
              {header}
            </Text>
          </Flex>

          <Tabs colorScheme="teal" p="4">
            <TabList>
              {Object.entries(manifest).map(([key, value]) => {
                if (
                  typeof value === 'object' &&
                  value !== null &&
                  !Array.isArray(value) &&
                  Object.keys(value).length > 0
                ) {
                  return <Tab key={key}>{formatKey(key)}</Tab>;
                }
                return null;
              })}
            </TabList>

            <TabPanels>
              {Object.entries(manifest).map(([key, value]) => {
                if (_.isNil(value) || (_.isObject(value) && !Array.isArray(value) && _.isEmpty(value))) {
                  return null;
                }

                if (typeof value !== 'object' || Array.isArray(value)) {
                  return null;
                }
                const groupedObjectData = groupManifestData(value as Record<string, any>);

                return (
                  <TabPanel key={key}>
                    {Object.entries(groupedObjectData).map(([groupName, groupFields]) => (
                      <Box key={groupName} mb="6">
                        <Flex alignItems="center" mb="3">
                          <Divider flex="1" borderColor={borderColor} />
                          <Text px="3" fontWeight="bold" fontSize="md" color="gray.600">
                            {groupName}
                          </Text>
                          <Divider flex="1" borderColor={borderColor} />
                        </Flex>
                        <VStack align="stretch" spacing={0} divider={<Divider />}>
                          {renderKeyValuePairs(groupFields as Record<string, ManifestValue>)}
                        </VStack>
                      </Box>
                    ))}
                  </TabPanel>
                );
              })}
            </TabPanels>
          </Tabs>
        </Box>
      </Card>
    );
  }

  return (
    <Card
      borderTop="4px solid"
      borderTopColor="teal.400"
      height="100%"
      data-testid="manifestCard-card"
      overflow="hidden"
      boxShadow="md"
    >
      <Box as="section" bg="bg-surface" borderRadius="lg" height="100%">
        <Flex
          alignItems="center"
          paddingX="6"
          paddingY="4"
          bg={headerBg}
          borderBottom="1px solid"
          borderColor={borderColor}
        >
          <Text data-testid="manifestCard-heading" fontWeight="bold" fontSize="xl">
            {header}
          </Text>
        </Flex>

        <Box p="5">
          {Object.entries(groupedData).map(([groupName, groupData]) => (
            <Box key={groupName} mb="6">
              <Flex alignItems="center" mb="3">
                <Divider flex="1" borderColor={borderColor} />
                <Text px="3" fontWeight="bold" fontSize="md" color="gray.600">
                  {groupName}
                </Text>
                <Divider flex="1" borderColor={borderColor} />
              </Flex>

              <VStack align="stretch" spacing={0} divider={<Divider />}>
                {renderKeyValuePairs(groupData)}
              </VStack>
            </Box>
          ))}
        </Box>
      </Box>
    </Card>
  );
};

export default ManifestCard;
