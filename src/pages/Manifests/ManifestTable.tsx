import {
  Box,
  Button,
  Card,
  Container,
  Flex,
  Heading,
  Icon,
  Modal,
  ModalBody,
  ModalCloseButton,
  Modal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>eader,
  ModalOverlay,
  Stack,
  Text,
  useColorModeValue,
  useDisclosure,
} from '@chakra-ui/react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { ColumnDef } from '@tanstack/react-table';
import _ from 'lodash';
import * as React from 'react';
import { BsArrowReturnRight } from 'react-icons/bs';
import { FiRefreshCw } from 'react-icons/fi';
import Loader from '../../components/loader/Loader';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../data/constants';
import useLogin from '../../hooks/useLogin';
import { refreshCellInventory } from '../../services/httpCommon';
import { getNodesManifestData } from '../../services/inventoryManager';
import { NodesManifestProps, StreetCellManifestProps } from '../../types/InventoryManager.type';
import { RefreshCellInventoryResponse } from '../../types/orchestrator.types';
import { DataTable } from '../MetricsCollector/components/DataTable';
import ManifestCard from './ManifestCard';
import Inventory from './Inventory';
import ManifestConfigMenu from './ManifestConfigMenu';
import { useManifestData } from './useManifestData';

export const ManifestComponent = ({ manifestData, isInModal = false }: { manifestData: any; isInModal?: boolean }) => {
  const boxShadow = useColorModeValue('sm', 'sm-dark');

  const getCardWidth = () => {
    const itemCount = manifestData ? Object.keys(manifestData).length : 0;

    if (isInModal) {
      // In modal: max 2 cards per row
      return itemCount > 1 ? '48%' : '48%';
    } else {
      // In subrow: max 3 cards per row
      return itemCount > 2 ? '30%' : itemCount > 1 ? '45%' : '60%';
    }
  };

  const width = getCardWidth();

  return manifestData ? (
    <>
      {manifestData && (
        <Box display="flex" flexWrap="wrap" gap="10px" data-testid="manifest_component_wrapper">
          {Object.keys(manifestData)
            .filter((key) => !_.isNull(manifestData[key]))
            .map((key, index) => {
              const parts = key.split('_');
              parts[0] = parts[0].charAt(0).toUpperCase() + parts[0].slice(1);

              if (parts[0] === 'Bw') {
                parts[0] = 'BlueWireless';
              }

              if (parts[1]) {
                parts[1] = parts[1].charAt(0).toUpperCase() + parts[1].slice(1);
              }
              return (
                <Box margin="2" key={index} width={width}>
                  <ManifestCard
                    header={parts.join(' ')}
                    manifest={manifestData[key as keyof StreetCellManifestProps]}
                  />
                </Box>
              );
            })}
        </Box>
      )}
    </>
  ) : (
    <Card borderTop="1px solid #e2e2e2">
      <Box as="section" bg="bg-surface" boxShadow={boxShadow} borderRadius="lg" p={{ base: '4', md: '6' }}>
        <Text fontWeight="bold">No manifest found</Text>
      </Box>
    </Card>
  );
};

const ManifestTable = () => {
  const [manifestLimit, setManifestLimit] = React.useState<number>(1000);

  const {
    isLoading,
    data: nodesManifestData,
    refetch: refetchNodesManifestData,
  } = useQuery({
    queryKey: ['getNodesManifestData', 'manifestLimit'],
    queryFn: () => getNodesManifestData(manifestLimit),
  });

  const {
    mutate,
    data: inventoryList,
    isLoading: isInvLoading,
  } = useMutation<RefreshCellInventoryResponse>({
    mutationFn: refreshCellInventory,
    onSuccess: () => {
      refetchNodesManifestData();
    },
  });

  const { handleExpandedChange, renderSubComponent } = useManifestData();
  const { isOpen, onOpen, onClose } = useDisclosure();

  const columns = React.useMemo<ColumnDef<NodesManifestProps>[]>(
    () => [
      {
        header: 'Node Serial No',
        accessorKey: 'node_serial_no',
        id: 'node_serial_no',
        cell: (props) => {
          const node_serial_no = props.row.original.node_serial_no as string;
          return (
            <>
              <Flex>
                <BsArrowReturnRight />
                <Text ml={2}>{node_serial_no}</Text>
              </Flex>
            </>
          );
        },
      },
      {
        header: 'Lifecycle',
        accessorKey: 'lifecycle',
        id: 'lifecycle',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Country Name',
        accessorKey: 'country_name',
        id: 'country_name',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Manifest Type',
        accessorKey: 'manifest_type',
        id: 'manifest_type',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Region Name',
        accessorKey: 'region_name',
        id: 'region_name',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Site Name',
        accessorKey: 'site_name',
        id: 'site_name',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        header: 'Location',
        accessorKey: 'location',
        id: 'location',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
        accessorFn: (row: NodesManifestProps) => {
          if (row.latitude !== undefined && row.longitude !== undefined) {
            return `Lat: ${row.latitude.toFixed(6)}, Long: ${row.longitude.toFixed(6)}`;
          } else {
            return '';
          }
        },
      },
      {
        header: '',
        accessorKey: 'config',
        id: 'config',
        enableSorting: false,
        cell: ({ row }) => {
          const node_id = row.original.node_id as string;
          return <ManifestConfigMenu node_id={node_id} />;
        },
      },
    ],
    []
  );
  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const isWriteAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);

  const colorMode = useColorModeValue('gray.100', 'gray.800');
  const boxShadow = useColorModeValue('sm', 'sm-dark');

  if (isLoading)
    return (
      <>
        <Loader />
      </>
    );

  return (
    <Flex
      px={{
        base: '4',
        md: '8',
      }}
      flexDirection="column"
      mt="2"
    >
      <Stack
        spacing="1"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="center"
        flex="1"
      >
        <Stack spacing="1">
          <Heading fontWeight="medium">Device Manifests</Heading>
        </Stack>
        <Button
          variant="primary"
          leftIcon={<Icon as={FiRefreshCw} marginStart="-1" />}
          onClick={() => {
            mutate();
            if (!isInvLoading) onOpen();
          }}
          position="absolute"
          right="60px"
          isDisabled={!isWriteAccess}
        >
          Refresh inventory
        </Button>
      </Stack>
      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box bg={colorMode}>
          <Container
            py={{
              base: '4',
              md: '0',
            }}
            px={{
              base: '0',
              md: 0,
            }}
          >
            <Box bg="bg-surface" boxShadow={boxShadow}>
              <Stack spacing="5">
                <Box
                  px={{
                    base: '4',
                    md: '6',
                  }}
                  pt="5"
                />
                <DataTable
                  enableFilter={true}
                  isExpandable={true}
                  columns={columns}
                  defaultPageSize={100}
                  count={nodesManifestData?.length}
                  data={nodesManifestData}
                  renderSubComponent={(props) => renderSubComponent(props?.row?.original?.node_serial_no)}
                  getRowCanExpand={() => true}
                  onExpandedChange={handleExpandedChange}
                  limit={manifestLimit}
                  setLimit={setManifestLimit}
                  version={'v2'}
                />
              </Stack>
            </Box>
          </Container>
        </Box>
      </Stack>
      <Modal isOpen={isOpen} onClose={onClose} size={'3xl'}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Inventory</ModalHeader>
          <ModalCloseButton />
          <ModalBody>{isInvLoading ? <Loader /> : <Inventory data={inventoryList} />}</ModalBody>
          <ModalFooter>
            <Button colorScheme="brand" mr={3} onClick={onClose}>
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Flex>
  );
};

export default ManifestTable;
