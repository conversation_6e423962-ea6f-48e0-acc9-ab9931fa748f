import { useState, useCallback } from 'react';
import { useMutation } from '@tanstack/react-query';
import { Box, Text } from '@chakra-ui/react';
import Loader from '../../components/loader/Loader';
import {
  getAirspan4GManifestData,
  getClusterManifestData,
  getNetworkManifestData,
  getPodManifestData,
  getPowerManifestData,
  getRadioManifestData,
  getServerManifestData,
  getStreetCellManifestData,
} from '../../services/inventoryManager';
import { ManifestComponent } from './ManifestTable';

// Custom Hook for managing manifest data and expanded rows
export const useManifestData = () => {
  const [expandedRows, setExpandedRows] = useState<{ [key: string]: any }>({});

  const { mutate: StreetCellManifestData } = useMutation(getStreetCellManifestData, {
    onSuccess: (data, variables) => {
      setExpandedRows((prevExpandedRows) => ({
        ...prevExpandedRows,
        [variables]: data,
      }));
    },
    onError: (error: any, variables: any) => {
      let errorMessage = 'Error fetching street cell manifest data';
      if (error.response && error.response.status === 404) {
        errorMessage = "Street cell manifest doesn't exist";
      } else {
        errorMessage = 'Unable to fetch street cell manifest';
      }

      setExpandedRows((prevExpandedRows) => ({
        ...prevExpandedRows,
        [variables]: { error: errorMessage },
      }));
    },
  });

  const { mutate: ServerManifestData } = useMutation(getServerManifestData, {
    onSuccess: (data, variables) => {
      setExpandedRows((prevExpandedRows) => ({
        ...prevExpandedRows,
        [variables]: { 'Server Manifest': data },
      }));
    },
    onError: (error: any, variables: any) => {
      let errorMessage = 'Error fetching server manifest data';
      if (error.response && error.response.status === 404) {
        errorMessage = "Server manifest doesn't exist";
      } else {
        errorMessage = 'Unable to fetch server manifest';
      }

      setExpandedRows((prevExpandedRows) => ({
        ...prevExpandedRows,
        [variables]: { error: errorMessage },
      }));
    },
  });

  const { mutate: NetworkManifestData } = useMutation(getNetworkManifestData, {
    onSuccess: (data, variables) => {
      setExpandedRows((prevExpandedRows) => ({
        ...prevExpandedRows,
        [variables]: { 'Network Manifest': data },
      }));
    },
    onError: (error: any, variables: any) => {
      let errorMessage = 'Error fetching network manifest data';
      if (error.response && error.response.status === 404) {
        errorMessage = "Network manifest doesn't exist";
      } else {
        errorMessage = 'Unable to fetch network manifest';
      }

      setExpandedRows((prevExpandedRows) => ({
        ...prevExpandedRows,
        [variables]: { error: errorMessage },
      }));
    },
  });

  const { mutate: Airspan4GManifestData } = useMutation(getAirspan4GManifestData, {
    onSuccess: (data, variables) => {
      setExpandedRows((prevExpandedRows) => ({
        ...prevExpandedRows,
        [variables]: { 'Airspan4G Manifest': data },
      }));
    },
    onError: (error: any, variables: any) => {
      let errorMessage = 'Error fetching airspan 4g manifest data';
      if (error.response && error.response.status === 404) {
        errorMessage = "Airspan 4g manifest doesn't exist";
      } else {
        errorMessage = 'Unable to fetch airspan 4g manifest';
      }

      setExpandedRows((prevExpandedRows) => ({
        ...prevExpandedRows,
        [variables]: { error: errorMessage },
      }));
    },
  });

  const { mutate: ClusterManifestData } = useMutation(getClusterManifestData, {
    onSuccess: (data, variables) => {
      setExpandedRows((prevExpandedRows) => ({
        ...prevExpandedRows,
        [variables]: { 'Cluster Manifest': data },
      }));
    },
    onError: (error: any, variables: any) => {
      let errorMessage = 'Error fetching cluster manifest data';
      if (error.response && error.response.status === 404) {
        errorMessage = "Cluster manifest doesn't exist";
      } else {
        errorMessage = 'Unable to fetch cluster manifest';
      }

      setExpandedRows((prevExpandedRows) => ({
        ...prevExpandedRows,
        [variables]: { error: errorMessage },
      }));
    },
  });

  const { mutate: PodManifestData } = useMutation(getPodManifestData, {
    onSuccess: (data, variables) => {
      setExpandedRows((prevExpandedRows) => ({
        ...prevExpandedRows,
        [variables]: { 'Pod Manifest': data },
      }));
    },
    onError: (error: any, variables: any) => {
      let errorMessage = 'Error fetching pod manifest data';
      if (error.response && error.response.status === 404) {
        errorMessage = "Pod manifest doesn't exist";
      } else {
        errorMessage = 'Unable to fetch pod manifest';
      }

      setExpandedRows((prevExpandedRows) => ({
        ...prevExpandedRows,
        [variables]: { error: errorMessage },
      }));
    },
  });

  const { mutate: RadioManifestData } = useMutation(getRadioManifestData, {
    onSuccess: (data, variables) => {
      // we have to flatten the data , so flatten manifest, then discovery, and discovery has health_info so flatten it too
      const { manifest: manifest, discovery: discovery, ...filteredData } = data;
      let flattenedData: any;
      if (discovery) {
        if ('health_info' in discovery) {
          const { health_info, ...discoveryWithoutHealthInfo } = discovery;
          flattenedData = { ...discoveryWithoutHealthInfo, ...health_info };
        } else {
          flattenedData = discovery;
        }
      } else {
        flattenedData = discovery;
      }

      setExpandedRows((prevExpandedRows) => ({
        ...prevExpandedRows,
        [variables]: { 'Radio Manifest': { ...filteredData, ...flattenedData }, ...manifest },
      }));
    },

    onError: (error: any, variables: any) => {
      let errorMessage = 'Error fetching radio manifest data';
      if (error.response && error.response.status === 404) {
        errorMessage = "Radio manifest doesn't exist";
      } else {
        errorMessage = 'Unable to fetch radio manifest';
      }

      setExpandedRows((prevExpandedRows) => ({
        ...prevExpandedRows,
        [variables]: { error: errorMessage },
      }));
    },
  });

  const { mutate: PowerManifestData } = useMutation(getPowerManifestData, {
    onSuccess: (data, variables) => {
      setExpandedRows((prevExpandedRows) => ({
        ...prevExpandedRows,
        [variables]: { 'Power Manifest': data },
      }));
    },
    onError: (error: any, variables: any) => {
      let errorMessage = 'Error fetching power manifest data';
      if (error.response && error.response.status === 404) {
        errorMessage = "Power manifest doesn't exist";
      } else {
        errorMessage = 'Unable to fetch power manifest';
      }

      setExpandedRows((prevExpandedRows) => ({
        ...prevExpandedRows,
        [variables]: { error: errorMessage },
      }));
    },
  });

  const handleExpandedChange = useCallback(
    (row: any) => {
      if (row.getIsExpanded) {
        // Only fetch data if it hasn't been fetched yet
        if (!expandedRows[row.original?.node_serial_no]) {
          setExpandedRows((prevExpandedRows) => ({
            ...prevExpandedRows,
            [row.original?.node_serial_no]: null,
          }));

          switch (row.original?.manifest_type) {
            case 'streetcell':
              StreetCellManifestData(row.original?.node_serial_no);
              break;
            case 'server':
              ServerManifestData(row.original?.node_serial_no);
              break;
            case 'network':
              NetworkManifestData(row.original?.node_serial_no);
              break;
            case 'as4g':
              Airspan4GManifestData(row.original?.node_serial_no);
              break;
            case 'cluster':
              ClusterManifestData(row.original?.node_serial_no);
              break;
            case 'pod':
              PodManifestData(row.original?.node_serial_no);
              break;
            case 'power':
              PowerManifestData(row.original?.node_serial_no);
              break;
            case 'radio':
              RadioManifestData(row.original?.node_serial_no);
              break;
          }
        }
      } else {
        setExpandedRows((prevExpandedRows) => {
          const newExpandedRows = { ...prevExpandedRows };
          delete newExpandedRows[row.original?.node_serial_no];
          return newExpandedRows;
        });
      }
    },
    [
      expandedRows,
      StreetCellManifestData,
      ServerManifestData,
      NetworkManifestData,
      Airspan4GManifestData,
      ClusterManifestData,
      PodManifestData,
      PowerManifestData,
      RadioManifestData,
    ]
  );

  const resetExpandRows = (nodeSerialNo: string) => {
    setExpandedRows((prevExpandedRows) => ({
      ...prevExpandedRows,
      [nodeSerialNo]: null,
    }));
  };

  const renderSubComponent = useCallback(
    (node_serial_no: string, isInModal = false) => {
      const manifestData = expandedRows[node_serial_no];

      if (manifestData && Object.keys(manifestData).includes('error')) {
        return (
          <Box>
            <Text>{manifestData['error']}</Text>
          </Box>
        );
      }

      if (!manifestData) {
        return <Loader />;
      }

      return <ManifestComponent manifestData={manifestData} isInModal={isInModal} />;
    },
    [expandedRows]
  );

  return {
    handleExpandedChange,
    renderSubComponent,
    resetExpandRows,
  };
};
