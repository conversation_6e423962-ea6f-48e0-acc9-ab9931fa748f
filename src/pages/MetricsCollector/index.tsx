import {
  Box,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Switch,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
} from '@chakra-ui/react';
import { useNavigate, useParams } from 'react-router-dom';
import Events from './components/Events/Events';
import Alarms from './components/Alarms/Alarms';
import { LIFE_CYCLE } from '../../data/constants';
import { useEffect, useState } from 'react';

const AlarmsAndEvents = () => {
  const { tab, id } = useParams();
  const navigate = useNavigate();
  const [lifecycle, setLifecycle] = useState<LIFE_CYCLE[] | null>(() => {
    const stored = localStorage.getItem('OperationalOnly');
    return stored === 'true' ? [LIFE_CYCLE.OPERATIONAL, LIFE_CYCLE.FAULTY] : null;
  });

  const tabIndex = tab === 'alarms' ? 0 : 1;

  const handleTabs = (index: number) => {
    const newTab = index === 0 ? 'alarms' : 'events';
    navigate(`/alarms-and-events/${newTab}${id ? `/${id}` : ''}`);
  };

  useEffect(() => {
    const opOnly = lifecycle && lifecycle.includes(LIFE_CYCLE.OPERATIONAL);
    localStorage.setItem('OperationalOnly', JSON.stringify(opOnly));
  }, [lifecycle]);

  const handleToggle = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLifecycle(e.target.checked ? [LIFE_CYCLE.OPERATIONAL, LIFE_CYCLE.FAULTY] : null);
  };

  return (
    <Tabs
      isFitted
      variant="enclosed-colored"
      orientation="horizontal"
      onChange={(index: number) => handleTabs(index)}
      index={tabIndex}
      isManual
    >
      <Flex align="center" mb="8" mt="4">
        <FormControl display="flex" alignItems="center" ml="auto" justifyContent="flex-end">
          <FormLabel htmlFor="lifecycle-operational" mb="0" fontSize="xl" fontWeight="bold">
            In Service
          </FormLabel>
          <Switch
            colorScheme="teal"
            size="lg"
            id="lifecycle-operational"
            isChecked={
              !!lifecycle && lifecycle.includes(LIFE_CYCLE.OPERATIONAL) && lifecycle.includes(LIFE_CYCLE.FAULTY)
            }
            onChange={handleToggle}
          />
        </FormControl>
      </Flex>
      <TabList>
        <Tab>
          <Heading fontWeight="small" size="lg">
            Alarms
          </Heading>
        </Tab>
        <Tab>
          <Heading fontWeight="small" size="lg">
            Events
          </Heading>
        </Tab>
      </TabList>
      <TabPanels>
        <TabPanel p={0}>
          <Box bg="bg-surface" p="0" m="0">
            {tabIndex === 0 && <Alarms pathname={`/alarms-and-events/alarms`} lifecycle={lifecycle} />}
          </Box>
        </TabPanel>
        <TabPanel p={0}>
          <Box bg="bg-surface" p="0" m="0">
            {tabIndex === 1 && <Events lifecycle={lifecycle} />}
          </Box>
        </TabPanel>
      </TabPanels>
    </Tabs>
  );
};
export default AlarmsAndEvents;
