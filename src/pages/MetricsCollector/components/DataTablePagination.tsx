import { Box, Button, ButtonGroup, HStack, Select, Text } from '@chakra-ui/react';
import { Table } from '@tanstack/react-table';

interface DataTablePaginationProps<TData> {
  table: Table<TData>;
  pageSizeOptions?: number[];
  isPreviousData?: boolean;
  setLimit?: React.Dispatch<React.SetStateAction<number>>;
  limit?: number;
  isLoading?: boolean;
}

export function DataTablePagination<TData>({
  table,
  pageSizeOptions = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
  setLimit = () => ({}),
  limit,
  isLoading = false,
}: DataTablePaginationProps<TData>) {
  const totalRecords = (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize;
  const handleNext = (table: Table<TData>, totalRecords: number) => {
    if (limit && totalRecords === limit) {
      setLimit((old) => old + 1000);
    }
    table.nextPage();
  };
  const totalPages = Math.ceil(table.getPageCount() / table.getState().pagination.pageSize);
  return (
    <Box
      px={{
        base: '4',
        md: '6',
      }}
      pb="5"
    >
      <HStack spacing="3" justify="space-between">
        <Text color="muted" fontSize="sm">
          Showing {table.getState().pagination.pageIndex + 1} of {totalPages}
        </Text>
        <Select
          width={{ base: 'auto', md: 'auto' }}
          value={table.getState().pagination.pageSize}
          onChange={(e) => {
            table.setPageSize(Number(e.target.value));
          }}
        >
          {pageSizeOptions.map((pageSize) => (
            <option key={pageSize} value={pageSize}>
              Show {pageSize}
            </option>
          ))}
        </Select>
        <ButtonGroup
          spacing="3"
          justifyContent="space-between"
          width={{
            base: 'full',
            md: 'auto',
          }}
          variant="secondary"
        >
          <Button
            onClick={() => table.previousPage()}
            isDisabled={table.getState().pagination.pageIndex === 0 || isLoading}
          >
            Previous
          </Button>
          <Button
            onClick={() => handleNext(table, totalRecords)}
            isDisabled={totalPages === table.getState().pagination.pageIndex + 1 || isLoading}
          >
            Next
          </Button>
        </ButtonGroup>
      </HStack>
    </Box>
  );
}
