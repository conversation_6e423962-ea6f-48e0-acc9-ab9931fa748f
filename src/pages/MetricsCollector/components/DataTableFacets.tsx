import {
  Box,
  Button,
  Divider,
  Flex,
  Icon,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Text,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  IconButton,
  VStack,
  PopoverArrow,
  PopoverHeader,
  PopoverBody,
  PopoverFooter,
  HStack,
  Input,
  Tooltip,
} from '@chakra-ui/react';
import { Column } from '@tanstack/react-table';
import * as React from 'react';
import { FaFilter } from 'react-icons/fa';
import { GrCheckbox, GrCheckboxSelected } from 'react-icons/gr';
import { useLocation } from 'react-router-dom';
import { getStorageKey } from '../../../utils/helpers';
import { GiCancel } from 'react-icons/gi';
import { IoSearchCircleOutline } from 'react-icons/io5';
import { useState } from 'react';
import { useRef } from 'react';
import { useEffect } from 'react';

interface DataTableFacetedFilterProps<TData, TValue> {
  column?: Column<TData, TValue>;
  title?: string;
  filters: {
    label: string;
    value: string;
    icon?: React.ComponentType<{ className?: string }>;
  }[];
  selectedFilter?: Record<string, string>;
  isFiltered?: boolean;
}

export function DataTableFacetedFilter<TData, TValue>({
  column,
  title = '',
  filters,
  selectedFilter,
  isFiltered = false,
}: DataTableFacetedFilterProps<TData, TValue>) {
  const location = useLocation();
  const { severityCategory = '' } = location.state || {};
  const pathname = location.pathname;
  const facets = column?.getFacetedUniqueValues() ?? new Map<string, number>();

  const [selectedValues, setSelectedValues] = React.useState<Set<string>>(
    new Set(column?.getFilterValue() as string[])
  );
  const serializedFilter = React.useMemo(() => JSON.stringify(selectedFilter), [selectedFilter]);
  const [searchQuery, setSearchQuery] = React.useState('');

  // Load persisted filters from local storage when the pathname or column changes.
  React.useEffect(() => {
    const persistedFilters = JSON.parse(localStorage.getItem(getStorageKey(pathname)) || '{}');
    if (persistedFilters[title]) {
      setSelectedValues(new Set(persistedFilters[title]));
      column?.setFilterValue(persistedFilters[title]);
    } else {
      setSelectedValues(new Set());
      column?.setFilterValue(undefined);
    }
  }, [pathname, column, isFiltered]);

  // Manage changes in the severity category or selected filter.
  React.useEffect(() => {
    let filterValue = null;
    const currentFilter = JSON.parse(serializedFilter);

    if (title === 'Severity' && severityCategory) {
      filterValue = severityCategory;
    } else if (currentFilter && Object.keys(currentFilter).length) {
      selectedValues.clear();
      setSelectedValues(new Set());
      if (title === 'Region' && currentFilter['region_name']) {
        filterValue = currentFilter['region_name'];
      } else {
        column?.setFilterValue(undefined);
      }
      if (title === 'Status' && currentFilter['status']) {
        filterValue = currentFilter['status'];
      } else {
        column?.setFilterValue(undefined);
      }

      if (filterValue !== null) {
        selectedValues.clear();
        selectedValues.add(filterValue);
        setSelectedValues(selectedValues);
        const newFilterValues = Array.from(selectedValues);
        column?.setFilterValue(newFilterValues.length > 0 ? newFilterValues : undefined);
      }
    }
  }, [title, severityCategory, serializedFilter]);

  // Fix the useEffect for filter synchronization
  React.useEffect(() => {
    const persistedFilters = JSON.parse(localStorage.getItem(getStorageKey(pathname)) || '{}');
    const storedValues = persistedFilters[title] || [];

    // Only update if values actually changed
    if (JSON.stringify(storedValues) !== JSON.stringify(Array.from(selectedValues))) {
      setSelectedValues(new Set(storedValues));
      column?.setFilterValue(storedValues.length > 0 ? storedValues : undefined);
    }
  }, [pathname, title, serializedFilter]);

  const handleFilterSelection = (filterValue: string) => {
    const newSelectedValues = new Set(selectedValues);
    const wasSelected = newSelectedValues.has(filterValue);

    if (wasSelected) {
      newSelectedValues.delete(filterValue);
    } else {
      newSelectedValues.add(filterValue);
    }

    setSelectedValues(newSelectedValues);

    // Update column filter correctly
    const filterArray = Array.from(newSelectedValues);
    column?.setFilterValue(filterArray.length > 0 ? filterArray : undefined);

    // Update local storage
    const persistedFilters = JSON.parse(localStorage.getItem(getStorageKey(pathname)) || '{}');
    if (filterArray.length > 0) {
      persistedFilters[title] = filterArray;
    } else {
      delete persistedFilters[title];
    }
    localStorage.setItem(getStorageKey(pathname), JSON.stringify(persistedFilters));
  };

  const handleClearFilter = () => {
    setSelectedValues(new Set());
    column?.setFilterValue(undefined);

    const persistedFilters = JSON.parse(localStorage.getItem(getStorageKey(pathname)) || '{}');
    delete persistedFilters[title];
    localStorage.setItem(getStorageKey(pathname), JSON.stringify(persistedFilters));
  };

  const handleSelectAll = () => {
    const allValues = filters.map((f) => f.value);
    const newSelectedValues = selectedValues.size === allValues.length ? new Set<string>() : new Set(allValues);

    setSelectedValues(newSelectedValues);
    column?.setFilterValue(Array.from(newSelectedValues));

    const persistedFilters = JSON.parse(localStorage.getItem(getStorageKey(pathname)) || '{}');
    persistedFilters[title] = Array.from(newSelectedValues);
    localStorage.setItem(getStorageKey(pathname), JSON.stringify(persistedFilters));
  };

  const filteredFilters = React.useMemo(() => {
    if (!searchQuery) return filters;
    return filters.filter((f) => f.label.toLowerCase().includes(searchQuery.toLowerCase()));
  }, [filters, searchQuery]);

  return (
    <Popover placement="bottom" closeOnBlur={true} isLazy={true}>
      <PopoverTrigger>
        <Button
          variant="outline"
          size="sm"
          fontWeight="normal"
          bg={selectedValues.size > 0 ? 'brand.500' : 'white'}
          color="black"
          _hover={{
            color: 'white',
            bg: 'brand.400',
          }}
        >
          <FaFilter color="teal" size="15" />
          <Text ml="1">{title}</Text>
        </Button>
      </PopoverTrigger>

      <PopoverContent w="400px" h="400px" pt={2}>
        <PopoverArrow bg="white" borderTop="1px solid" borderLeft="1px solid" borderColor="teal.500" boxSize="14px" />
        <PopoverHeader bg="gray.50" top="0" zIndex="2">
          <HStack spacing={2}>
            <Box flex="1">
              <InputGroup
                borderWidth="1px"
                borderRadius="md"
                borderColor="blue.500"
                _hover={{ borderColor: 'blue.500' }}
                _focus={{
                  borderColor: 'blue.500',
                }}
              >
                <InputLeftElement pointerEvents="none">
                  <IconButton
                    aria-label="Search"
                    icon={<IoSearchCircleOutline size="30px" color="teal" />}
                    size="sm"
                    variant="ghost"
                  />
                </InputLeftElement>

                {searchQuery && (
                  <InputRightElement>
                    <IconButton
                      aria-label="Clear input"
                      icon={<GiCancel color="red" size="20px" />}
                      size="sm"
                      variant="ghost"
                      onClick={() => setSearchQuery('')}
                      _hover={{ color: 'red.500' }}
                      boxSize="6"
                    />
                  </InputRightElement>
                )}

                <Input
                  size="md"
                  placeholder={`Search ${title}`}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  borderColor="blue.500"
                  _hover={{ borderColor: 'blue.500' }}
                  _focus={{
                    borderColor: 'blue.500',
                  }}
                />
              </InputGroup>
            </Box>
          </HStack>

          <Divider my={2} />

          <Box display="flex" justifyContent="start" mb={1}>
            <Button
              variant="ghost"
              width="100%"
              borderRadius="0"
              fontWeight="normal"
              color="gray.800"
              bg={'brand.100'}
              _hover={{
                color: 'white',
                bg: 'brand.300',
              }}
              onClick={handleSelectAll}
            >
              <Flex minWidth="100%" gap="2" py="2">
                <Box>
                  <Icon
                    color={'brand.500'}
                    as={selectedValues.size === filters.length ? GrCheckboxSelected : GrCheckbox}
                  />
                </Box>
                <Box>
                  <Text>Select All</Text>
                </Box>
              </Flex>
            </Button>
          </Box>
        </PopoverHeader>

        <PopoverBody overflowX="auto" overflowY="auto" position="relative" pt={0}>
          <VStack align="start" spacing={1}>
            {filteredFilters.map((filter) => {
              const isSelected = selectedValues.has(filter.value);

              return (
                <HStack key={filter.value} spacing={2} w="100%" justifyContent="space-between" alignItems="center">
                  <Button
                    variant="ghost"
                    width="100%"
                    borderRadius="0"
                    fontWeight="normal"
                    color="gray.800"
                    bg={isSelected ? 'brand.200' : 'white'}
                    _hover={{
                      color: 'white',
                      bg: 'brand.200',
                    }}
                    onClick={() => handleFilterSelection(filter.value)}
                  >
                    <Flex minWidth="100%" py="2">
                      <Box width="10%" flexShrink={0}>
                        <Icon ml="20px" color={'brand.500'} as={isSelected ? GrCheckboxSelected : GrCheckbox} />
                      </Box>

                      <Box ml="2" width="80%" overflow="hidden" textAlign="left">
                        <TruncatedText>{filter.label}</TruncatedText>
                      </Box>

                      <Box width="10%" flexShrink={0} textAlign="right">
                        <Text>{facets.get(filter.value) || 0}</Text>
                      </Box>
                    </Flex>
                  </Button>
                </HStack>
              );
            })}
          </VStack>
        </PopoverBody>

        <PopoverFooter>
          <Flex justifyContent="flex-end">
            <Button colorScheme="teal" size="sm" onClick={handleClearFilter} isDisabled={selectedValues.size === 0}>
              Clear
            </Button>
          </Flex>
        </PopoverFooter>
      </PopoverContent>
    </Popover>
  );
}

const TruncatedText = ({ children }: { children: string }) => {
  const textRef = useRef<HTMLParagraphElement>(null);
  const [isTruncated, setIsTruncated] = useState(false);

  useEffect(() => {
    const checkTruncation = () => {
      const element = textRef.current;
      if (element) {
        setIsTruncated(element.scrollWidth > element.clientWidth);
      }
    };

    checkTruncation();
    window.addEventListener('resize', checkTruncation);
    return () => window.removeEventListener('resize', checkTruncation);
  }, [children]);

  return isTruncated ? (
    <Tooltip label={children} placement="top" hasArrow>
      <Text ref={textRef} isTruncated>
        {children}
      </Text>
    </Tooltip>
  ) : (
    <Text ref={textRef} isTruncated>
      {children}
    </Text>
  );
};
