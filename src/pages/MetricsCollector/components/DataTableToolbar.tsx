import { Box, Button, Flex, Text } from '@chakra-ui/react';
import { Column, Table } from '@tanstack/react-table';
import { useEffect, useState } from 'react';
import { DataTableFacetedFilter } from './DataTableFacets';
import { DebouncedInput } from './DebounceInput';
import _ from 'lodash';
import DisplayColumns from './DisplayColumns';
import { MdAutorenew } from 'react-icons/md';
import { useNavigate } from 'react-router';
import { getStorageKey } from '../../../utils/helpers';

interface DataTableToolbarProps<TData> {
  table: Table<TData>;
  enableFilter?: boolean;
  alternativeViewId?: string;
  hiddenColumnsByDefault?: any;
  urlCellRef?: string;
  urlNodeId?: string;
  setPageCount?: any;
  count?: number;
  setLimit?: any;
  data?: any;
  version?: string;
  selectedFilter?: Record<string, string>;
  showSearch?: boolean;
}

interface MenuItemOptionProps {
  label: string;
  value: string;
}

function createFacetedFilters<TData>(
  table: Table<TData>,
  hiddenColumnsByDefault: string[]
): Record<string, MenuItemOptionProps[]> {
  const facetedFilters: Record<string, MenuItemOptionProps[]> = {};

  table.getAllColumns().forEach((column: Column<TData>) => {
    if (column.getIsVisible() && column.columnDef.filterFn !== 'auto') {
      const uniqueValues = column.getFacetedUniqueValues ? column.getFacetedUniqueValues() : new Map();
      const filteredUniqueValues = _.chain(Array.from(uniqueValues))
        .filter(([key]) => !_.isUndefined(key) && !_.isEmpty(key) && key !== 'N/A' && !_.isObject(key))
        .value();

      // sort them alphabetically
      filteredUniqueValues.sort((a, b) => a[0].localeCompare(b[0]));

      facetedFilters[column.id] = _.chain(filteredUniqueValues)
        .map(([value]) => ({
          label: _.isString(value) ? value : 'Undefined',
          value: _.isString(value) ? value : 'undefined',
        }))
        .value() as MenuItemOptionProps[];
    }
  });
  return facetedFilters;
}

export function DataTableToolbar<TData>({
  table,
  enableFilter = false,
  hiddenColumnsByDefault = [],
  alternativeViewId,
  urlCellRef,
  urlNodeId,
  setPageCount,
  count,
  setLimit,
  data,
  version,
  selectedFilter = {},
  showSearch,
}: DataTableToolbarProps<TData>) {
  const tabName = (table.options.columns[0] as Column<TData>).id;

  const isFiltered = table.getState().columnFilters.length > 0;
  // Generate faceted filters for all columns except those that are hidden
  const filterFacets = createFacetedFilters(table, hiddenColumnsByDefault);
  const [inputValue, setInputValue] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    if (version === 'v2') {
      if (table.getState().columnFilters?.length) {
        setPageCount(table.getFilteredRowModel().rows.length);
      } else {
        setPageCount(count);
      }
    }
  }, [table.getFilteredRowModel().rows.length, data, table, version]);

  useEffect(() => {
    let valueToSet = table.getState().globalFilter ?? '';
    const cellRefId = urlCellRef ? urlCellRef : '';
    const nodeId = urlNodeId ? urlNodeId : '';

    if (cellRefId || nodeId) {
      valueToSet = cellRefId || nodeId;
    } else if (alternativeViewId && (tabName === 'node_id' || tabName === 'cell_ref')) {
      valueToSet = alternativeViewId;
    } else if (alternativeViewId) {
      valueToSet = alternativeViewId;
    }

    setInputValue(valueToSet);
    table.setGlobalFilter(valueToSet);
  }, [alternativeViewId, tabName, table, urlCellRef, urlNodeId]);

  const removeLocationAttribute = (id?: string) => {
    let newPath;
    if (id) {
      newPath = location.pathname.replace(`/${id}`, '');
      newPath = newPath.replace(/\/$/, '');
    } else {
      newPath = location.pathname;
    }
    navigate(newPath, { replace: true });
  };

  return (
    <Box data-testid="data_table_toolbar" mt="4">
      <Flex direction="column" justifyItems="center" alignItems="center">
        <Flex direction="column" gap={5}>
          {showSearch && (
            <Flex justify="center" align="center" gap={5} w="100%">
              <Box alignSelf="end" border="1px solid" borderRadius="md" width="250px">
                <DebouncedInput
                  value={table.getState().globalFilter || inputValue}
                  onChange={(value) => {
                    setInputValue(String(value));
                    table.setGlobalFilter(String(value));
                  }}
                  removePathParam={removeLocationAttribute}
                  placeholder="Search all columns..."
                />
              </Box>
              <Box alignSelf="end" borderRadius="md">
                <DisplayColumns table={table} />
              </Box>
              <Box></Box>
            </Flex>
          )}

          {enableFilter && (
            //NOTE: Added flex wrap for the filter options
            <Flex justify="start" align="center" gap={5} flexWrap="wrap">
              {Object.entries(filterFacets).map(([facetKey, filters]) => {
                const column = table.getColumn(facetKey);
                if (column) {
                  let title: string;
                  if (typeof column.columnDef.header === 'string') {
                    title = column.columnDef.header;
                  } else {
                    // When columnDef.header is not a function or string, or if it's undefined
                    title = 'Cell count';
                  }

                  return (
                    <DataTableFacetedFilter
                      key={facetKey}
                      column={column as Column<unknown, unknown>}
                      title={title}
                      filters={filters}
                      selectedFilter={selectedFilter}
                      isFiltered={isFiltered}
                    />
                  );
                }
                return null;
              })}

              <Button
                colorScheme="teal"
                variant="solid"
                onClick={() => (
                  table.resetColumnFilters(),
                  removeLocationAttribute(),
                  localStorage.removeItem(getStorageKey(location.pathname))
                )}
                size="sm"
                isDisabled={!isFiltered}
              >
                <Text>Reset Filters</Text>
                <MdAutorenew size="25" />
              </Button>
            </Flex>
          )}
        </Flex>
      </Flex>
    </Box>
  );
}
