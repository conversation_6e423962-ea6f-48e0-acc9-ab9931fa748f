import { VscError } from 'react-icons/vsc';
import { LuRefreshCw } from 'react-icons/lu';
import { IoCheckmarkCircleOutline } from 'react-icons/io5';
import { IconContext } from 'react-icons';
import { BsHandThumbsUp } from 'react-icons/bs';

export type AlarmStatus = 'new' | 'updated' | 'acknowledged' | 'resolved';

interface AlarmIconProps {
  status: AlarmStatus;
  size?: number;
}

const alarmStyles = {
  new: {
    icon: <VscError />,
    color: '#E53E3E', // Red
  },
  updated: {
    icon: <LuRefreshCw />,
    color: '#3182CE', // Blue
  },
  acknowledged: {
    icon: <BsHandThumbsUp />,
    color: '#ED8936', // Teal
  },
  resolved: {
    icon: <IoCheckmarkCircleOutline />,
    color: '#38A169', // Green
  },
};

const AlarmStatusIcon = ({ status, size = 24 }: AlarmIconProps) => {
  const { icon, color } = alarmStyles[status];

  return <IconContext.Provider value={{ color, size: size.toString() }}>{icon}</IconContext.Provider>;
};

export default AlarmStatusIcon;
