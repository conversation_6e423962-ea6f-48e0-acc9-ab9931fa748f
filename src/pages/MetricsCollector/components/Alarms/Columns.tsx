import { ColumnDef } from '@tanstack/react-table';
import { AlarmElement } from '../../../../types/metricCollector.type';
import { Box, Flex, Text, Tooltip } from '@chakra-ui/react';
import { BsArrowReturnRight } from 'react-icons/bs';
import Chip from '../../../../components/chip/Chip';
import { formatInReadableTimeDate } from '../../../../utils/formatInReadableTimeData';
import { FaHardHat } from 'react-icons/fa';
import { MdFactory } from 'react-icons/md';
import { FaTools, FaCheckCircle, FaTimesCircle } from 'react-icons/fa';
import { TimeElapsed } from '../../../../components/nodeComponents/server/acp/AcpCard';
import AlarmStatusIcon from './AlarmStatusIcon';
import intervalToDuration from 'date-fns/intervalToDuration';
import formatDuration from 'date-fns/formatDuration';

// Mapping of status types to icons with descriptive colors and configurations
const lifecycleStatusIcons = (lifecycle: string) => {
  const iconConfig = {
    FACTORY: {
      icon: MdFactory,
      color: '#4A5568',
      size: 24,
      tooltip: 'In Factory',
    },
    STAGING: {
      icon: FaHardHat,
      color: '#ED8936',
      size: 24,
      tooltip: 'Staging in Progress',
    },
    COMMISSIONING: {
      icon: FaTools,
      color: '#4299E1',
      size: 24,
      tooltip: 'Commissioning Phase',
    },
    OPERATIONAL: {
      icon: FaCheckCircle,
      color: '#48BB78',
      size: 24,
      tooltip: 'Fully Operational',
    },
    DECOMMISSIONED: {
      icon: FaTimesCircle,
      color: '#E53E3E',
      size: 24,
      tooltip: 'Decommissioned',
    },
  };

  return iconConfig[lifecycle as keyof typeof iconConfig];
};

export default function AlarmColumns({ openAlarms }: { openAlarms: boolean }): ColumnDef<AlarmElement>[] {
  const commonCols: ColumnDef<AlarmElement>[] = [
    {
      id: 'expander',
      cell: (props) => {
        const expander_data = props.row.original.expander as string;
        return (
          <>
            <Flex>
              <BsArrowReturnRight />
              <Text ml={2}>{expander_data}</Text>
            </Flex>
          </>
        );
      },
    },
    {
      header: 'Id',
      accessorKey: 'id',
    },
    {
      header: 'Event Name',
      accessorKey: 'event_name',
      id: 'eventName',
      accessorFn: (originalRow) => {
        // if subRows is defined and has at least one elem
        if (originalRow.subRows && originalRow.subRows?.length > 0) {
          return originalRow.subRows[0].event_name || 'N/A';
        }
        return 'N/A';
      },
      cell: ({ getValue }) => {
        const value = getValue() as string;
        const shouldShowTooltip = value && value.length > 30;
        const content = (
          <Box isTruncated maxWidth="30ch">
            {value}
          </Box>
        );
        return shouldShowTooltip ? <Tooltip label={value}>{content}</Tooltip> : content;
      },
    },
    {
      header: 'Type',
      accessorKey: 'type',
      id: 'alarmsType',
      cell: ({ getValue }) => {
        const value = getValue() as string;
        const shouldShowTooltip = value && value.length > 30;
        const content = (
          <Box isTruncated maxWidth="30ch">
            {value}
          </Box>
        );
        return shouldShowTooltip ? <Tooltip label={value}>{content}</Tooltip> : content;
      },
    },
    {
      header: 'Severity',
      accessorKey: 'severity',
      cell: ({ getValue }) => <Chip statusText={getValue() as string} hasStatusLight />,
    },
    {
      header: 'Status',
      accessorKey: 'status',
      cell: ({ getValue }) => {
        const status = getValue() as string;
        if (status === 'new') {
          return (
            <Tooltip label="New Alarm" hasArrow>
              <AlarmStatusIcon status="new" />
            </Tooltip>
          );
        } else if (status === 'acknowledged') {
          return (
            <Tooltip label="Acknowledged" hasArrow>
              <AlarmStatusIcon status="acknowledged" size={24} />
            </Tooltip>
          );
        } else if (status === 'updated') {
          return (
            <Tooltip label="Updated" hasArrow>
              <AlarmStatusIcon status="updated" size={24} />
            </Tooltip>
          );
        } else if (status === 'resolved') {
          return (
            <Tooltip label="Resolved" hasArrow>
              <AlarmStatusIcon status="resolved" size={24} />
            </Tooltip>
          );
        }
        return null;
      },
    },
    {
      header: 'Created',
      accessorKey: 'created',
      id: 'created',
      cell: ({ getValue }) => <>{getValue() ? formatInReadableTimeDate(getValue() as string).toString() : null}</>,
    },
    {
      header: 'Object Type',
      accessorKey: 'object_type',
      id: 'objectType',
      accessorFn: (originalRow) => {
        // if subRows is defined and has at least one elem
        if (originalRow.subRows && originalRow.subRows?.length > 0) {
          return originalRow.subRows[0].object_type || 'N/A';
        }
        return 'N/A';
      },
    },
    {
      header: 'Roles',
      accessorKey: 'inventory',
      id: 'roles',
      accessorFn: (originalRow) => {
        const node = originalRow?.inventory?.node || {};
        const roles = node?.roles?.toString().split(',').join(', ') || 'N/A';
        return roles;
      },
      cell: ({ getValue }) => {
        const roles = getValue() as string | undefined;
        const shouldShowTooltip = roles && roles.length > 29;
        const content = (
          <Box isTruncated maxWidth="26ch">
            {roles}
          </Box>
        );
        return (shouldShowTooltip ? <Tooltip label={roles}>{content}</Tooltip> : roles) || 'N/A';
      },
    },
    {
      header: 'Country Code',
      accessorFn: (originalRow) => {
        const cells = originalRow.inventory?.cells || [];
        return Array.from(new Set(cells.map((cell: any) => cell.country_code))).join(', ') || 'N/A';
      },
      id: 'country_code',
      cell: ({ getValue }) => {
        const countryCodes = getValue() as string;
        const shouldShowTooltip = countryCodes.length > 29;
        const content = (
          <Box isTruncated maxWidth="26ch">
            {countryCodes}
          </Box>
        );
        return shouldShowTooltip ? <Tooltip label={countryCodes}>{content}</Tooltip> : countryCodes;
      },
      filterFn: (row, id, value) => {
        const countryCodes = row.getValue(id) as string;
        return countryCodes.split(', ').some((code) => value.includes(code));
      },
    },
    {
      header: 'Region Name',
      accessorFn: (originalRow) => {
        const cells = originalRow.inventory?.cells || [];
        return Array.from(new Set(cells.map((cell: any) => cell.region_name))).join(', ') || 'N/A';
      },
      id: 'region_name',
      cell: ({ getValue }) => {
        const regionNames = getValue() as string;
        const shouldShowTooltip = regionNames.length > 29;
        const content = (
          <Box isTruncated maxWidth="26ch">
            {regionNames}
          </Box>
        );
        return shouldShowTooltip ? <Tooltip label={regionNames}>{content}</Tooltip> : regionNames;
      },
      filterFn: (row, id, value) => {
        const regionNames = row.getValue(id) as string;
        return regionNames.split(', ').some((name) => value.includes(name));
      },
    },
    {
      header: 'Object Id',
      accessorKey: 'object_id',
      id: 'objectId',
      accessorFn: (originalRow) => {
        if (originalRow.subRows && originalRow.subRows?.length > 0) {
          return originalRow.subRows[0].object_id || 'N/A';
        }
        return 'N/A';
      },
      cell: ({ getValue }) => {
        const value = getValue() as string;
        const shouldShowTooltip = value && value.length > 20;
        const content = (
          <Box isTruncated maxWidth="20ch">
            {value}
          </Box>
        );
        return shouldShowTooltip ? <Tooltip label={value}>{content}</Tooltip> : content;
      },
    },
    {
      header: 'Node Id',
      accessorKey: 'inventory',
      id: 'alarmNodeId',
      accessorFn: (originalRow) => {
        return originalRow.inventory?.node?.node_id || 'N/A';
      },
    },
    {
      header: 'Node Type',
      accessorKey: 'inventory',
      id: 'alarmNodeType',
      accessorFn: (originalRow) => {
        return originalRow?.inventory?.node?.node_type || 'N/A';
      },
    },
    {
      header: 'Cell Reference',
      accessorKey: 'inventory',
      id: 'alarm_cell_refs',
      accessorFn: (originalRow) => {
        const node = originalRow?.inventory?.node || {};
        const cell_refs = node?.cell_ids?.toString() || node?.cell_refs?.toString() || 'N/A';
        return cell_refs;
      },
      cell: ({ getValue }) => {
        // check if cell_refs is an empty array or undefined and return a placeholder
        const cellRefs = getValue() as string | undefined;
        const shouldShowTooltip = cellRefs && cellRefs.length > 20;
        const content = (
          <Box isTruncated maxWidth="20ch">
            {cellRefs}
          </Box>
        );
        return (shouldShowTooltip ? <Tooltip label={cellRefs}>{content}</Tooltip> : cellRefs) || 'N/A';
      },
    },
    {
      header: 'Site Name',
      accessorKey: 'inventory',
      id: 'alarmSiteName',
      accessorFn: (originalRow) => {
        return originalRow?.inventory?.node?.site_name || 'N/A';
      },
    },
    {
      header: 'Lifecycle',
      id: 'lifecycle',
      accessorKey: 'inventory',
      accessorFn: (originalRow) => {
        const node = originalRow?.inventory?.node || {};
        const lifecycle = node?.lifecycle || 'N/A';
        return lifecycle;
      },
      cell: ({ getValue }) => {
        const lifecycle = getValue() as string;
        const config = lifecycleStatusIcons(lifecycle);
        return config ? (
          <Text>{lifecycle}</Text>
        ) : (
          // TODO: We need this in future to show the icon
          // <Box position="relative" display="inline-block">
          //   <Tooltip label={config.tooltip} hasArrow>
          //     <Box display="flex" alignItems="center">
          //       <config.icon size={config.size} color={config.color} />
          //     </Box>
          //   </Tooltip>
          // </Box>
          'N/A'
        );
      },
    },
    {
      header: 'Problem',
      accessorKey: 'specific_problem',
      cell: ({ getValue }) => {
        const value = getValue() as string;
        const shouldShowTooltip = value && value.length > 20;
        const content = (
          <Box isTruncated maxWidth="20ch">
            {value}
          </Box>
        );
        return shouldShowTooltip ? <Tooltip label={value}>{content}</Tooltip> : content;
      },
    },
  ];
  const createdIndex = commonCols.findIndex((col) => col.id === 'created');

  if (openAlarms) {
    const updatedCol: ColumnDef<AlarmElement> = {
      accessorKey: 'updated',
      header: 'Updated',
      cell: ({ getValue }) => <>{getValue() ? formatInReadableTimeDate(getValue() as string).toString() : null}</>,
    };
    const openedForCol: ColumnDef<AlarmElement> = {
      id: 'opened_for',
      accessorKey: 'opened_for',
      header: 'Opened For',
      cell: ({ row }) => {
        const createdDate = row.original.created;
        return <TimeElapsed key={row.original.id} initialUptime={createdDate?.toString() || 'N/A'} />;
      },
    };
    // Insert after 'created'
    commonCols.splice(createdIndex + 1, 0, updatedCol, openedForCol);
  } else {
    const updatedCol: ColumnDef<AlarmElement> = {
      accessorKey: 'updated',
      header: 'Resolved at',
      cell: ({ getValue }) => <>{getValue() ? formatInReadableTimeDate(getValue() as string).toString() : null}</>,
    };
    const openedForCol: ColumnDef<AlarmElement> = {
      id: 'opened_for',
      header: 'Opened For',
      cell: ({ row }) => {
        const createdDate = row.original.created;
        const updatedDate = row.original.updated;
        if (!createdDate || !updatedDate) {
          return <Text> Not Available</Text>;
        }
        const duration = intervalToDuration({ start: new Date(createdDate), end: new Date(updatedDate) });
        return <Text>{formatDuration(duration, { format: ['days', 'hours', 'minutes', 'seconds'] })}</Text>;
      },
    };
    // Insert after 'created'
    commonCols.splice(createdIndex + 1, 0, updatedCol, openedForCol);
  }

  return commonCols;
}
