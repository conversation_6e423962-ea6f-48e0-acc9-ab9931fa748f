import React, { useCallback, useEffect, useState } from 'react';
import { <PERSON>, Spinner, Stack, useDisclosure } from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { getAlarmsFacetSummary } from '../../../../services/metricsCollector';
import { AlarmElement } from '../../../../types/metricCollector.type';
import EventsByAlarmId from './EventsByAlarm';
import useUpdateAlarm from '../../hooks/useUpdateAlarm';
import useResolveAlarm from '../../hooks/useResolveAlarm';
import ServerFacet from '../ServerFacets/ServerFacets';
import { SelectedFilters } from '../ServerFacets/types';
import { alarmFacetsDefinition } from './FacetsDefinition';
import { ResolutionModal } from './ResolutionModal';
import { useUserInfo } from './hooks/useUserInfo';
import { useAlarmTableData } from './hooks/useAlarmTableData';
import { AlarmSubComponent } from './SubComponent/AlarmSubComponent';
import { useParams } from 'react-router-dom';
import { DataTable } from '../DataTable';
import AlarmColumns from './Columns';
import { getStorageKey } from '../../../../utils/helpers';
import { LIFE_CYCLE } from '../../../../data/constants';

const Alarms = ({
  useServerFacets = true,
  nodeId,
  pathname,
  openAlarms = true,
  lifecycle,
}: {
  useServerFacets?: boolean;
  nodeId?: string;
  pathname: string;
  openAlarms?: boolean;
  lifecycle?: LIFE_CYCLE[] | null;
}) => {
  const [selectedAlarm, setSelectedAlarm] = useState<AlarmElement | null>(null);
  const [showRelatedData, setShowRelatedData] = useState<boolean>(false);
  const [alarmLimit, setAlarmLimit] = useState<number>(1000);
  const [resolveExpandable, setResolveExpandable] = useState<boolean>(false);
  const [alarmFilters, setAlarmFilters] = useState<SelectedFilters>({});
  const [initialFiltersLoaded, setInitialFiltersLoaded] = useState<boolean>(false);

  // Load filters from local storage on mount and set the loaded flag
  useEffect(() => {
    let isMounted = true;
    const storedFilters = localStorage.getItem(getStorageKey(pathname)) || '{}';
    if (storedFilters) {
      try {
        const storedFiltersObject = JSON.parse(storedFilters);
        let initialFilters = storedFiltersObject;

        if (nodeId) {
          initialFilters = {
            ...storedFiltersObject,
            node_id: { ...storedFiltersObject.node_id, [nodeId]: true },
          };
        }

        if (isMounted) {
          setAlarmFilters(initialFilters);
          setInitialFiltersLoaded(true);
        }
      } catch (error) {
        console.error('Error parsing stored filters', error);
        if (isMounted) {
          setInitialFiltersLoaded(true);
        }
      }
    } else {
      if (isMounted) {
        if (nodeId) {
          setAlarmFilters({ node_id: { [nodeId]: true } });
        }
        setInitialFiltersLoaded(true);
      }
    }

    return () => {
      isMounted = false;
    };
  }, [nodeId, pathname]);

  const { isOpen, onOpen, onClose } = useDisclosure();
  const { updateAlarmMutation } = useUpdateAlarm();
  const { resolveAlarmMutation } = useResolveAlarm();
  const { userFullName } = useUserInfo();
  const { id: urlCellRef } = useParams();

  // Pass the initialFiltersLoaded flag to the enabled parameter of the hook
  const { alarmList, isLoading } = useAlarmTableData(
    alarmFilters,
    alarmLimit,
    openAlarms,
    initialFiltersLoaded, // Query is only enabled when true
    lifecycle
  );

  const { isLoading: isLoadingAlarmsFacetSummary, data: alarmsFacetSummary } = useQuery({
    queryKey: ['getAlarmsFacetSummary'],
    queryFn: () => getAlarmsFacetSummary(),
    enabled: useServerFacets, // Only fetch summary if facets are used
  });

  // Persist filters to local storage whenever they change (only after initial load)
  useEffect(() => {
    if (initialFiltersLoaded) {
      localStorage.setItem(getStorageKey(pathname), JSON.stringify(alarmFilters));
    }
  }, [alarmFilters, initialFiltersLoaded, pathname]);

  const handleApplyFilters = (newFilters: SelectedFilters) => {
    setAlarmFilters(newFilters);
    // Local storage update is handled by the separate useEffect above
  };

  const handleResolveAlarm = useCallback(
    (alarm: AlarmElement) => {
      setSelectedAlarm(alarm);
      onOpen();
    },
    [setSelectedAlarm, onOpen]
  );

  const handleShowEventsForAlarm = useCallback(
    (alarm: AlarmElement) => {
      setSelectedAlarm(alarm);
      setShowRelatedData(true);
    },
    [setSelectedAlarm, setShowRelatedData]
  );

  const handleAcknowledgeAlarm = useCallback(
    (alarm: AlarmElement) => {
      updateAlarmMutation({
        alarm_id: String(alarm.id),
        alarm: {
          status: 'acknowledged',
          acknowledged: new Date(),
          acknowledger: userFullName,
        },
      });
    },
    [updateAlarmMutation, userFullName]
  );

  const handleCloseModal = () => {
    onClose();
    setSelectedAlarm(null);
  };

  const HIDDEN_COLUMNS = {
    alarmsType: false,
    alarmSiteName: false,
    alarmNodeType: false,
    roles: false,
    country_code: false,
    status: false,
    alarm_cell_refs: false,
    alarmNodeId: nodeId ? false : true,
  };

  const renderSubComponent = useCallback(
    ({ row }: { row: { original: AlarmElement } }) => (
      <AlarmSubComponent
        row={row}
        onShowEventsForAlarm={handleShowEventsForAlarm}
        onResolveAlarm={handleResolveAlarm}
        onAcknowledgeAlarm={handleAcknowledgeAlarm}
      />
    ),
    [handleShowEventsForAlarm, handleResolveAlarm, handleAcknowledgeAlarm]
  );

  // Show spinner only if the query is enabled and loading
  const showLoading = initialFiltersLoaded && isLoading;
  const columns = AlarmColumns({ openAlarms });

  return (
    <>
      <Box bg="bg-surface">
        {useServerFacets && (
          <>
            {isLoadingAlarmsFacetSummary ? (
              <Spinner />
            ) : (
              alarmsFacetSummary && (
                <ServerFacet
                  facetSummary={alarmsFacetSummary}
                  onApply={handleApplyFilters}
                  facetsDefinition={alarmFacetsDefinition}
                  pathname={pathname}
                />
              )
            )}
          </>
        )}

        <Stack spacing="5">
          <DataTable
            isExpandable={true}
            enableFilter={false}
            renderSubComponent={renderSubComponent}
            getRowCanExpand={(row) => !!row.original.subRows}
            isLoading={showLoading} // Use the controlled loading state
            hasEmptyResult={initialFiltersLoaded && alarmList?.length === 0} // Show empty only after load attempt
            columns={columns}
            data={alarmList as AlarmElement[]}
            pageSizeOptions={[10, 20, 30, 40, 50, 60, 70, 80, 90, 100]}
            defaultPageSize={100}
            hiddenColumnsByDefault={HIDDEN_COLUMNS}
            defaultSortingColumn={[{ id: 'updated', desc: true }]}
            count={alarmList[0]?.count}
            limit={alarmLimit}
            setLimit={setAlarmLimit}
            resolveExpandable={resolveExpandable}
            setresolveExpandable={setResolveExpandable}
            version="v2"
            urlCellRef={urlCellRef}
          />
        </Stack>
      </Box>

      {/* Related events view */}
      {selectedAlarm && showRelatedData && (
        <EventsByAlarmId
          internal_id={selectedAlarm.internal_id}
          alarm_id={String(selectedAlarm.id)}
          setSelectedAlarm={setSelectedAlarm}
          setShowRelatedData={setShowRelatedData}
        />
      )}

      {/* Resolution modal */}
      <ResolutionModal
        isOpen={isOpen}
        onClose={handleCloseModal}
        selectedAlarm={selectedAlarm}
        resolveAlarmMutation={resolveAlarmMutation}
        setResolveExpandable={setResolveExpandable}
      />
    </>
  );
};

export default Alarms;
