import * as React from 'react';
import {
  Box,
  But<PERSON>,
  Stack,
  useBreakpointValue,
  useColorModeValue,
  useDisclosure,
  Modal,
  ModalCloseButton,
  ModalContent,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
} from '@chakra-ui/react';
import { AlarmElement } from '../../../../types/metricCollector.type';
import { DataTable } from '../DataTable';
import { useEventListByAlarmId } from '../../hooks/useEventList';
import { transformEvents, getHiddenColumnsByDefault, getEventColumns, useRenderSubComponent } from '../Events/Utils';

interface EventsByAlarmIdProps {
  internal_id: string;
  alarm_id: string;
  setSelectedAlarm: React.Dispatch<React.SetStateAction<AlarmElement | null>>;
  setShowRelatedData: React.Dispatch<React.SetStateAction<boolean>>;
}

const EventsByAlarmId: React.FC<EventsByAlarmIdProps> = ({
  internal_id,
  alarm_id,
  setSelectedAlarm,
  setShowRelatedData,
}) => {
  const { data: events, isLoading, isFetching } = useEventListByAlarmId(internal_id);

  const { count, events: eventList } = events ?? { count: 0, events: [] };
  const { isOpen: isVisible, onClose, onOpen } = useDisclosure();

  React.useEffect(() => {
    if (count > 0) {
      onOpen();
    }
  }, [count, onOpen]);

  const data = React.useMemo(() => transformEvents(eventList), [eventList]);
  const hiddenColumnsByDefault = React.useMemo(() => getHiddenColumnsByDefault(), []);
  const renderSubComponent = useRenderSubComponent();
  const columns = React.useMemo(() => getEventColumns(), []);

  const boxShowColor = useColorModeValue('sm', 'sm-dark');
  const borderRadiusVal = useBreakpointValue({
    base: 'none',
    md: 'lg',
  });

  return (
    <Modal
      isOpen={isVisible}
      onClose={() => {
        onClose;
        setSelectedAlarm(null);
        setShowRelatedData(false);
      }}
      size="6xl"
    >
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Events for Alarm ID: {alarm_id} </ModalHeader>
        <ModalCloseButton />
        <Stack
          spacing={{
            base: '5',
            lg: '6',
          }}
        >
          {data?.length > 0 && (
            <Box
              bg="bg-surface"
              boxShadow={{
                base: 'none',
                md: boxShowColor,
              }}
              borderRadius={borderRadiusVal}
            >
              <Stack spacing="5">
                <Box
                  px={{
                    base: '4',
                    md: '6',
                  }}
                  pt="5"
                />
                <DataTable
                  isExpandable={true}
                  enableFilter={true}
                  columns={columns}
                  data={data}
                  pageSizeOptions={[100, 200, 300, 400, 500, 600, 700, 800, 900, 1000]}
                  isLoading={isLoading && isFetching}
                  defaultPageSize={100}
                  hasEmptyResult={eventList?.length == 0}
                  renderSubComponent={renderSubComponent}
                  getRowCanExpand={(row) => !!row.original.subRows}
                  version={'v2'}
                  hiddenColumnsByDefault={hiddenColumnsByDefault}
                />
              </Stack>
              <ModalFooter>
                <Button
                  onClick={() => {
                    onClose;
                    setSelectedAlarm(null);
                  }}
                >
                  Close
                </Button>
              </ModalFooter>
            </Box>
          )}
        </Stack>
      </ModalContent>
    </Modal>
  );
};
export default EventsByAlarmId;
