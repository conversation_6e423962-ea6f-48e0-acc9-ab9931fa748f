import * as React from 'react';
import {
  Alert,
  AlertDescription,
  AlertIcon,
  AlertTitle,
  Box,
  CloseButton,
  Spinner,
  useDisclosure,
} from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { formatISO, subHours } from 'date-fns';
import { useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useParams } from 'react-router-dom';
import { getEventsFacetSummary } from '../../../../services/metricsCollector';
import { EventHeader } from '../../../../types/metricCollector.type';
import { DataTable } from '../DataTable';
import useEventList from '../../hooks/useEventList';
import { SelectedFilters } from '../ServerFacets/types';
import ServerFacet from '../ServerFacets/ServerFacets';
import { eventFacetsDefinition } from './FacetsDefinition';
import { DateRangePicker } from '../../../../components/dateRangePicker/DateRangePicker';
import { getStorageKey } from '../../../../utils/helpers';
import { transformEvents, getHiddenColumnsByDefault, getEventColumns, useRenderSubComponent } from './Utils';
import { LIFE_CYCLE } from '../../../../data/constants';

export function stripZFromEnd(dateStr: string): string {
  if (!dateStr) return '';
  // strip Z from dateStr
  dateStr = dateStr.replace('Z', '');
  return dateStr;
}

const Events = ({ lifecycle }: { lifecycle?: LIFE_CYCLE[] | null }) => {
  const [filters, setFilters] = useState<{
    [key: string]: { [key: string]: boolean };
  }>({});

  const [limit, setLimit] = useState<number>(1000);
  const [eventsList, setEventsList] = useState<EventHeader[] | []>([]);
  const [eventsCount, setEventsCount] = useState(0);
  const methods = useForm({
    defaultValues: {
      fromDate: stripZFromEnd(formatISO(subHours(new Date(), 24))),
      toDate: stripZFromEnd(formatISO(new Date())),
    },
  });
  const [eventFilters, setEventFilters] = useState<SelectedFilters>({});
  const pathname = location.pathname;

  const { getValues } = methods;
  const start_date = getValues('fromDate');
  const end_date = getValues('toDate');

  // Load filters from local storage on mount
  React.useEffect(() => {
    const storedFilters = localStorage.getItem(getStorageKey(pathname)) || '{}';
    if (storedFilters) {
      try {
        setEventFilters(JSON.parse(storedFilters));
      } catch (error) {
        console.error('Error parsing stored events filters', error);
      }
    }
  }, [pathname]);

  const { isLoading: isLoadingEventsFacetSummary, data: eventsFacetSummary } = useQuery({
    queryKey: ['getEventsFacetSummary', { start_date, end_date }],
    queryFn: () =>
      getEventsFacetSummary({
        start_date,
        end_date,
      }),
  });

  const {
    data: events,
    isLoading,
    isFetching,
  } = useEventList(
    {
      event_type: '-alarm',
      desc: 'event_time',
      start_date: getValues('fromDate'),
      end_date: getValues('toDate'),
      limit,
      lifecycle,
    },
    eventFilters
  );
  const { count: eventCount, events: eventList } = events ?? { count: 0, events: [] };
  const { isOpen: isVisible, onClose, onOpen } = useDisclosure();

  React.useEffect(() => {
    if (eventCount > 1000) {
      onOpen();
    }
    if (eventCount !== 0) {
      setEventsCount(eventCount);
    }
  }, [eventCount]);

  // Use the shared transformer
  const data: EventHeader[] = React.useMemo(() => transformEvents(eventList), [eventList]);

  const getAllData = React.useCallback(() => {
    if (limit > 1000) {
      const uniqueArray: EventHeader[] = [...eventsList, ...data];
      setEventsList(uniqueArray);
    } else {
      setEventsList(data);
    }
  }, [data, eventFilters]);

  React.useEffect(() => {
    if (data.length) {
      getAllData();
    }
  }, [data, getAllData, eventFilters]);

  const columns = React.useMemo(() => getEventColumns(), []);
  const hiddenColumnsByDefault = React.useMemo(() => getHiddenColumnsByDefault(), []);

  const renderSubComponent = useRenderSubComponent();

  const handleApplyFilters = (newFilters: SelectedFilters) => {
    setEventFilters(newFilters);
    localStorage.setItem(getStorageKey(pathname), JSON.stringify(newFilters));
  };

  const { id } = useParams();
  return (
    <>
      <FormProvider {...methods}>
        <Box ml="6" p="4">
          <DateRangePicker setLimit={setLimit} />
        </Box>

        {isLoadingEventsFacetSummary && <Spinner />}
        {eventsFacetSummary && (
          <ServerFacet
            facetSummary={eventsFacetSummary}
            onApply={handleApplyFilters}
            facetsDefinition={eventFacetsDefinition}
            pathname={pathname}
          />
        )}

        {isVisible && eventCount > 0 ? (
          <Alert status="success" width="40%" m="0 auto" border="1px solid" borderColor={'green.500'} rounded="lg">
            <AlertIcon />
            <Box>
              <AlertTitle>Success!</AlertTitle>
              <AlertDescription>
                {eventCount} total records have been found. You are only seeing the first{' '}
                {eventCount < 1000 ? eventCount : 1000}. To view more click the Next button below.
              </AlertDescription>
            </Box>
            <CloseButton alignSelf="flex-start" position="relative" right={-1} top={-1} onClick={onClose} />
          </Alert>
        ) : null}

        <DataTable
          isExpandable={true}
          urlCellRef={id}
          enableFilter={false}
          columns={columns}
          data={eventsList}
          pageSizeOptions={[100, 200, 300, 400, 500, 600, 700, 800, 900, 1000]}
          limit={limit}
          setLimit={setLimit}
          isLoading={isLoading && isFetching}
          defaultPageSize={100}
          hasEmptyResult={eventList?.length == 0}
          renderSubComponent={renderSubComponent}
          getRowCanExpand={(row) => !!row.original.subRows}
          hiddenColumnsByDefault={hiddenColumnsByDefault}
          count={eventsCount}
        />
      </FormProvider>
    </>
  );
};
export default Events;
