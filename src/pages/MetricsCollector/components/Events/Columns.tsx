import { ColumnDef } from '@tanstack/react-table';
import { EventHeader } from '../../../../types/metricCollector.type';
import { Box, Flex, Text, Tooltip } from '@chakra-ui/react';
import { BsArrowReturnRight } from 'react-icons/bs';
import Chip from '../../../../components/chip/Chip';

export default function EventColumns(): ColumnDef<EventHeader>[] {
  return [
    {
      id: 'expander',
      cell: (props) => {
        const expander_data = props.row.original.expander as string;

        return (
          <>
            <Flex>
              <BsArrowReturnRight />
              <Text ml="2">{expander_data}</Text>
            </Flex>
          </>
        );
      },
    },
    {
      header: 'Event Time',
      accessorKey: 'event_time',
      id: 'event_time',
      cell: ({ getValue }) => {
        return (
          <>
            {getValue()
              ? new Intl.DateTimeFormat('en-GB', {
                  year: '2-digit',
                  month: 'short',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  timeZone: 'UTC',
                }).format(new Date(getValue() as string))
              : null}
          </>
        );
      },
    },
    {
      header: 'Event Name',
      accessorKey: 'event_name',
      id: 'event_name',
      filterFn: (row, id, value) => value.includes(row.getValue(id)),
    },
    {
      header: 'Severity',
      accessorKey: 'severity',
      id: 'severity',
      filterFn: (row, id, value) => value.includes(row.getValue(id)),

      cell: ({ getValue }) => <Chip statusText={getValue() as string} hasStatusLight />,
    },
    {
      header: 'Event Type',
      accessorKey: 'event_type',
      id: 'event_type',
    },
    {
      header: 'Priority',
      accessorKey: 'priority',
      id: 'priority',
      filterFn: (row, id, value) => value.includes(row.getValue(id)),
      cell: ({ getValue }) => <Chip statusText={getValue() as string} />,
    },
    {
      header: 'Source Name',
      accessorKey: 'source_name',
      id: 'source_name',
      filterFn: (row, id, value) => value.includes(row.getValue(id)),
    },
    {
      header: 'Object Id',
      accessorKey: 'objectId',
      id: 'object_id',
    },
    {
      header: 'Node Id',
      accessorFn: (originalRow: any) => originalRow?.nodeId || 'N/A',
      id: 'node_id',
      filterFn: (row, id, value) => value.includes(row.getValue(id)),
    },
    {
      header: 'Node Type',
      accessorKey: 'nodeType',
      id: 'nodeType',
      filterFn: (row, id, value) => value.includes(row.getValue(id)),
    },
    {
      header: 'Cell Reference',
      accessorFn: (originalRow: any) => originalRow?.cell_refs || 'N/A',
      id: 'cell_refs',
      filterFn: (row, id, value) => value.includes(row.getValue(id)),
      cell: ({ getValue }) => {
        // check if cell_refs is an empty array or undefined and return a placeholder
        const cellRefs = getValue() as string[] | undefined;
        const shouldShowTooltip = cellRefs && cellRefs.length > 29;
        const content = (
          <Box isTruncated maxWidth="26ch">
            {cellRefs}
          </Box>
        );
        return (shouldShowTooltip ? <Tooltip label={cellRefs}>{content}</Tooltip> : cellRefs) || 'N/A';
        // return cellRefs && cellRefs?.length > 0 ? cellRefs : 'N/A';
      },
    },
    {
      header: 'Site Name',
      accessorKey: 'nodeSiteName',
      id: 'nodeSiteName',
      filterFn: (row, id, value) => value.includes(row.getValue(id)),
    },
    {
      header: 'Problem',
      accessorKey: 'specificProblem',
      id: 'specific_problem',
      filterFn: (row, id, value) => value.includes(row.getValue(id)),
      cell: ({ getValue }) => {
        const value = getValue() as string;
        const shouldShowTooltip = value && value.length > 40;
        const content = (
          <Box isTruncated maxWidth="40ch">
            {value}
          </Box>
        );
        return shouldShowTooltip ? <Tooltip label={value}>{content}</Tooltip> : content;
      },
    },
    {
      header: 'Domain',
      accessorKey: 'domain',
      id: 'domain',
      filterFn: (row, id, value) => value.includes(row.getValue(id)),
    },
    {
      header: 'URI',
      accessorKey: 'uri',
      id: 'uri',
      filterFn: (row, id, value) => value.includes(row.getValue(id)),
    },
    {
      header: 'Type',
      accessorKey: 'type',
      id: 'type',
      filterFn: (row, id, value) => value.includes(row.getValue(id)),
    },
    {
      header: 'Cause',
      accessorKey: 'cause',
      id: 'cause',
      filterFn: (row, id, value) => value.includes(row.getValue(id)),
    },
    {
      header: 'Trend Indication',
      accessorKey: 'trendIndication',
      id: 'trend_indication',
      filterFn: (row, id, value) => value.includes(row.getValue(id)),
    },
    {
      header: 'Reporting Entity',
      accessorKey: 'reporting_entity_name',
      id: 'reporting_entity_name',
      filterFn: (row, id, value) => value.includes(row.getValue(id)),
    },
  ];
}
