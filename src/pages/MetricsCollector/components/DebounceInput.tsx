import { Icon, Input, InputGroup, InputLeftElement, InputRightElement, IconButton } from '@chakra-ui/react';
import React from 'react';
import { useParams } from 'react-router';
import { GiCancel } from 'react-icons/gi';
import { IoSearchCircleOutline } from 'react-icons/io5';

// A debounced input react component
export function DebouncedInput({
  value: initialValue,
  onChange,
  debounce = 300,
  testId,
  icon,
  removePathParam,
  ...props
}: {
  value: string | number | undefined;
  onChange: (value: string | number) => void;
  debounce?: number;
  testId?: string;
  icon?: null | string;
  removePathParam?: (id?: string) => void;
} & Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'>) {
  const [value, setValue] = React.useState(initialValue);
  const { id } = useParams();

  React.useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  React.useEffect(() => {
    const timeout = setTimeout(() => {
      if (value && value !== undefined) {
        onChange(value);
      } else {
        onChange('');
      }
    }, debounce);

    return () => clearTimeout(timeout);
  }, [value, initialValue]);

  return (
    <InputGroup>
      <InputLeftElement pointerEvents="none">
        <IconButton
          aria-label="Search"
          icon={<IoSearchCircleOutline size="30px" color="teal" />}
          size="sm"
          variant="ghost"
        />
      </InputLeftElement>

      {value && (
        <InputRightElement>
          <IconButton
            aria-label="Clear input"
            icon={<GiCancel color="red" size="20px" />}
            size="sm"
            variant="ghost"
            onClick={() => {
              setValue('');
              onChange('');
              if (id) removePathParam?.(id);
            }}
            _hover={{ color: 'red.500' }}
            boxSize="6"
          />
        </InputRightElement>
      )}

      <Input
        data-testid={testId && testId}
        type={'search'}
        {...props}
        value={value}
        sx={{
          '&::-webkit-search-cancel-button': {
            display: 'none',
          },
        }}
        onChange={(e) => {
          setValue(e.target.value);
          if (id && e.target.value === '') {
            onChange(e.target.value);
            removePathParam && removePathParam(id);
          }
        }}
        size="md"
        width="100%"
        pl="10"
        pr={value ? '10' : '3'}
      />
    </InputGroup>
  );
}
