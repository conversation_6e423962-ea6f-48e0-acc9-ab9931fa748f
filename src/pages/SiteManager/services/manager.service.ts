/**
 * Manager service layer - Abstraction over API calls with error handling
 */

import { ManagerProps, PostManagerProps } from '../../../types/InventoryManager.type';
import { ManagerApiPayload } from '../types/manager.types';
import { API_ENDPOINTS } from '../constants/manager.constants';
import { extractErrorMessage } from '../utils/manager.utils';
import { apiClient } from '../../../services/httpCommon';

/**
 * Manager Service Class - Encapsulates all manager-related API operations
 */
export class ManagerService {
  /**
   * Fetch all managers
   */
  static async getAllManagers(): Promise<ManagerProps[]> {
    try {
      const { data } = await apiClient.get(`${API_ENDPOINTS.MANAGERS}?page=1&limit=1000`);
      return data;
    } catch (error) {
      throw new Error(`Failed to fetch managers: ${extractErrorMessage(error)}`);
    }
  }

  /**
   * Fetch a specific manager by instance
   */
  static async getManagerById(managerInstance: string): Promise<ManagerProps[]> {
    try {
      const { data } = await apiClient.get(`${API_ENDPOINTS.MANAGERS}?page=1&manager_instance=${managerInstance}`);
      return data;
    } catch (error) {
      throw new Error(`Failed to fetch manager ${managerInstance}: ${extractErrorMessage(error)}`);
    }
  }

  /**
   * Create a new manager
   */
  static async createManager(managerData: ManagerApiPayload): Promise<PostManagerProps> {
    try {
      const { data } = await apiClient.post(API_ENDPOINTS.MANAGERS, managerData);
      return data;
    } catch (error) {
      throw new Error(`Failed to create manager: ${extractErrorMessage(error)}`);
    }
  }

  /**
   * Update an existing manager
   */
  static async updateManager(managerInstance: string, managerData: ManagerApiPayload): Promise<ManagerProps> {
    try {
      const { data } = await apiClient.patch(API_ENDPOINTS.MANAGER_BY_ID(managerInstance), managerData);
      return data;
    } catch (error) {
      throw new Error(`Failed to update manager ${managerInstance}: ${extractErrorMessage(error)}`);
    }
  }

  /**
   * Delete a manager
   */
  static async deleteManager(managerInstance: string): Promise<void> {
    try {
      await apiClient.delete(API_ENDPOINTS.MANAGER_BY_ID(managerInstance));
    } catch (error) {
      throw new Error(`Failed to delete manager ${managerInstance}: ${extractErrorMessage(error)}`);
    }
  }

  /**
   * Validate manager data before API call
   */
  static validateManagerData(data: ManagerApiPayload): string[] {
    const errors: string[] = [];

    if (!data.manager_instance?.trim()) {
      errors.push('Manager instance is required');
    }

    if (!data.component_type?.trim()) {
      errors.push('Component type is required');
    }

    if (!data.manager_url?.trim()) {
      errors.push('Manager URL is required');
    }

    if (data.manager_url && !data.manager_url.match(/^https?:\/\/.+/)) {
      errors.push('Manager URL must be a valid HTTP or HTTPS URL');
    }

    if (!data.credentials?.username?.trim()) {
      errors.push('Username is required');
    }

    if (!data.credentials?.password?.trim()) {
      errors.push('Password is required');
    }

    return errors;
  }

  /**
   * Add manager region - no payload required, just endpoint
   */
  static async addRegionToManager(managerInstance: string, regionCode: string): Promise<any> {
    try {
      const { data } = await apiClient.put(API_ENDPOINTS.LINK_REGION_TO_MANAGER(managerInstance, regionCode));
      return data;
    } catch (error) {
      throw new Error(
        `Failed to add region ${regionCode} to manager ${managerInstance}: ${extractErrorMessage(error)}`
      );
    }
  }

  /**
   * Update manager region
   */
  static async updateManagerRegion(
    managerInstance: string,
    regionCode: string,
    regionData: { region_code: string; region_name: string }
  ): Promise<any> {
    try {
      const { data } = await apiClient.put(
        API_ENDPOINTS.LINK_REGION_TO_MANAGER(managerInstance, regionCode),
        regionData
      );
      return data;
    } catch (error) {
      throw new Error(
        `Failed to update region ${regionCode} for manager ${managerInstance}: ${extractErrorMessage(error)}`
      );
    }
  }

  /**
   * Delete manager region
   */
  static async deleteManagerRegion(managerInstance: string, regionCode: string): Promise<void> {
    try {
      await apiClient.delete(API_ENDPOINTS.LINK_REGION_TO_MANAGER(managerInstance, regionCode));
    } catch (error) {
      throw new Error(
        `Failed to delete region ${regionCode} from manager ${managerInstance}: ${extractErrorMessage(error)}`
      );
    }
  }
}

// Export individual functions for backward compatibility
export const {
  getAllManagers: getManagers,
  getManagerById: getManager,
  createManager,
  updateManager,
  deleteManager,
  validateManagerData,
} = ManagerService;
