/**
 * React Query mutation hooks for manager operations
 */

import { useMutation, useQueryClient, UseMutationResult } from '@tanstack/react-query';
import { useToast } from '@chakra-ui/react';
import { ManagerProps, PostManagerProps } from '../../../types/InventoryManager.type';
import {
  ManagerApiPayload,
  CreateManagerVariables,
  UpdateManagerVariables,
  DeleteManagerVariables,
} from '../types/manager.types';
import { QUERY_KEYS, TOAST_CONFIG } from '../constants/manager.constants';
import { ManagerService } from '../services/manager.service';

/**
 * Hook for creating managers with enhanced error handling
 */
export const useCreateManager = (): UseMutationResult<PostManagerProps, Error, ManagerApiPayload, unknown> => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (data: ManagerApiPayload) => ManagerService.createManager(data),
    onSuccess: (newManager, variables) => {
      // Invalidate and refetch managers list
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.MANAGERS });

      // Show success toast
      toast({
        title: 'Manager Created',
        description: `Manager ${newManager.manager_instance} has been created successfully.`,
        ...TOAST_CONFIG.SUCCESS,
      });
    },
    onError: (error, variables) => {
      console.error('Error creating manager:', error);

      toast({
        title: 'Creation Failed',
        description: error.message || 'Failed to create manager. Please try again.',
        ...TOAST_CONFIG.ERROR,
      });
    },
  });
};

/**
 * Hook for updating managers with enhanced error handling
 */
export const useUpdateManager = (): UseMutationResult<ManagerProps, Error, UpdateManagerVariables, unknown> => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: ({ manager_instance, data }: UpdateManagerVariables) =>
      ManagerService.updateManager(manager_instance, data),
    onSuccess: (updatedManager, variables) => {
      // Invalidate queries
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.MANAGERS });
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.MANAGER_BY_ID(variables.manager_instance),
      });

      // Show success toast
      toast({
        title: 'Manager Updated',
        description: `Manager ${variables.manager_instance} has been updated successfully.`,
        ...TOAST_CONFIG.SUCCESS,
      });
    },
    onError: (error, variables) => {
      console.error('Error updating manager:', error);

      toast({
        title: 'Update Failed',
        description: error.message || 'Failed to update manager. Please try again.',
        ...TOAST_CONFIG.ERROR,
      });
    },
  });
};

/**
 * Hook for deleting managers with enhanced error handling
 */
export const useDeleteManager = (): UseMutationResult<void, Error, string, unknown> => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (manager_instance: string) => ManagerService.deleteManager(manager_instance),
    onSuccess: (_, manager_instance) => {
      // Invalidate and refetch managers list
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.MANAGERS });

      // Remove specific manager from cache
      queryClient.removeQueries({
        queryKey: QUERY_KEYS.MANAGER_BY_ID(manager_instance),
      });

      // Show success toast
      toast({
        title: 'Manager Deleted',
        description: `Manager ${manager_instance} has been deleted successfully.`,
        ...TOAST_CONFIG.SUCCESS,
      });
    },
    onError: (error, manager_instance) => {
      console.error('Error deleting manager:', error);

      toast({
        title: 'Delete Failed',
        description: error.message || 'Failed to delete manager. Please try again.',
        ...TOAST_CONFIG.ERROR,
      });
    },
  });
};

/**
 * Custom hook for manager form operations
 */
export const useManagerFormOperations = () => {
  const createMutation = useCreateManager();
  const updateMutation = useUpdateManager();
  const deleteMutation = useDeleteManager();

  const isLoading = createMutation.isLoading || updateMutation.isLoading || deleteMutation.isLoading;

  const createManager = async (data: ManagerApiPayload) => {
    return createMutation.mutateAsync(data);
  };

  const updateManager = async (manager_instance: string, data: ManagerApiPayload) => {
    return updateMutation.mutateAsync({ manager_instance, data });
  };

  const deleteManager = async (manager_instance: string) => {
    return deleteMutation.mutateAsync(manager_instance);
  };

  return {
    createManager,
    updateManager,
    deleteManager,
    isLoading,
    createMutation,
    updateMutation,
    deleteMutation,
  };
};
