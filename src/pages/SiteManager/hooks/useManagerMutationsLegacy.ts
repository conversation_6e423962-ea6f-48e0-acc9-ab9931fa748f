import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createManager, updateManager, deleteManager } from '../../../services/inventoryManager';
import { useToast } from '@chakra-ui/react';
import { ManagerProps } from '../../../types/InventoryManager.type';

interface ManagerMutationData {
  manager_instance: string;
  manager_type?: string;
  component_type: string;
  manager_url: string;
  version?: string;
  ran_type?: string;
  credentials: {
    username: string;
    password: string;
  };
}

export const useCreateManager = () => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (data: ManagerMutationData) => createManager(data),
    onSuccess: (newManager) => {
      // Invalidate and refetch managers list
      queryClient.invalidateQueries({ queryKey: ['managers'] });

      toast({
        title: 'Manager Created',
        description: `Manager ${newManager.manager.manager_instance} has been created successfully.`,
        status: 'success',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      console.error('Error creating manager:', error);
      toast({
        title: 'Creation Failed',
        description: 'Failed to create manager. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
    },
  });
};

export const useUpdateManager = () => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: ({ manager_instance, data }: { manager_instance: string; data: ManagerMutationData }) =>
      updateManager(manager_instance, data),
    onSuccess: (updatedManager, variables) => {
      // Invalidate and refetch managers list
      queryClient.invalidateQueries({ queryKey: ['managers'] });

      toast({
        title: 'Manager Updated',
        description: `Manager ${variables.manager_instance} has been updated successfully.`,
        status: 'success',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error, variables) => {
      console.error('Error updating manager:', error);
      toast({
        title: 'Update Failed',
        description: 'Failed to update manager. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
    },
  });
};

export const useDeleteManager = () => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (manager_instance: string) => deleteManager(manager_instance),
    onSuccess: (_, manager_instance) => {
      // Invalidate and refetch managers list
      queryClient.invalidateQueries({ queryKey: ['managers'] });

      toast({
        title: 'Manager Deleted',
        description: `Manager ${manager_instance} has been deleted successfully.`,
        status: 'success',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
    },
    onError: (error) => {
      console.error('Error deleting manager:', error);
      toast({
        title: 'Delete Failed',
        description: 'Failed to delete manager. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
    },
  });
};
