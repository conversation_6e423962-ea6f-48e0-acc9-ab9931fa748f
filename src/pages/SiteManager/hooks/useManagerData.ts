/**
 * React Query hooks for manager data fetching
 */

import { useQuery, UseQueryResult } from '@tanstack/react-query';
import { ManagerProps } from '../../../types/InventoryManager.type';
import { QUERY_KEYS } from '../constants/manager.constants';
import { ManagerService } from '../services/manager.service';

/**
 * Hook to fetch all managers
 */
export const useManagersData = (): UseQueryResult<ManagerProps[], Error> => {
  return useQuery({
    queryKey: QUERY_KEYS.MANAGERS,
    queryFn: ManagerService.getAllManagers,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook to fetch a specific manager by instance
 */
export const useManagerById = (managerInstance: string, enabled = true): UseQueryResult<ManagerProps[], Error> => {
  return useQuery({
    queryKey: QUERY_KEYS.MANAGER_BY_ID(managerInstance),
    queryFn: () => ManagerService.getManagerById(managerInstance),
    enabled: enabled && !!managerInstance,
    staleTime: 5 * 60 * 1000,
    cacheTime: 10 * 60 * 1000,
  });
};
