import { Text, Box } from '@chakra-ui/react';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo } from 'react';
import { ManagerProps } from '../../../types/InventoryManager.type';
import { BsArrowReturnRight } from 'react-icons/bs';
import { Flex } from '@chakra-ui/react';
import ManagerConfigMenu from '../components/ManagerConfigMenu';
import StatusComponent from '../../../components/icons/StatusIcon';
import { getStatusColor } from '../../CellOverview/hooks/useStatus';

interface FlattenedManagerProps {
  manager_instance: string;
  manager_type: string;
  component_type: string;
  manager_url: string;
  version: string;
  ran_type: string | null;
  host_id?: string | null;
  host_type?: string | null;
  host_node_id?: string | null;
  region_codes: string;
  region_names: string;
  regions: ManagerProps['regions'];
  status?: string;
}

type ManagerColumnOptions = {
  onEdit?: (manager: FlattenedManagerProps) => void;
  onDelete?: (manager: FlattenedManagerProps) => Promise<void>;
  onConfigure?: (manager: FlattenedManagerProps) => void;
  onUpdateRegion?: (manager: FlattenedManagerProps) => void;
};

const useManagerColumns = (options?: ManagerColumnOptions): ColumnDef<FlattenedManagerProps>[] => {
  const columns = useMemo<ColumnDef<FlattenedManagerProps>[]>(
    () => [
      {
        accessorKey: 'manager_instance',
        header: 'Manager Instance',
        cell: ({ row }) => (
          <Flex>
            <BsArrowReturnRight />
            <Text ml={2}>{row.original.manager_instance}</Text>
          </Flex>
        ),
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },

      {
        accessorKey: 'component_type',
        header: 'Component Type',
        cell: ({ row }) => row.original.component_type,
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        accessorKey: 'region_codes',
        header: 'Region Codes',
        cell: ({ row }) => row.original.region_codes,
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        accessorKey: 'region_names',
        header: 'Region Names',
        cell: ({ row }) => row.original.region_names,
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        accessorKey: 'manager_url',
        header: 'Manager URL',
        cell: ({ row }) => row.original.manager_url,
      },
      {
        accessorKey: 'version',
        header: 'Version',
        cell: ({ row }) => row.original.version,
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        accessorKey: 'ran_type',
        header: 'RAN Type',
        cell: ({ row }) => row.original.ran_type || '-',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        accessorKey: 'host_id',
        header: 'Host ID',
        cell: ({ row }) => row.original.host_id || '-',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        accessorKey: 'host_type',
        header: 'Host Type',
        cell: ({ row }) => row.original.host_type || '-',
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
      {
        accessorKey: 'status',
        header: 'Status',
        cell: (props) => (
          <Box display="flex" justifyContent="space-around">
            {props.row.original.status && props.row.original.host_node_id && (
              <StatusComponent
                dataTestId="cell-main-table-status-icon"
                boxSize="sm"
                color={getStatusColor(props.row.original.status)}
                status={props.row.original.status}
                node_id={props.row.original.host_node_id}
              />
            )}
            <ManagerConfigMenu
              dataTestId="manager-config-menu"
              manager={props.row.original}
              onEdit={options?.onEdit}
              onDelete={options?.onDelete}
              onUpdateRegion={options?.onUpdateRegion}
            />
          </Box>
        ),
        filterFn: (row, id, value) => value.includes(row.getValue(id)),
      },
    ],
    [options]
  );

  return columns;
};

export default useManagerColumns;
export type { FlattenedManagerProps };
