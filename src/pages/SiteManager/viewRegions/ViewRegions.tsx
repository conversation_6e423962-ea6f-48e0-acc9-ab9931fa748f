import { Box, Flex, useColorModeValue } from '@chakra-ui/react';
import * as React from 'react';
import EmptyState from '../../../components/emptyState/EmptyState';
import Loader from '../../../components/loader/Loader';
import { Region, Site } from '../../../types/InventoryManager.type';
import useRegionList from '../hooks/useRegionList';
import useSiteList from '../hooks/useSiteList';
import { Regions } from './Regions';
import SiteCard from '../components/SiteCard';
import { RegionCard } from '../components/RegionCard';
import { useEffect, useMemo } from 'react';

const ViewRegions = () => {
  const { data: regionList = [], isLoading: isRegionsLoading } = useRegionList();
  const { data: siteList = [], isLoading: isSitesLoading } = useSiteList();

  const [selectedRegion, setSelectedRegion] = React.useState<Region | null>(null);
  const [selectedSite, setSelectedSite] = React.useState<Site | null>(null);

  // Filter state
  const [selectedCountry, setSelectedCountry] = React.useState<string | null>(null);
  const [selectedRegionType, setSelectedRegionType] = React.useState<string | null>(null);
  const [selectedLifecycle, setSelectedLifecycle] = React.useState<string | null>(null);

  const bgColor = useColorModeValue('white', 'gray.900');

  // Normalize lifecycle values - handle null/empty as UNSET
  const normalizeLifecycle = (lifecycle: string | null | undefined): string => {
    if (!lifecycle || lifecycle === '' || lifecycle === null) {
      return 'UNSET';
    }
    return lifecycle;
  };

  // Compute filtered regions based on filter state
  const filteredRegions = useMemo(() => {
    return regionList.filter((region) => {
      if (selectedCountry && region.country_name !== selectedCountry) {
        return false;
      }

      if (selectedRegionType && region.region_type !== selectedRegionType) {
        return false;
      }

      if (selectedLifecycle) {
        const normalizedLifecycle = normalizeLifecycle(region.lifecycle);
        if (normalizedLifecycle !== selectedLifecycle) {
          return false;
        }
      }

      return true;
    });
  }, [regionList, selectedCountry, selectedRegionType, selectedLifecycle]);

  useEffect(() => {
    // This ensures that the selected site is updated with the latest site info received from siteList
    if (selectedSite) {
      const updatedSite = siteList.find((site) => site.site_id === selectedSite.site_id);
      if (updatedSite) {
        setSelectedSite(updatedSite);
      } else {
        // The selected site no longer exists in the siteList
        setSelectedSite(null);
      }
    }
  }, [siteList, selectedSite]);

  useEffect(() => {
    if (selectedRegion) {
      const updatedRegion = regionList.find((region) => region.region_code === selectedRegion.region_code);
      if (updatedRegion) {
        setSelectedRegion(updatedRegion);
      } else {
        setSelectedRegion(null);
      }
    }
  }, [regionList, selectedRegion]);

  // Auto-select first region when filteredRegions is loaded and no region is selected
  useEffect(() => {
    if (filteredRegions.length > 0 && !selectedRegion && !selectedSite) {
      setSelectedRegion(filteredRegions[0]);
    }
  }, [filteredRegions, selectedRegion, selectedSite]);

  // Clear selection when no filtered regions are available
  useEffect(() => {
    if (filteredRegions.length === 0 && selectedRegion) {
      setSelectedRegion(null);
      setSelectedSite(null);
    }
  }, [filteredRegions.length, selectedRegion]);

  // Helper function to select the first region after any filter change
  const selectFirstRegionAfterFilter = (
    newCountry: string | null,
    newRegionType: string | null,
    newLifecycle: string | null
  ) => {
    // Filter regions based on the new filter values
    const filtered = regionList.filter((region) => {
      if (newCountry && region.country_name !== newCountry) {
        return false;
      }

      if (newRegionType && region.region_type !== newRegionType) {
        return false;
      }

      if (newLifecycle) {
        const normalizedLifecycle = normalizeLifecycle(region.lifecycle);
        if (normalizedLifecycle !== newLifecycle) {
          return false;
        }
      }

      return true;
    });

    if (filtered.length > 0) {
      const firstRegion = filtered[0];
      setSelectedRegion(firstRegion);
      setSelectedSite(null);
    } else {
      // No regions match the current filters - clear selection
      setSelectedRegion(null);
      setSelectedSite(null);
    }
  };

  // Filter handlers
  const handleCountryChange = (country: string | null) => {
    setSelectedCountry(country);
    selectFirstRegionAfterFilter(country, selectedRegionType, selectedLifecycle);
  };

  const handleRegionTypeChange = (regionType: string | null) => {
    setSelectedRegionType(regionType);
    selectFirstRegionAfterFilter(selectedCountry, regionType, selectedLifecycle);
  };

  const handleLifecycleChange = (lifecycle: string | null) => {
    setSelectedLifecycle(lifecycle);
    selectFirstRegionAfterFilter(selectedCountry, selectedRegionType, lifecycle);
  };

  if (isRegionsLoading || isSitesLoading) return <Loader data-testid="loader" />;

  if (!regionList.length) return <EmptyState>No regions found</EmptyState>;

  const handleSetSelectedRegion = (region: Region | null) => {
    setSelectedRegion(region);
    if (region === null) {
      setSelectedSite(null);
    }
  };

  return (
    <Flex height="80vh">
      <Box bg={bgColor} width="35%" borderRightWidth="1px" overflowY="auto" px="4">
        <Regions
          regionList={regionList}
          filteredRegions={filteredRegions}
          sites={siteList}
          setSelectedRegion={handleSetSelectedRegion}
          setSelectedSite={setSelectedSite}
          selectedRegion={selectedRegion}
          selectedSite={selectedSite}
          selectedCountry={selectedCountry}
          selectedRegionType={selectedRegionType}
          selectedLifecycle={selectedLifecycle}
          onCountryChange={handleCountryChange}
          onRegionTypeChange={handleRegionTypeChange}
          onLifecycleChange={handleLifecycleChange}
        />
      </Box>

      {/* Main Detail View */}
      <Box bg={bgColor} flex="1" overflowY="auto" p="4">
        {selectedRegion && !selectedSite ? (
          <RegionCard region={selectedRegion} />
        ) : selectedSite ? (
          <SiteCard site={selectedSite} />
        ) : filteredRegions.length === 0 ? (
          <EmptyState>No regions match the selected filters</EmptyState>
        ) : (
          <EmptyState>Select a region to view details</EmptyState>
        )}
      </Box>
    </Flex>
  );
};

export default ViewRegions;
