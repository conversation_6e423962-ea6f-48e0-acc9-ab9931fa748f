import React, { useMemo } from 'react';
import { Box, Button, FormControl, FormLabel, HStack, Select, useColorModeValue } from '@chakra-ui/react';
import { Region } from '../../../types/InventoryManager.type';

interface RegionFiltersProps {
  regionList: Region[];
  selectedCountry: string | null;
  selectedRegionType: string | null;
  selectedLifecycle: string | null;
  onCountryChange: (country: string | null) => void;
  onRegionTypeChange: (regionType: string | null) => void;
  onLifecycleChange: (lifecycle: string | null) => void;
}

const REGION_TYPES = {
  venue: 'Venue',
  datacenter: 'Data Center',
  cloud: 'Cloud',
};

const normalizeLifecycle = (lifecycle: string | null | undefined): string => {
  if (!lifecycle || lifecycle === '' || lifecycle === null) {
    return 'UNSET';
  }
  return lifecycle;
};

export const RegionFilters: React.FC<RegionFiltersProps> = ({
  regionList,
  selectedCountry,
  selectedRegionType,
  selectedLifecycle,
  onCountryChange,
  onRegionTypeChange,
  onLifecycleChange,
}) => {
  // Color mode values
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  // Extract unique values for each filter
  const { uniqueCountries, uniqueRegionTypes, uniqueLifecycles } = useMemo(() => {
    const countries = regionList.map((region) => region.country_name).filter(Boolean);
    const regionTypes = regionList.map((region) => region.region_type).filter(Boolean);
    const lifecycles = regionList.map((region) => normalizeLifecycle(region.lifecycle));

    return {
      uniqueCountries: Array.from(new Set(countries)).sort((a, b) => a.localeCompare(b)),
      uniqueRegionTypes: Array.from(new Set(regionTypes)).sort((a, b) => a.localeCompare(b)),
      uniqueLifecycles: Array.from(new Set(lifecycles)).sort((a, b) => a.localeCompare(b)),
    };
  }, [regionList]);

  const getRegionTypeLabel = (type: string) => {
    return REGION_TYPES[type as keyof typeof REGION_TYPES] || type;
  };

  const getLifecycleLabel = (lifecycle: string) => {
    return lifecycle;
  };

  const isClearAllDisabled = selectedCountry === null && selectedRegionType === null && selectedLifecycle === null;

  return (
    <Box
      position="sticky"
      top="48px"
      zIndex={1}
      bg={bgColor}
      px="4"
      py="3"
      boxShadow="md"
      borderBottom="1px"
      borderColor={borderColor}
    >
      <HStack spacing="2" align="flex-end">
        {/* Country Filter */}
        <FormControl minW="150px">
          <FormLabel fontSize="sm" fontWeight="medium" color="gray.600" mb="1">
            Country
          </FormLabel>
          <Select
            size="sm"
            value={selectedCountry || ''}
            onChange={(e) => onCountryChange(e.target.value || null)}
            borderColor="blue.500"
            focusBorderColor="blue.500"
            _hover={{ borderColor: 'blue.400' }}
          >
            <option value="">All</option>
            {uniqueCountries.map((country) => (
              <option key={country} value={country}>
                {country}
              </option>
            ))}
          </Select>
        </FormControl>

        {/* Region Type Filter */}
        <FormControl minW="100px">
          <FormLabel fontSize="sm" fontWeight="medium" color="gray.600" mb="1">
            Region Type
          </FormLabel>
          <Select
            size="sm"
            value={selectedRegionType || ''}
            onChange={(e) => onRegionTypeChange(e.target.value || null)}
            borderColor="blue.500"
            focusBorderColor="blue.500"
            _hover={{ borderColor: 'blue.400' }}
          >
            <option value="">All</option>
            {uniqueRegionTypes.map((regionType) => (
              <option key={regionType} value={regionType}>
                {getRegionTypeLabel(regionType)}
              </option>
            ))}
          </Select>
        </FormControl>

        {/* Lifecycle Filter */}
        <FormControl minW="150px">
          <FormLabel fontSize="sm" fontWeight="medium" color="gray.600" mb="1">
            Lifecycle
          </FormLabel>
          <Select
            size="sm"
            value={selectedLifecycle || ''}
            onChange={(e) => onLifecycleChange(e.target.value || null)}
            borderColor="blue.500"
            focusBorderColor="blue.500"
            _hover={{ borderColor: 'blue.400' }}
          >
            <option value="">All</option>
            {uniqueLifecycles.map((lifecycle) => (
              <option key={lifecycle} value={lifecycle}>
                {getLifecycleLabel(lifecycle)}
              </option>
            ))}
          </Select>
        </FormControl>

        {/* Clear All Filters */}

        <Button
          size="sm"
          variant="outline"
          colorScheme="red"
          onClick={() => {
            onCountryChange(null);
            onRegionTypeChange(null);
            onLifecycleChange(null);
          }}
          isDisabled={isClearAllDisabled}
          flexShrink={0}
        >
          Clear All
        </Button>
      </HStack>
    </Box>
  );
};

export default RegionFilters;
