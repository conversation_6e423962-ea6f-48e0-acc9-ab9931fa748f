import {
  Accordion,
  AccordionButton,
  AccordionItem,
  AccordionPanel,
  Badge,
  Box,
  Button,
  Flex,
  FlexProps,
  HStack,
  Icon,
  Text,
  VStack,
} from '@chakra-ui/react';
import { Region, Site } from '../../../types/InventoryManager.type';
import { ColumnHeader } from './Column';
import { ChevronRightIcon, ChevronDownIcon } from '@chakra-ui/icons';
import { useState, useEffect } from 'react';
import { HiLocationMarker, HiServer, HiCloud } from 'react-icons/hi';
import { FiMapPin } from 'react-icons/fi';
import RegionFilters from './RegionFilters';

interface Props extends FlexProps {
  regionList: Region[];
  filteredRegions: Region[];
  sites: Site[];
  setSelectedRegion: (region: Region | null) => void;
  setSelectedSite: (site: Site | null) => void;
  selectedRegion: Region | null;
  selectedSite: Site | null;
  selectedCountry: string | null;
  selectedRegionType: string | null;
  selectedLifecycle: string | null;
  onCountryChange: (country: string | null) => void;
  onRegionTypeChange: (regionType: string | null) => void;
  onLifecycleChange: (lifecycle: string | null) => void;
}

export const Regions = (props: Props) => {
  const {
    regionList,
    filteredRegions,
    sites,
    setSelectedRegion,
    setSelectedSite,
    selectedRegion,
    selectedSite,
    selectedCountry,
    selectedRegionType,
    selectedLifecycle,
    onCountryChange,
    onRegionTypeChange,
    onLifecycleChange,
    ...rest
  } = props;

  // State to manage expanded regions by their indices
  const [expandedIndices, setExpandedIndices] = useState<number[]>([]);

  // Normalize lifecycle values - handle null/empty as UNSET
  const normalizeLifecycle = (lifecycle: string | null | undefined): string => {
    if (!lifecycle || lifecycle === '' || lifecycle === null) {
      return 'UNSET';
    }
    return lifecycle;
  };

  const handleCollapseAll = () => {
    setExpandedIndices([]);
    setSelectedSite(null);
  };

  // Get region type icon
  const getRegionTypeIcon = (regionType: string) => {
    switch (regionType?.toLowerCase()) {
      case 'venue':
        return HiLocationMarker;
      case 'datacenter':
        return HiServer;
      case 'cloud':
        return HiCloud;
      default:
        return FiMapPin;
    }
  };

  // Get lifecycle color scheme
  const getLifecycleColorScheme = (lifecycle: string) => {
    const normalized = normalizeLifecycle(lifecycle);
    switch (normalized?.toLowerCase()) {
      case 'production':
      case 'prod':
        return 'green';
      case 'development':
      case 'dev':
        return 'blue';
      case 'staging':
      case 'stage':
        return 'orange';
      case 'testing':
      case 'test':
        return 'purple';
      case 'unset':
        return 'gray';
      default:
        return 'cyan';
    }
  };

  // When a site is selected, ensure its parent region is expanded and highlighted
  useEffect(() => {
    if (selectedSite) {
      const parentRegion = regionList.find((region) => region.region_id === selectedSite.region_id);
      if (parentRegion) {
        const regionIndex = regionList.findIndex((region) => region.region_id === parentRegion.region_id);
        if (regionIndex !== -1 && !expandedIndices.includes(regionIndex)) {
          setExpandedIndices((prev) => [...prev, regionIndex]);
        }
        setSelectedRegion(parentRegion);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedSite]);

  const handleRegionClick = (region: Region) => {
    if (selectedRegion?.region_id === region.region_id && selectedSite?.region_id !== region.region_id) {
      setSelectedSite(null);
    } else {
      // Select the new region and deselect any selected site
      setSelectedRegion(region);
      setSelectedSite(null);

      // Ensure the new region is expanded
      const regionIndex = regionList.findIndex((r) => r.region_id === region.region_id);
      if (regionIndex !== -1 && !expandedIndices.includes(regionIndex)) {
        setExpandedIndices((prev) => [...prev, regionIndex]);
      }
    }
  };

  const handleSiteClick = (site: Site) => {
    if (selectedSite?.site_id === site.site_id) {
      // If the same site is clicked again, deselect it
      setSelectedSite(null);
    } else {
      setSelectedSite(site);
      // Highlight the parent region (handled by useEffect)
    }
  };

  return (
    <Flex as="nav" direction="column" bg="linear-gradient(180deg, gray.50 0%, white 100%)" minH="100vh" {...rest}>
      <ColumnHeader>
        <HStack justifyContent="space-between" width="100%">
          <Text fontWeight="bold" fontSize="lg">
            Regions
          </Text>
          <Button
            size="sm"
            onClick={handleCollapseAll}
            colorScheme="teal"
            variant="outline"
            fontWeight="semibold"
            borderRadius="lg"
            _hover={{
              transform: 'translateY(-1px)',
            }}
            transition="all 0.2s ease"
            isDisabled={expandedIndices.length === 0}
          >
            Collapse All
          </Button>
        </HStack>
      </ColumnHeader>

      <RegionFilters
        regionList={regionList}
        selectedCountry={selectedCountry}
        selectedRegionType={selectedRegionType}
        selectedLifecycle={selectedLifecycle}
        onCountryChange={onCountryChange}
        onRegionTypeChange={onRegionTypeChange}
        onLifecycleChange={onLifecycleChange}
      />

      {/* Sticky Table Headers */}
      {filteredRegions.length > 0 && (
        <Box
          position="sticky"
          top="130px"
          boxShadow="0 2px 4px rgba(0, 0, 0, 0.1)"
          mx="2"
          borderRadius="lg"
          py="5"
          px="2"
          display="grid"
          gridTemplateColumns="20% 40% 30% "
          gap="6"
          alignItems="center"
          bg="gray.100"
          m="1"
        >
          {/* Region Code Header - 10% */}
          <Text fontWeight="bold" fontSize="sm" color="teal.700" textAlign="left">
            Region Code
          </Text>

          {/* Region Name Header - 50% */}
          <Text fontWeight="bold" fontSize="sm" color="teal.700" textAlign="left">
            Region Name
          </Text>

          {/* Lifecycle Header - 40% */}
          <Text fontWeight="bold" fontSize="sm" color="teal.700" textAlign="left">
            Lifecycle
          </Text>
        </Box>
      )}

      <Accordion
        allowMultiple
        index={expandedIndices}
        onChange={(indices) => setExpandedIndices(Array.isArray(indices) ? indices : [indices])}
        data-testid="all-regions"
        p="1"
        mt="1"
      >
        {filteredRegions.map((region, index) => {
          const regionSites = sites
            .filter((site) => site.region_id === region.region_id)
            .sort((a, b) => a.name.localeCompare(b.name));

          const isRegionHighlighted =
            selectedRegion?.region_id === region.region_id ||
            (selectedSite && selectedSite.region_id === region.region_id);

          const isExpanded = expandedIndices.includes(index);
          const lifecycleColorScheme = getLifecycleColorScheme(region.lifecycle || '');

          return (
            <AccordionItem key={region.region_id} border="none" mb="2">
              <Box
                borderRadius="xl"
                boxShadow={isRegionHighlighted ? '0 8px 25px rgba(0, 0, 0, 0.15)' : '0 2px 10px rgba(0, 0, 0, 0.08)'}
                border={isRegionHighlighted ? '1.5px solid' : '1px solid'}
                borderColor={isRegionHighlighted ? 'teal.500' : 'transparent'}
                overflow="hidden"
                transition="all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
                _hover={{
                  transform: 'translateY(-5px)',
                  border: '1.5px solid',
                  borderColor: 'teal.500',
                  cursor: 'pointer',
                }}
              >
                <AccordionButton
                  onClick={() => handleRegionClick(region)}
                  bg={isRegionHighlighted ? 'teal.50' : 'linear-gradient(135deg, teal.50 0%, cyan.50 100%)'}
                  _hover={{
                    bg: isRegionHighlighted
                      ? 'linear-gradient(135deg, teal.100 0%, cyan.100 100%)'
                      : 'linear-gradient(135deg, gray.50 0%, blue.50 100%)',
                  }}
                  p="2"
                  borderRadius="xl"
                  transition="all 0.3s ease"
                  data-testid={`region-${region.region_id}`}
                  display="grid"
                  gridTemplateColumns="20% 45% 20% 15%"
                  gap="2"
                  alignItems="center"
                  minH="6"
                  w="100%"
                >
                  {/* Region Code Column - 10% */}
                  <Box textAlign="left" overflow="hidden">
                    <Text
                      fontWeight="bold"
                      fontSize="lg"
                      color={isRegionHighlighted ? 'teal.700' : 'gray.800'}
                      lineHeight="1.2"
                      isTruncated
                      title={region.region_code}
                    >
                      {region.region_code}
                    </Text>
                  </Box>

                  {/* Region Name Column - 50% */}
                  <Box textAlign="left" overflow="hidden">
                    <Text
                      fontSize="md"
                      color={isRegionHighlighted ? 'teal.600' : 'gray.600'}
                      fontWeight="medium"
                      isTruncated
                      title={region.region_name}
                    >
                      {region.region_name}
                    </Text>
                  </Box>

                  {/* Lifecycle Badge Column - 40% */}
                  <Box display="flex" justifyContent="left" alignItems="center">
                    <Badge
                      colorScheme={lifecycleColorScheme}
                      variant="solid"
                      fontSize="xs"
                      px="3"
                      py="1"
                      borderRadius="full"
                      fontWeight="semibold"
                      textTransform="uppercase"
                      letterSpacing="wide"
                      whiteSpace="nowrap"
                    >
                      {normalizeLifecycle(region.lifecycle)}
                    </Badge>
                  </Box>

                  {/* Expand/Collapse Indicator Column - 10% */}
                  <Box display="flex" justifyContent="center" alignItems="center">
                    <Box
                      bg={isRegionHighlighted ? 'teal.500' : 'teal.200'}
                      borderRadius="full"
                      p="2"
                      transition="all 0.3s ease"
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                    >
                      <Icon
                        as={ChevronDownIcon}
                        boxSize="5"
                        color="white"
                        transform={isExpanded ? 'rotate(180deg)' : 'rotate(0deg)'}
                        transition="transform 0.3s ease"
                      />
                    </Box>
                  </Box>
                </AccordionButton>

                <AccordionPanel px="5" pb="5" pt="3">
                  <VStack spacing="2" align="stretch">
                    {regionSites.length > 0 ? (
                      regionSites.map((site) => {
                        const isSiteSelected = selectedSite?.site_id === site.site_id;

                        return (
                          <Box
                            key={site.site_id}
                            onClick={() => handleSiteClick(site)}
                            cursor="pointer"
                            bg={isSiteSelected ? 'linear-gradient(135deg, teal.50 0%, blue.50 100%)' : 'white'}
                            borderRadius="lg"
                            p="2"
                            border="1px solid"
                            borderColor={isSiteSelected ? 'teal.500' : 'white'}
                            transition="all 0.2s ease"
                            _hover={{
                              bg: isSiteSelected
                                ? 'linear-gradient(135deg, teal.100 0%, blue.100 100%)'
                                : 'linear-gradient(135deg, gray.100 0%, blue.50 100%)',
                              borderColor: 'teal.500',
                              transform: 'translateX(-3px)',
                              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                            }}
                            data-testid={`site-${site.site_id}`}
                            boxShadow="md"
                          >
                            <HStack spacing="3" align="center">
                              {/* Site Icon */}
                              <Box
                                bg={isSiteSelected ? 'teal.500' : 'teal.200'}
                                color="white"
                                borderRadius="md"
                                p="2"
                                transition="all 0.2s ease"
                              >
                                <ChevronRightIcon boxSize="4" />
                              </Box>

                              {/* Site Name */}
                              <Box flex="1">
                                <Text
                                  fontWeight="semibold"
                                  fontSize="md"
                                  color={isSiteSelected ? 'teal.800' : 'gray.800'}
                                  lineHeight="1.3"
                                >
                                  {site.name}
                                </Text>
                              </Box>

                              {/* Site Status Indicator */}
                              <Box
                                w="3"
                                h="3"
                                borderRadius="full"
                                bg={isSiteSelected ? 'teal.400' : 'gray.300'}
                                transition="all 0.2s ease"
                              />
                            </HStack>
                          </Box>
                        );
                      })
                    ) : (
                      <Box
                        bg="gray.50"
                        borderRadius="lg"
                        p="4"
                        border="1px dashed"
                        borderColor="gray.300"
                        textAlign="center"
                      >
                        <Text fontSize="sm" color="gray.500" fontStyle="italic">
                          No sites in this region
                        </Text>
                      </Box>
                    )}
                  </VStack>
                </AccordionPanel>
              </Box>
            </AccordionItem>
          );
        })}
      </Accordion>
    </Flex>
  );
};

export default Regions;
