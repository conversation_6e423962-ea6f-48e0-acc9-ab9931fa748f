/**
 * Site Manager module exports - Centralized export hub
 */

// Components
export { default as ManagersList } from './components/ManagersList';
export { ManagerFormModal } from './components/ManagerForm/ManagerFormModal';
export { UpdateRegionModal } from './components/UpdateRegionModal';
export * from './components/ManagerForm';

// Hooks
export * from './hooks/useManagerData';
export * from './hooks/useManagerMutations';
export * from './hooks/useManagerRegionMutations';
export { default as useManagerColumns } from './hooks/useManagerColumns';

// Services
export { ManagerService } from './services/manager.service';

// Types
export * from './types/manager.types';

// Constants
export * from './constants/manager.constants';

// Schemas
export * from './schemas/manager.schemas';

// Utils - Resolve naming conflicts with explicit exports
export {
  transformFormDataToApiPayload,
  transformApiResponseToFormData,
  generateDefaultFormValues,
  hasFormValuesChanged,
  getLoadingMessage,
  getButtonText,
  getModalTitle,
  sanitizeFormDataForLogging,
  isValidUrl,
  generateTestId,
  formatManagerInstance,
  debounce,
  deepClone,
  isEmpty,
  extractErrorMessage as extractManagerErrorMessage,
} from './utils/manager.utils';

export {
  ERROR_CODES,
  extractErrorMessage as extractEnhancedErrorMessage,
  getStatusCodeMessage,
  createManagerError,
  extractValidationErrors,
  getErrorCode,
  isRetryableError,
  getUserFriendlyMessage,
  logManagerError,
  retryWithBackoff,
} from './utils/error.utils';

// Legacy exports for backward compatibility
export { default as CreateManagerForm } from './components/CreateManagerForm';
export * from './hooks/useManagerMutationsLegacy';
export { useCreateManager, useDeleteManager, useUpdateManager } from './hooks/useManagerMutations';
export { useManagersData as useManagersDataLegacy } from './hooks/useManagersData';
