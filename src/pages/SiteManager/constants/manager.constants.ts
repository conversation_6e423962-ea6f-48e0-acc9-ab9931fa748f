/**
 * Manager-related constants and configuration
 */

import { ManagerToastConfig } from '../types/manager.types';

// Component type options (alphabetically ordered)
export const COMPONENT_TYPES: Record<string, string> = {
  ACP: 'ACP',
  AIRSPAN: 'AIRSPAN',
  ANTHOS: 'ANTHOS',
  BLUWIRELESS: 'BLUWIRELESS',
  CUCP: 'CUCP',
  CUUP: 'CUUP',
  DRUID: 'DRUID',
  DU: 'DU',
  E500: 'E500',
  ENODEB: 'ENODEB',
  FIBROLAN: 'FIBROLAN',
  FORTIMANAGER: 'FORTIMANAGER',
  FORTINET: 'FORTINET',
  JUNIPER: 'JUNIPER',
  MOSOLABS: 'MOSOLABS',
  PARTICLE: 'PARTICLE',
  PKI: 'PKI',
  RAD_K8: 'RAD_K8',
  SERVER: 'SERVER',
  SIXWIND: 'SIXWIND',
  UPS: 'UPS',
  VRANC: 'VRANC',
} as const;

// RAN type options
export const RAN_TYPES: Record<string, string> = {
  '4G': '4G',
  '5G': '5G',
} as const;

// Default form values
export const DEFAULT_MANAGER_VALUES = {
  manager_instance: 'acp_marlow',
  manager_type: 'ACP',
  component_type: 'AIRSPAN',
  manager_url: 'https://**************',
  version: '',
  ran_type: '4G',
  credentials_username: '',
  credentials_password: '',
} as const;

// Form validation constants
export const VALIDATION_RULES = {
  USERNAME_PATTERN: /^[a-zA-Z0-9._-]+$/,
  URL_PATTERN: /^https?:\/\/.+/,
  MIN_PASSWORD_LENGTH: 8,
  MIN_REQUIRED_LENGTH: 1,
} as const;

// Toast notification configurations
export const TOAST_CONFIG: Record<string, Omit<ManagerToastConfig, 'title' | 'description'>> = {
  SUCCESS: {
    status: 'success',
    duration: 3000,
    isClosable: true,
    position: 'top',
  },
  ERROR: {
    status: 'error',
    duration: 3000,
    isClosable: true,
    position: 'top',
  },
  WARNING: {
    status: 'warning',
    duration: 3000,
    isClosable: true,
    position: 'top',
  },
  INFO: {
    status: 'info',
    duration: 3000,
    isClosable: true,
    position: 'top',
  },
} as const;

// Query keys for React Query
export const QUERY_KEYS = {
  MANAGERS: ['managers'] as const,
  MANAGER_BY_ID: (id: string) => ['managers', id] as const,
} as const;

// Field labels and tooltips
export const FIELD_CONFIG = {
  MANAGER_INSTANCE: {
    label: 'Manager Instance',
    tooltip: 'Unique identifier for the manager instance',
    placeholder: 'e.g., acp_marlow',
  },
  MANAGER_TYPE: {
    label: 'Manager Type',
    tooltip: 'Select the type of manager',
    placeholder: 'Select manager type',
  },
  COMPONENT_TYPE: {
    label: 'Component Type',
    tooltip: 'Select the component type being managed',
    placeholder: 'Select component type',
  },
  MANAGER_URL: {
    label: 'Manager URL',
    tooltip: 'Enter the full URL to access the manager',
    placeholder: 'https://**************',
  },
  VERSION: {
    label: 'Version',
    tooltip: 'Manager software version (optional)',
    placeholder: 'e.g., v2.1.0',
  },
  RAN_TYPE: {
    label: 'RAN Type',
    tooltip: 'Radio Access Network type (optional)',
    placeholder: 'Select RAN type',
  },
  USERNAME: {
    label: 'Username',
    tooltip: 'Username for manager authentication',
    placeholder: 'Enter username',
  },
  PASSWORD: {
    label: 'Password',
    tooltip: 'Password for manager authentication',
    placeholder: 'Enter password',
  },
} as const;

// Loading messages
export const LOADING_MESSAGES = {
  CREATING: 'Creating Manager...',
  UPDATING: 'Updating Manager...',
  DELETING: 'Deleting Manager...',
  SUBMITTING: 'Submitting...',
} as const;

// Form button texts
export const BUTTON_TEXTS = {
  CREATE: 'Create Manager',
  UPDATE: 'Update Manager',
  CANCEL: 'Cancel',
  DELETE: 'Delete',
  CREATING: 'Creating...',
  UPDATING: 'Updating...',
  DELETING: 'Deleting...',
} as const;

// API endpoints (relative paths)
export const API_ENDPOINTS = {
  MANAGERS: 'inv/manifest/managers/',
  MANAGER_BY_ID: (id: string) => `inv/manifest/managers/${id}`,
  LINK_REGION_TO_MANAGER: (managerId: string, regionCode: string) =>
    `inv/manifest/managers/${managerId}/region/${regionCode}`,
} as const;
