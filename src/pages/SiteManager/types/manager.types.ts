/**
 * Manager-related TypeScript types and interfaces
 */

import { FlattenedManagerProps } from '../hooks/useManagerColumns';

// Base manager form data structure
export interface ManagerFormData {
  manager_instance: string;
  manager_type?: string;
  component_type: string;
  manager_url: string;
  version?: string;
  ran_type?: string;
  credentials_username: string;
  credentials_password: string;
}

// API payload structure for creating/updating managers
export interface ManagerApiPayload {
  manager_instance: string;
  component_type: string;
  manager_url: string;
  ran_type?: string;
  credentials: {
    username: string;
    password: string;
  };
}

// Form modal props
export interface ManagerFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  isEdit?: boolean;
  defaultValues?: Partial<FlattenedManagerProps>;
  onSuccess?: (result?: any) => void;
}

// Form field props for reusable components
export interface FormFieldProps {
  label: string;
  tooltip: string;
  isRequired?: boolean;
  isDisabled?: boolean;
  placeholder?: string;
  testId?: string;
}

// Manager operation types
export type ManagerOperation = 'create' | 'update' | 'delete' | 'view';

// Manager column options for data table
export interface ManagerColumnOptions {
  onEdit?: (manager: FlattenedManagerProps) => void;
  onDelete?: (manager: FlattenedManagerProps) => Promise<void>;
  onView?: (manager: FlattenedManagerProps) => void;
}

// Manager mutation variables for React Query
export interface CreateManagerVariables {
  data: ManagerApiPayload;
}

export interface UpdateManagerVariables {
  manager_instance: string;
  data: ManagerApiPayload;
}

export interface DeleteManagerVariables {
  manager_instance: string;
}

// Form state interface
export interface ManagerFormState {
  isLoading: boolean;
  hasChanged: boolean;
  operation: ManagerOperation;
}

// Error handling types
export interface ManagerFormError {
  field?: string;
  message: string;
  code?: string;
}

// Toast notification types
export interface ManagerToastConfig {
  title: string;
  description: string;
  status: 'success' | 'error' | 'warning' | 'info';
  duration: number;
  isClosable: boolean;
  position: 'top' | 'bottom' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}
