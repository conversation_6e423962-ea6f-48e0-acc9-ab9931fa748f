/**
 * Utility functions for manager operations
 */

import { ManagerFormData, ManagerApiPayload } from '../types/manager.types';
import { FlattenedManagerProps } from '../hooks/useManagerColumns';
import { DEFAULT_MANAGER_VALUES } from '../constants/manager.constants';

/**
 * Transform form data to API payload format
 */
export const transformFormDataToApiPayload = (formData: ManagerFormData): ManagerApiPayload => {
  return {
    manager_instance: formData.manager_instance,
    component_type: formData.component_type,
    manager_url: formData.manager_url,
    ran_type: formData.ran_type,
    credentials: {
      username: formData.credentials_username,
      password: formData.credentials_password,
    },
  };
};

/**
 * Transform API response to form data format
 */
export const transformApiResponseToFormData = (
  apiResponse: any,
  currentCredentials?: { username: string; password: string }
): ManagerFormData => {
  return {
    manager_instance: apiResponse.manager_instance,
    manager_type: apiResponse.manager_type || '',
    component_type: apiResponse.component_type,
    manager_url: apiResponse.manager_url,
    version: apiResponse.version || '',
    ran_type: apiResponse.ran_type || '',
    credentials_username: currentCredentials?.username || '',
    credentials_password: '', // Always clear password for security
  };
};

/**
 * Generate default form values based on operation type
 */
export const generateDefaultFormValues = (
  isEdit: boolean,
  defaultValues?: Partial<FlattenedManagerProps>
): ManagerFormData => {
  if (isEdit && defaultValues) {
    return {
      manager_instance: defaultValues.manager_instance || DEFAULT_MANAGER_VALUES.manager_instance,
      manager_type: defaultValues.manager_type || DEFAULT_MANAGER_VALUES.manager_type,
      component_type: defaultValues.component_type || DEFAULT_MANAGER_VALUES.component_type,
      manager_url: defaultValues.manager_url || DEFAULT_MANAGER_VALUES.manager_url,
      version: defaultValues.version || DEFAULT_MANAGER_VALUES.version,
      ran_type: defaultValues.ran_type || DEFAULT_MANAGER_VALUES.ran_type,
      credentials_username: '',
      credentials_password: '',
    };
  }

  // For create mode, return empty values instead of defaults
  return {
    manager_instance: '',
    manager_type: '',
    component_type: '',
    manager_url: '',
    version: '',
    ran_type: '',
    credentials_username: '',
    credentials_password: '',
  };
};

/**
 * Check if form values have changed from defaults
 */
export const hasFormValuesChanged = (currentValues: ManagerFormData, defaultValues: ManagerFormData): boolean => {
  return Object.keys(defaultValues).some((key) => {
    const currentValue = currentValues[key as keyof ManagerFormData];
    const defaultValue = defaultValues[key as keyof ManagerFormData];
    return currentValue !== defaultValue;
  });
};

/**
 * Generate loading message based on operation
 */
export const getLoadingMessage = (isEdit: boolean): string => {
  return isEdit ? 'Updating Manager...' : 'Creating Manager...';
};

/**
 * Generate button text based on operation and loading state
 */
export const getButtonText = (isEdit: boolean, isLoading: boolean): string => {
  if (isLoading) {
    return isEdit ? 'Updating...' : 'Creating...';
  }
  return isEdit ? 'Update Manager' : 'Create Manager';
};

/**
 * Generate modal title based on operation
 */
export const getModalTitle = (isEdit: boolean): string => {
  return isEdit ? 'Update Manager' : 'Create New Manager';
};

/**
 * Sanitize form data for logging (remove sensitive information)
 */
export const sanitizeFormDataForLogging = (formData: ManagerFormData) => {
  return {
    ...formData,
    credentials_username: formData.credentials_username ? '[REDACTED]' : '',
    credentials_password: '[REDACTED]',
  };
};

/**
 * Validate URL format
 */
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Generate test ID for form elements
 */
export const generateTestId = (fieldName: string, operation?: string): string => {
  const base = fieldName.replace(/_/g, '-').toLowerCase();
  return operation ? `${operation}-${base}` : base;
};

/**
 * Format manager instance for display
 */
export const formatManagerInstance = (instance: string): string => {
  return instance.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase());
};

/**
 * Extract error message from API error response
 */
export const extractErrorMessage = (error: any): string => {
  if (typeof error === 'string') return error;
  if (error?.response?.data?.message) return error.response.data.message;
  if (error?.response?.data?.error) return error.response.data.error;
  if (error?.message) return error.message;
  return 'An unexpected error occurred';
};

/**
 * Debounce function for form validation
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Deep clone object
 */
export const deepClone = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj));
};

/**
 * Check if object is empty
 */
export const isEmpty = (obj: object): boolean => {
  return Object.keys(obj).length === 0;
};
