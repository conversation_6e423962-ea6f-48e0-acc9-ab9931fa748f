import { AddIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Heading,
  Icon,
  Modal,
  ModalCloseButton,
  ModalContent,
  ModalOverlay,
  Stack,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
  useColorModeValue,
  useDisclosure,
} from '@chakra-ui/react';
import * as React from 'react';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../data/constants';
import useLogin from '../../hooks/useLogin';
import CreateSiteForm from './components/CreateSiteForm';
import CreateRegionForm from './components/CreateRegionForm';
import ViewRegions from './viewRegions/ViewRegions';
import ManagersList from './components/ManagersList';

export const SiteManager: React.FC = () => {
  const navigate = useNavigate();
  const { tab } = useParams();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [isCreatingRegion, setIsCreatingRegion] = React.useState(false);
  const [tabIndex, setTabIndex] = useState(0);

  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);
  const colorModeValue = useColorModeValue('sm', 'sm-dark');

  useEffect(() => {
    if (tab === 'regions') {
      setTabIndex(0);
    } else if (tab === 'managers') {
      setTabIndex(1);
    } else if (!tab) {
      // Default to regions tab when no tab is specified
      setTabIndex(0);
    }
  }, [tab]);

  const handleTabs = (index: number) => {
    if (index === 0) {
      navigate('/site-manager/regions');
    } else {
      navigate('/site-manager/managers');
    }
  };

  const handleOpenCreateSite = () => {
    setIsCreatingRegion(false);
    onOpen();
  };

  const handleOpenCreateRegion = () => {
    setIsCreatingRegion(true);
    onOpen();
  };

  return (
    <>
      <Stack
        spacing="4"
        direction={{
          base: 'column',
          lg: 'row',
        }}
        justify="space-between"
      >
        <Stack spacing="1">
          <Heading fontWeight="medium">Site Manager</Heading>
          <Text color="muted">All important metrics at a glance</Text>
        </Stack>
        {checkRoleAccess && tabIndex === 0 && (
          <>
            <Button
              onClick={handleOpenCreateRegion}
              variant="primary"
              leftIcon={<Icon as={AddIcon} marginStart="-1" />}
              data-testid="Create Region"
            >
              Create Region
            </Button>
            <Button
              onClick={handleOpenCreateSite}
              variant="primary"
              leftIcon={<Icon as={AddIcon} marginStart="-1" />}
              data-testid="Create Site"
            >
              Create Site
            </Button>
          </>
        )}
      </Stack>

      <Stack
        spacing={{
          base: '5',
          lg: '6',
        }}
      >
        <Box
          bg="bg-surface"
          boxShadow={{
            base: 'none',
            md: colorModeValue,
          }}
        >
          <Stack spacing="5">
            <Tabs
              isManual
              isFitted
              variant="enclosed-colored"
              orientation="horizontal"
              isLazy
              onChange={(index: number) => handleTabs(index)}
              index={tabIndex}
            >
              <TabList>
                <Tab>
                  <Heading fontWeight="medium" size="lg">
                    Regions
                  </Heading>
                </Tab>
                <Tab>
                  <Heading fontWeight="medium" size="lg">
                    Managers
                  </Heading>
                </Tab>
              </TabList>
              <TabPanels>
                <TabPanel id="regions">
                  <ViewRegions />
                </TabPanel>
                <TabPanel id="managers">
                  <ManagersList />
                </TabPanel>
              </TabPanels>
            </Tabs>
          </Stack>
        </Box>
      </Stack>

      <Modal isOpen={isOpen} onClose={onClose} size="2xl" isCentered>
        <ModalOverlay bg="blackAlpha.900" />
        <ModalContent>
          <ModalCloseButton />
          {isCreatingRegion ? <CreateRegionForm onClose={onClose} /> : <CreateSiteForm onClose={onClose} />}
        </ModalContent>
      </Modal>
    </>
  );
};

export default SiteManager;
