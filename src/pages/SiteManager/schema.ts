import { z } from 'zod';
import { LIFE_CYCLE } from '../../data/constants';
export const postSiteSchema = z.object({
  name: z.string().nonempty({ message: 'Site name is required' }),
  address: z.string().nonempty({ message: 'Site address is required' }),
  description: z.string().optional(),
  additional_info: z.string().optional(),
  latitude: z.string().regex(/^\(?[+-]?(90(\.0+)?|[1-8]?\d(\.\d+)?)$/, {
    message: 'Must be a valid range [-90, 90]',
  }),
  longitude: z.string().regex(/^\s?[+-]?(180(\.0+)?|1[0-7]\d(\.\d+)?|\d{1,2}(\.\d+)?)\)?$/, {
    message: 'Must be a valid range [-180, 180]',
  }),
  region: z.string().min(1, {
    message: 'Region is Required',
  }),
});

export type PostSiteSchema = z.infer<typeof postSiteSchema>;

// Define the mapping between country codes and their region code prefixes
const COUNTRY_TO_REGION_PREFIX: Record<string, string> = {
  GBR: 'GB',
  USA: 'US',
};

export const postRegionSchema = z
  .object({
    region_type: z.enum(['venue', 'datacenter', 'cloud'], {
      required_error: 'Region type is required',
      invalid_type_error: 'Region type must be one of: venue, datacenter or cloud',
    }),
    region_code: z
      .string()
      .nonempty({ message: 'Region code is required' })
      .length(6, { message: 'Region code must be exactly 6 characters long' }),
    region_name: z.string().nonempty({ message: 'Region name is required' }),
    country_code: z
      .string()
      .nonempty({ message: 'Country code is required' })
      .length(3, { message: 'Country code must be exactly 3 letters' }),
    description: z.string().optional(),
    discovery: z.boolean({
      required_error: 'Discovery flag is required',
      invalid_type_error: 'Discovery must be true or false',
    }),
    lifecycle: z.nativeEnum(LIFE_CYCLE, {
      required_error: 'Lifecycle is required',
      invalid_type_error: 'Invalid lifecycle value',
    }),
    latitude: z.preprocess(
      (val) => {
        if (val === '' || val === null || val === undefined) return undefined;
        const num = Number(val);
        return isNaN(num) ? undefined : num;
      },
      z
        .number({
          required_error: 'Latitude is required',
          invalid_type_error: 'Latitude must be a number',
        })
        .min(-90, { message: 'Must be in the range [-90, 90]' })
        .max(90, { message: 'Must be in the range [-90, 90]' })
    ),
    longitude: z.preprocess(
      (val) => {
        if (val === '' || val === null || val === undefined) {
          return undefined;
        }
        const num = Number(val);
        return isNaN(num) ? undefined : num;
      },
      z
        .number({
          required_error: 'Longitude is required',
          invalid_type_error: 'Longitude must be a number',
        })
        .min(-180, { message: 'Must be in the range [-180, 180]' })
        .max(180, { message: 'Must be in the range [-180, 180]' })
    ),
    radius: z.preprocess(
      (val) => {
        if (val === '' || val === null || val === undefined) return undefined;
        const num = Number(val);
        return isNaN(num) ? undefined : num;
      },
      z
        .number({
          required_error: 'Radius is required',
          invalid_type_error: 'Radius must be a number',
        })
        .nonnegative({ message: 'Radius must be non-negative' })
    ),
    // CellVizion fields - optional and nullable (can be string or number)
    deployment_id: z.preprocess(
      (val) => (val === null || val === undefined ? undefined : val),
      z.union([z.string(), z.number()]).optional()
    ),
    deployment_name: z.preprocess(
      (val) => (val === null || val === undefined ? undefined : String(val)),
      z.string().optional()
    ),
    customer_id: z.preprocess(
      (val) => (val === null || val === undefined ? undefined : val),
      z.union([z.string(), z.number()]).optional()
    ),
    customer_name: z.preprocess(
      (val) => (val === null || val === undefined ? undefined : String(val)),
      z.string().optional()
    ),
  })
  .superRefine((data, ctx) => {
    const { country_code, region_code } = data;

    if (country_code && region_code) {
      const expectedRegionPrefix = COUNTRY_TO_REGION_PREFIX[country_code.toUpperCase()];
      const actualRegionPrefix = region_code.slice(0, 2).toUpperCase();

      // Validate that the country code is supported
      if (!expectedRegionPrefix) {
        ctx.addIssue({
          path: ['country_code'],
          code: z.ZodIssueCode.custom,
          message: `Unsupported country code: ${country_code}`,
        });
        return; // Exit early if country code is unsupported
      }

      // Check if region_code starts with the expected prefix
      if (actualRegionPrefix !== expectedRegionPrefix) {
        ctx.addIssue({
          path: ['region_code'],
          code: z.ZodIssueCode.custom,
          message: `Must start with ${expectedRegionPrefix} followed by exactly 4-letter code`,
        });
      }

      // Verify that the actual prefix maps back to the country code
      const expectedCountryCode = Object.keys(COUNTRY_TO_REGION_PREFIX).find(
        (key) => COUNTRY_TO_REGION_PREFIX[key] === actualRegionPrefix
      );

      if (expectedCountryCode && expectedCountryCode !== country_code.toUpperCase()) {
        ctx.addIssue({
          path: ['country_code'],
          code: z.ZodIssueCode.custom,
          message: `Must be ${expectedCountryCode}`,
        });
      }
    }
  });

export type CreateRegionSchema = z.infer<typeof postRegionSchema>;
