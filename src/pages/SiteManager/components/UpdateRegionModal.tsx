/**
 * Update Region Modal - Manages region updates and deletions for managers
 */

import React, { useEffect, useMemo, useState } from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  VStack,
  HStack,
  Box,
  Text,
  useColorModeValue,
  Divider,
  Flex,
  List,
  ListItem,
  IconButton,
  Badge,
  Card,
  CardBody,
  Circle,
  Icon,
} from '@chakra-ui/react';
import { AddIcon, CloseIcon } from '@chakra-ui/icons';
import { FiMapPin, FiMinus, FiX } from 'react-icons/fi';
import { FlattenedManagerProps } from '../hooks/useManagerColumns';
import { useAddManagerRegion, useDeleteManagerRegion } from '../hooks/useManagerRegionMutations';
import Loader from '../../../components/loader/Loader';
import { useManagerById } from '../hooks/useManagerData';
import useRegionList from '../hooks/useRegionList';
import { FaTimes } from 'react-icons/fa';

interface UpdateRegionModalProps {
  isOpen: boolean;
  onClose: () => void;
  manager: FlattenedManagerProps | null;
  isStepTwo?: boolean; // Indicates if this is step 2 of the creation process
}

// No schema needed for this list-based approach

export const UpdateRegionModal: React.FC<UpdateRegionModalProps> = ({
  isOpen,
  onClose,
  manager,
  isStepTwo = false,
}) => {
  const [selectedRegion, setSelectedRegion] = useState<string>('');
  const [initialManagerInstance, setInitialManagerInstance] = useState<string | null>(null);
  const [localManager, setLocalManager] = useState<FlattenedManagerProps | null>(manager);
  const [preventAutoClose, setPreventAutoClose] = useState<boolean>(false);
  const addRegionMutation = useAddManagerRegion();
  const deleteRegionMutation = useDeleteManagerRegion();

  // Fetch available regions
  const { data: regionList = [] } = useRegionList();

  // Subscribe to manager data updates - this will automatically receive fresh data
  // when regions are added/deleted thanks to query invalidation in the mutations
  const { data: managerData } = useManagerById(manager?.manager_instance || '', isOpen && !!manager);

  // Update local manager when fresh data arrives (triggered by mutation success)
  useEffect(() => {
    if (managerData && managerData.length > 0 && isOpen) {
      const updatedManager = managerData[0];
      setLocalManager({
        ...localManager!,
        regions: updatedManager.regions,
        region_codes: updatedManager.regions.map((r) => r.region_code).join(', '),
        region_names: updatedManager.regions.map((r) => r.region_name).join(', '),
      });
    }
  }, [managerData, isOpen]);

  // Store initial manager instance to maintain consistency
  useEffect(() => {
    if (isOpen && manager?.manager_instance) {
      setInitialManagerInstance(manager.manager_instance);
      setLocalManager(manager);
    }
  }, [isOpen, manager]);

  // Extract region data from local manager
  const regions = useMemo(() => {
    if (!localManager?.regions || localManager.regions.length === 0) return [];
    return localManager.regions.map((region) => ({
      region_code: region.region_code,
      region_name: region.region_name || '',
    }));
  }, [localManager]);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setSelectedRegion('');
    }
  }, [isOpen]);

  // Clear input field on successful add region - DO NOT CLOSE MODAL
  useEffect(() => {
    if (addRegionMutation.isSuccess) {
      setSelectedRegion('');
      setPreventAutoClose(true); // Explicitly prevent any auto-close behavior
      // Reset mutation state after a short delay to allow for multiple operations
      setTimeout(() => {
        addRegionMutation.reset();
        setPreventAutoClose(false);
      }, 100);
      // Modal stays open for more operations
    }
  }, [addRegionMutation.isSuccess]);

  // Reset delete mutation on success - DO NOT CLOSE MODAL
  useEffect(() => {
    if (deleteRegionMutation.isSuccess) {
      setPreventAutoClose(true); // Explicitly prevent any auto-close behavior
      // Reset mutation state after a short delay to allow for multiple operations
      setTimeout(() => {
        deleteRegionMutation.reset();
        setPreventAutoClose(false);
      }, 100);
      // Modal stays open for more operations
    }
  }, [deleteRegionMutation.isSuccess]);

  // Reset mutation states when modal closes to prevent stale state
  useEffect(() => {
    if (!isOpen) {
      addRegionMutation.reset();
      deleteRegionMutation.reset();
      setInitialManagerInstance(null);
      setLocalManager(null);
    }
  }, [isOpen]);

  // Handle add region
  const handleAddRegion = () => {
    if (!localManager || !selectedRegion) return;

    const regionCode = selectedRegion.split('|')[0];
    addRegionMutation.mutate({
      manager_instance: localManager.manager_instance,
      region_code: regionCode,
    });
  };

  // Handle region unlinking (deletion)
  const handleUnlinkRegion = (regionCode: string) => {
    if (!localManager) return;

    deleteRegionMutation.mutate({
      manager_instance: localManager.manager_instance,
      region_code: regionCode,
    });
  };

  // Handle modal close - Only allow explicit user-initiated close
  const handleClose = () => {
    if (isLoading) return; // Prevent closing during loading

    // Only close if user explicitly clicks close button, not due to successful operations
    setSelectedRegion('');
    setInitialManagerInstance(null);
    setLocalManager(null);
    setPreventAutoClose(false);
    addRegionMutation.reset();
    deleteRegionMutation.reset();
    onClose();
  };

  // Filter out already linked regions
  const availableRegions = useMemo(() => {
    if (!regionList || !localManager) return [];
    const linkedRegionCodes = regions.map((r) => r.region_code);
    return regionList.filter((region) => !linkedRegionCodes.includes(region.region_code));
  }, [regionList, regions, localManager]);

  // If no local manager, don't render
  if (!localManager) {
    return null;
  }

  const isLoading = addRegionMutation.isLoading || deleteRegionMutation.isLoading;

  // Custom close handler that prevents auto-close during operations
  const preventedClose = () => {
    // Only allow close if not in the middle of an operation and not preventing auto-close
    if (!isLoading && !preventAutoClose) {
      handleClose();
    }
  };

  return (
    <>
      <Modal isOpen={isOpen} onClose={preventedClose} size="xl" isCentered closeOnOverlayClick={false}>
        <ModalOverlay bg="blackAlpha.900" />
        <ModalContent borderRadius="lg" boxShadow="2xl">
          <ModalHeader bg="gray.50" borderTopRadius="lg" borderBottom="1px" borderColor="gray.200" py={4}>
            {isStepTwo && (
              <Box mb={3}>
                <Text fontSize="xs" color="gray.500" mb={2} textAlign="center">
                  STEP 2 OF 2
                </Text>
                <HStack spacing={3} justify="center">
                  <HStack spacing={2}>
                    <Circle size="32px" bg="green.500" color="white" boxShadow="sm">
                      <Text fontSize="sm" fontWeight="bold">
                        ✓
                      </Text>
                    </Circle>
                    <Box>
                      <Text fontSize="sm" fontWeight="semibold" color="green.600">
                        Manager Created
                      </Text>
                      <Text fontSize="xs" color="gray.500">
                        Successfully added
                      </Text>
                    </Box>
                  </HStack>
                  <Box w="40px" h="2px" bg="blue.500" />
                  <HStack spacing={2}>
                    <Circle size="32px" bg="blue.500" color="white" boxShadow="sm">
                      <Text fontSize="sm" fontWeight="bold">
                        2
                      </Text>
                    </Circle>
                    <Box>
                      <Text fontSize="sm" fontWeight="semibold" color="blue.600">
                        Link Regions
                      </Text>
                      <Text fontSize="xs" color="gray.500">
                        Assign regions now
                      </Text>
                    </Box>
                  </HStack>
                </HStack>
              </Box>
            )}

            <HStack>
              <FiMapPin size={20} />
              <Text fontSize="lg" fontWeight="semibold">
                Manage Manager Regions
              </Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton isDisabled={isLoading} />

          <ModalBody>
            <VStack spacing={6} align="stretch">
              {/* Manager Info Card */}
              <Card variant="filled" size="sm">
                <CardBody>
                  <HStack spacing={1} mb={2}>
                    <Badge colorScheme="purple" variant="subtle">
                      Manager Info
                    </Badge>
                  </HStack>
                  <VStack align="start" spacing={1}>
                    <HStack>
                      <Text fontSize="sm" color="gray.600" fontWeight="medium">
                        Instance:
                      </Text>
                      <Text fontSize="sm" fontWeight="bold">
                        {localManager.manager_instance}
                      </Text>
                    </HStack>

                    {localManager.manager_type && (
                      <HStack>
                        <Text fontSize="sm" color="gray.600" fontWeight="medium">
                          Type:
                        </Text>
                        <Text fontSize="sm">{localManager.manager_type}</Text>
                      </HStack>
                    )}
                  </VStack>
                </CardBody>
              </Card>

              <Divider />

              {/* Region List */}
              <Box>
                <HStack justify="space-between" mb={4}>
                  <HStack>
                    <Text fontSize="md" fontWeight="semibold">
                      Linked Regions
                    </Text>
                    <Badge colorScheme="blue" fontSize="sm" px={2} py={1} borderRadius="full">
                      {regions.length}
                    </Badge>
                  </HStack>
                </HStack>

                {regions.length === 0 ? (
                  <Card variant="outline" borderStyle="dashed">
                    <CardBody>
                      <VStack py={4}>
                        <FiMapPin size={24} color="gray" />
                        <Text fontSize="sm" color="gray.500">
                          No regions linked to this manager
                        </Text>
                        <Text fontSize="xs" color="gray.400">
                          Add regions using the dropdown below
                        </Text>
                      </VStack>
                    </CardBody>
                  </Card>
                ) : (
                  <List spacing={2}>
                    {regions.map((region) => (
                      <ListItem key={region.region_code}>
                        <Card variant="outline" size="sm">
                          <CardBody>
                            <Flex align="center" justify="space-between">
                              <HStack spacing={3}>
                                <Box p={2} bg="blue.50" borderRadius="md">
                                  <FiMapPin color="blue.500" />
                                </Box>
                                <Box display="flex" flexDirection="row" alignItems="flex-start">
                                  <Text fontWeight="semibold" fontSize="sm">
                                    {region.region_code}
                                  </Text>
                                  {region.region_name && (
                                    <Text fontWeight="semibold" fontSize="sm" color="gray.600" ml={1}>
                                      - {region.region_name}
                                    </Text>
                                  )}
                                </Box>
                              </HStack>
                              <IconButton
                                aria-label="Unlink region"
                                icon={<FiX />}
                                size="md"
                                colorScheme="red"
                                variant="outline"
                                onClick={() => handleUnlinkRegion(region.region_code)}
                                isLoading={
                                  deleteRegionMutation.isLoading &&
                                  deleteRegionMutation.variables?.region_code === region.region_code
                                }
                                isDisabled={deleteRegionMutation.isLoading}
                              />
                            </Flex>
                          </CardBody>
                        </Card>
                      </ListItem>
                    ))}
                  </List>
                )}
              </Box>

              <Divider />

              {/* Add Region Section */}
              <Card variant="filled" bg="blue.50">
                <CardBody>
                  <FormControl>
                    <FormLabel fontSize="sm" fontWeight="semibold" color="blue.800">
                      Add New Region
                    </FormLabel>
                    <Flex gap={3} align="center">
                      <Select
                        placeholder="Select a region to link"
                        value={selectedRegion}
                        onChange={(e) => setSelectedRegion(e.target.value)}
                        size="md"
                        isDisabled={addRegionMutation.isLoading}
                        flex="1"
                        bg="white"
                        borderColor="blue.200"
                        _hover={{ borderColor: 'blue.300' }}
                        _focus={{ borderColor: 'blue.400', boxShadow: '0 0 0 1px #3182CE' }}
                      >
                        {availableRegions.map((region) => (
                          <option key={region.region_id} value={`${region.region_code}|${region.region_name}`}>
                            {region.region_code} - {region.region_name}
                          </option>
                        ))}
                      </Select>
                      <Button
                        colorScheme="blue"
                        onClick={handleAddRegion}
                        isDisabled={!selectedRegion || addRegionMutation.isLoading || deleteRegionMutation.isLoading}
                        isLoading={addRegionMutation.isLoading}
                        loadingText="Linking..."
                        size="md"
                        minW="130px"
                        leftIcon={!addRegionMutation.isLoading ? <AddIcon /> : undefined}
                        _hover={{ transform: 'translateY(-1px)', boxShadow: 'md' }}
                        transition="all 0.2s"
                      >
                        Link Region
                      </Button>
                    </Flex>
                    {availableRegions.length === 0 && (
                      <Text fontSize="xs" color="blue.600" mt={2}>
                        ✓ All available regions are already linked to this manager
                      </Text>
                    )}
                  </FormControl>
                </CardBody>
              </Card>

              {/* Remove blocking overlay - operations are quick enough */}
            </VStack>
          </ModalBody>

          <ModalFooter bg="gray.50" borderBottomRadius="lg" borderTop="1px" borderColor="gray.200">
            {/* Only way to close modal - successful operations keep modal open for more actions */}
            <Button
              bg="red.600"
              color="white"
              variant="outline"
              size="md"
              p="4"
              mr="2"
              shadow="lg"
              _hover={{ bg: 'red', opacity: 0.9, transform: 'translateY(-5px)' }}
              _disabled={{ bg: 'red', opacity: 0.3, cursor: 'not-allowed' }}
              _focus={{ boxShadow: 'lg' }}
              isDisabled={isLoading}
              onClick={handleClose}
            >
              <Box p="2">
                <Icon as={FaTimes} boxSize="1.2em" />
              </Box>
              <Text>Close</Text>
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};
