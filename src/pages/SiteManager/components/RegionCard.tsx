import {
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  Text,
  useColorModeValue,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalCloseButton,
  Icon,
  Link,
  Badge,
  Grid,
  GridItem,
  Divider,
  HStack,
  VStack,
} from '@chakra-ui/react';
import { DeleteIcon, ExternalLinkIcon } from '@chakra-ui/icons';
import { Region } from '../../../types/InventoryManager.type';
import { AUTH_TOKEN_KEY, LIFE_CYCLE, READ_WRITE_ACCESS_ROLES } from '../../../data/constants';
import { EditIcon } from '@chakra-ui/icons';
import useLogin from '../../../hooks/useLogin';
import CreateRegionForm from './CreateRegionForm';
import useDeleteRegion from '../hooks/useDeleteRegion';
import { useState, useEffect } from 'react';

export const RegionCard = ({ region }: { region: Region }) => {
  const {
    region_name,
    region_code,
    region_type,
    radius,
    latitude,
    longitude,
    description,
    country_name,
    lifecycle,
    deployment_id,
    deployment_name,
    customer_id,
    customer_name,
  } = region;

  const [selectedLifecycle, setSelectedLifecycle] = useState<LIFE_CYCLE>(
    lifecycle === null ? LIFE_CYCLE.UNSET : (lifecycle as LIFE_CYCLE) || LIFE_CYCLE.FACTORY
  );

  const [isDiscovery, setIsDiscovery] = useState<boolean>(
    (region as Region).discovery !== undefined ? (region as Region).discovery : true
  );

  useEffect(() => {
    const newLifecycle =
      region.lifecycle === null ? LIFE_CYCLE.UNSET : (region.lifecycle as LIFE_CYCLE) || LIFE_CYCLE.FACTORY;

    setSelectedLifecycle(newLifecycle);
  }, [region, region.lifecycle]);

  useEffect(() => {
    setIsDiscovery((region as Region).discovery !== undefined ? (region as Region).discovery : true);
  }, [region]);

  const { isOpen, onOpen, onClose } = useDisclosure();
  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);

  const deleteRegion = useDeleteRegion();

  // Color mode values - must be called at top level
  const cardBg = useColorModeValue('white', 'gray.800');
  const headerBg = useColorModeValue('gray.50', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const headingColor = useColorModeValue('gray.800', 'white');
  const textColor = useColorModeValue('gray.600', 'gray.300');
  const sectionHeadingColor = useColorModeValue('gray.700', 'gray.200');
  const descriptionColor = useColorModeValue('gray.600', 'gray.400');
  const labelColor = useColorModeValue('gray.500', 'gray.400');

  return (
    <>
      <Card boxShadow="lg" borderRadius="xl" overflow="hidden" bg={cardBg} border="1px" borderColor={borderColor}>
        <CardHeader bg={headerBg} py="4">
          <Flex justifyContent="space-between" alignItems="center">
            <VStack align="start" spacing="1" flex="1">
              <Heading as="h2" size="lg" color={headingColor} fontWeight="bold" data-testid="region-heading">
                {region_code} - Region
              </Heading>
              <Text fontSize="md" color={textColor} fontWeight="medium">
                {region_name}
              </Text>

              <Badge colorScheme="blue" variant="subtle" fontSize="xs">
                {region_type}
              </Badge>
              <Badge colorScheme="green" variant="subtle" fontSize="xs">
                {country_name}
              </Badge>
            </VStack>
            {checkRoleAccess && (
              <HStack spacing="2">
                <Button
                  size="sm"
                  variant="outline"
                  colorScheme="blue"
                  onClick={() => onOpen()}
                  leftIcon={<Icon as={EditIcon} />}
                >
                  Edit
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  colorScheme="red"
                  onClick={() => deleteRegion.mutate(region_code)}
                  leftIcon={<Icon as={DeleteIcon} />}
                >
                  Delete
                </Button>
              </HStack>
            )}
          </Flex>
        </CardHeader>

        <CardBody p="6">
          <VStack spacing="6" align="stretch">
            {/* Description */}
            {description && (
              <Box data-testid="region-description">
                <Heading size="sm" color={sectionHeadingColor} mb="2">
                  Description
                </Heading>
                <Text fontSize="sm" color={descriptionColor}>
                  {description}
                </Text>
              </Box>
            )}

            <Divider />

            {/* Location Information */}
            <Box data-testid="region-location">
              <Heading size="sm" color={sectionHeadingColor} mb="3">
                Location Details
              </Heading>
              <Grid templateColumns="repeat(2, 1fr)" gap="4">
                <GridItem>
                  <Text fontSize="xs" color={labelColor} mb="1">
                    LATITUDE
                  </Text>
                  <Text fontSize="sm" fontWeight="medium">
                    {latitude}
                  </Text>
                </GridItem>
                <GridItem>
                  <Link
                    color="blue.500"
                    fontSize="sm"
                    fontWeight="medium"
                    href={`https://maps.google.com/?q=${latitude},${longitude}`}
                    isExternal
                    _hover={{ color: 'blue.600' }}
                  >
                    View on Map <ExternalLinkIcon mx="2px" />
                  </Link>
                </GridItem>
                <GridItem>
                  <Text fontSize="xs" color={labelColor} mb="1">
                    LONGITUDE
                  </Text>
                  <Text fontSize="sm" fontWeight="medium">
                    {longitude}
                  </Text>
                </GridItem>
                <GridItem>
                  <Text fontSize="xs" color={labelColor} mb="1">
                    RADIUS
                  </Text>
                  <Text fontSize="sm" fontWeight="medium">
                    {radius}m
                  </Text>
                </GridItem>
              </Grid>
            </Box>

            <Divider />

            {/* System Information */}
            <Box data-testid="region-system">
              <Heading size="sm" color={sectionHeadingColor} mb="3">
                System Settings
              </Heading>
              <Grid templateColumns="repeat(2, 1fr)" gap="4">
                <GridItem>
                  <Text fontSize="xs" color={labelColor} mb="2">
                    LIFECYCLE STATUS
                  </Text>
                  <Badge colorScheme={'blue'} fontSize="xs" px="2" py="1">
                    {Object.keys(LIFE_CYCLE).find(
                      (key) => LIFE_CYCLE[key as keyof typeof LIFE_CYCLE] === selectedLifecycle
                    )}
                  </Badge>
                </GridItem>
                <GridItem>
                  <Text fontSize="xs" color={labelColor} mb="2">
                    DISCOVERY
                  </Text>
                  <Badge colorScheme={isDiscovery ? 'green' : 'red'} fontSize="xs" px="2" py="1">
                    {isDiscovery ? 'Enabled' : 'Disabled'}
                  </Badge>
                </GridItem>
              </Grid>
            </Box>

            {/* CellVizion Information */}
            {(deployment_id || deployment_name || customer_id || customer_name) && (
              <>
                <Divider />
                <Box data-testid="region-cellvizion">
                  <Heading size="sm" color={sectionHeadingColor} mb="3">
                    CellVizion Details
                  </Heading>
                  <Grid templateColumns="repeat(2, 1fr)" gap="4">
                    {deployment_id && (
                      <GridItem>
                        <Text fontSize="xs" color={labelColor} mb="1">
                          DEPLOYMENT ID
                        </Text>
                        <Text fontSize="sm" fontWeight="medium">
                          {deployment_id}
                        </Text>
                      </GridItem>
                    )}
                    {deployment_name && (
                      <GridItem>
                        <Text fontSize="xs" color={labelColor} mb="1">
                          DEPLOYMENT NAME
                        </Text>
                        <Text fontSize="sm" fontWeight="medium">
                          {deployment_name}
                        </Text>
                      </GridItem>
                    )}
                    {customer_id && (
                      <GridItem>
                        <Text fontSize="xs" color={labelColor} mb="1">
                          CUSTOMER ID
                        </Text>
                        <Text fontSize="sm" fontWeight="medium">
                          {customer_id}
                        </Text>
                      </GridItem>
                    )}
                    {customer_name && (
                      <GridItem>
                        <Text fontSize="xs" color={labelColor} mb="1">
                          CUSTOMER NAME
                        </Text>
                        <Text fontSize="sm" fontWeight="medium">
                          {customer_name}
                        </Text>
                      </GridItem>
                    )}
                  </Grid>
                </Box>
              </>
            )}
          </VStack>
        </CardBody>
      </Card>

      <Modal isOpen={isOpen} onClose={onClose} size="2xl" isCentered>
        <ModalOverlay />
        <ModalContent>
          <ModalCloseButton />
          <CreateRegionForm
            onClose={onClose}
            defaultValues={{
              ...region,
              region_type:
                (region.region_type as any) in ['venue', 'datacenter', 'cloud']
                  ? (region.region_type as 'venue' | 'datacenter' | 'cloud')
                  : 'venue',
            }}
            isEdit={true}
          />
        </ModalContent>
      </Modal>
    </>
  );
};

export default RegionCard;
