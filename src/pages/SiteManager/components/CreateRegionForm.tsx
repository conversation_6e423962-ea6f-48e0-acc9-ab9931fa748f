import {
  Box,
  Button,
  ButtonGroup,
  Checkbox,
  Divider,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  Icon,
  Input,
  Select,
  Stack,
  Textarea,
  Tooltip,
  useColorModeValue,
  Text,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { CreateRegionSchema, postRegionSchema } from '../schema';
import { z } from 'zod';
import Loader from '../../../components/loader/Loader';
import { useCallback, useEffect, useState, useMemo } from 'react';
import usePostRegion from '../hooks/usePostRegion';
import useUpdateRegionByRegionCode from '../hooks/useUpdateRegionByRegionCode';
import { LIFE_CYCLE, lifecycleToApiValue } from '../../../data/constants';
import { Region } from '../../../types/InventoryManager.type';
import { InfoIcon } from '@chakra-ui/icons';
import { FaSave, FaTimes } from 'react-icons/fa';
import { FiEdit } from 'react-icons/fi';

interface CreateRegionFormProps {
  onClose: () => void;
  isEdit?: boolean;
  defaultValues?: Partial<CreateRegionSchema & { region_id: number }>;
}

const COUNTRY_TO_REGION_PREFIX: Record<string, string> = {
  GBR: 'GB',
  USA: 'US',
};

const REGION_TYPES: Record<string, string> = {
  venue: 'Venue',
  datacenter: 'Data Center',
  cloud: 'Cloud',
};

const updateRegionSchema = z.object({
  region_type: z
    .enum(['venue', 'datacenter', 'cloud'], {
      invalid_type_error: 'Region type must be one of: venue, datacenter or cloud',
    })
    .optional(),
  region_code: z
    .string()
    .nonempty({ message: 'Region code is required' })
    .length(6, { message: 'Region code must be exactly 6 characters long' }),
  region_name: z.string().optional(),
  country_code: z
    .string()
    .nonempty({ message: 'Country code is required' })
    .length(3, { message: 'Country code must be exactly 3 letters' }),
  description: z.string().optional(),
  discovery: z.boolean().optional(),
  lifecycle: z.nativeEnum(LIFE_CYCLE).optional(),
  latitude: z.preprocess(
    (val) => {
      if (val === '' || val === null || val === undefined) return undefined;
      const num = Number(val);
      return isNaN(num) ? undefined : num;
    },
    z
      .number({
        invalid_type_error: 'Latitude must be a number',
      })
      .min(-90, { message: 'Must be in the range [-90, 90]' })
      .max(90, { message: 'Must be in the range [-90, 90]' })
      .optional()
  ),
  longitude: z.preprocess(
    (val) => {
      if (val === '' || val === null || val === undefined) {
        return undefined;
      }
      const num = Number(val);
      return isNaN(num) ? undefined : num;
    },
    z
      .number({
        invalid_type_error: 'Longitude must be a number',
      })
      .min(-180, { message: 'Must be in the range [-180, 180]' })
      .max(180, { message: 'Must be in the range [-180, 180]' })
      .optional()
  ),
  radius: z.preprocess(
    (val) => {
      if (val === '' || val === null || val === undefined) return undefined;
      const num = Number(val);
      return isNaN(num) ? undefined : num;
    },
    z
      .number({
        invalid_type_error: 'Radius must be a number',
      })
      .nonnegative({ message: 'Radius must be non-negative' })
      .optional()
  ),
  // CellVizion fields - optional and nullable (can be string or number)
  deployment_id: z.preprocess(
    (val) => (val === null || val === undefined ? undefined : val),
    z.union([z.string(), z.number()]).optional()
  ),
  deployment_name: z.preprocess(
    (val) => (val === null || val === undefined ? undefined : String(val)),
    z.string().optional()
  ),
  customer_id: z.preprocess(
    (val) => (val === null || val === undefined ? undefined : val),
    z.union([z.string(), z.number()]).optional()
  ),
  customer_name: z.preprocess(
    (val) => (val === null || val === undefined ? undefined : String(val)),
    z.string().optional()
  ),
});

const CreateRegionForm: React.FC<CreateRegionFormProps> = ({ onClose, isEdit = false, defaultValues }) => {
  const [isLoading, setIsLoading] = useState(false);
  const createRegion = usePostRegion();
  const updateRegion = useUpdateRegionByRegionCode();

  const formDefaultValues = useMemo(() => {
    if (isEdit && defaultValues) {
      return {
        ...defaultValues,
        discovery: (defaultValues as any)?.discovery ?? true,
        region_type: (defaultValues as any)?.region_type ?? 'venue',
        lifecycle:
          defaultValues.lifecycle === null
            ? LIFE_CYCLE.UNSET
            : (defaultValues.lifecycle as LIFE_CYCLE) || LIFE_CYCLE.FACTORY,
      };
    }
    return {
      discovery: true,
      lifecycle: LIFE_CYCLE.FACTORY,
      region_type: 'venue',
    };
  }, [isEdit, defaultValues]);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isValid, isDirty },
    reset,
    setValue,
    watch,
  } = useForm<CreateRegionSchema>({
    resolver: zodResolver(isEdit ? updateRegionSchema : postRegionSchema),
    mode: 'onChange',
    defaultValues: formDefaultValues,
  });

  const countryCode = watch('country_code');
  const regionCode = watch('region_code');
  const discovery = watch('discovery');
  const regionType = watch('region_type');
  const allValues = watch();

  const hasChanged = useMemo(() => {
    if (!isEdit || !defaultValues) return false;
    return Object.keys(formDefaultValues).some((key) => {
      const currentValue = allValues[key as keyof CreateRegionSchema];
      const defaultValue = formDefaultValues[key as keyof CreateRegionSchema];

      // Special handling for numeric fields
      if (key === 'latitude' || key === 'longitude' || key === 'radius') {
        const currentNum =
          currentValue === '' || currentValue === null || currentValue === undefined ? undefined : Number(currentValue);
        const defaultNum =
          defaultValue === '' || defaultValue === null || defaultValue === undefined ? undefined : Number(defaultValue);
        return currentNum !== defaultNum;
      }

      // Special handling for string fields (including CellVizion fields)
      if (
        key === 'deployment_id' ||
        key === 'deployment_name' ||
        key === 'customer_id' ||
        key === 'customer_name' ||
        key === 'region_name' ||
        key === 'description'
      ) {
        const currentStr = currentValue === null || currentValue === undefined ? '' : String(currentValue);
        const defaultStr = defaultValue === null || defaultValue === undefined ? '' : String(defaultValue);
        return currentStr !== defaultStr;
      }

      return currentValue !== defaultValue;
    });
  }, [allValues, formDefaultValues, isEdit, defaultValues]);

  const onSubmit = async (data: CreateRegionSchema) => {
    let formData = {
      ...data,
      lifecycle: lifecycleToApiValue(data.lifecycle as LIFE_CYCLE),
    };

    if (!isEdit) {
      const filteredData = { ...formData };

      if (
        !filteredData.deployment_id ||
        (typeof filteredData.deployment_id === 'string' && filteredData.deployment_id.trim() === '')
      ) {
        delete filteredData.deployment_id;
      }
      if (!filteredData.deployment_name || filteredData.deployment_name.trim() === '') {
        delete filteredData.deployment_name;
      }
      if (
        !filteredData.customer_id ||
        (typeof filteredData.customer_id === 'string' && filteredData.customer_id.trim() === '')
      ) {
        delete filteredData.customer_id;
      }
      if (!filteredData.customer_name || filteredData.customer_name.trim() === '') {
        delete filteredData.customer_name;
      }

      formData = filteredData;
    }

    if (isEdit) {
      updateRegion.mutate({
        region_code: defaultValues?.region_code as string,
        region: formData as unknown as Omit<Region, 'region_code'>,
      });
      return;
    }
    createRegion.mutate(formData as any);
  };

  useEffect(() => {
    (createRegion?.isSuccess || updateRegion?.isSuccess) && onClose();
  }, [createRegion, updateRegion, onClose]);

  const handleCancel = () => {
    reset();
    onClose();
  };

  const determineNewRegionCode = (
    newCountryCode: string,
    currentRegionCode: string | undefined
  ): string | undefined => {
    const expectedPrefix = COUNTRY_TO_REGION_PREFIX[newCountryCode];

    if (expectedPrefix) {
      if (currentRegionCode) {
        return currentRegionCode.startsWith(expectedPrefix) ? currentRegionCode : '';
      }
      return expectedPrefix;
    }

    return currentRegionCode ? '' : undefined;
  };

  const handleCountryCodeChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const rawValue = e.target.value;
      const newCountryCode = rawValue
        .toUpperCase()
        .replace(/[^A-Z]/g, '')
        .slice(0, 3);

      // Update the country code
      setValue('country_code', newCountryCode, {
        shouldValidate: true,
        shouldDirty: true,
      });

      // Determine and update the region code if necessary
      const newRegionCode = determineNewRegionCode(newCountryCode, regionCode);
      if (newRegionCode !== regionCode) {
        setValue('region_code', newRegionCode || '', {
          shouldValidate: true,
          shouldDirty: true,
        });
      }
    },
    [regionCode, setValue]
  );

  // Handler for region_code changes
  const handleRegionCodeChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const regionCode = e.target.value
        .toUpperCase()
        .replace(/[^A-Z0-9]/g, '')
        .slice(0, 6);
      setValue('region_code', regionCode, { shouldValidate: true, shouldDirty: true });

      if (regionCode) {
        const prefix = regionCode.slice(0, 2);
        const correspondingCountry = Object.keys(COUNTRY_TO_REGION_PREFIX).find(
          (key) => COUNTRY_TO_REGION_PREFIX[key] === prefix
        );

        if (correspondingCountry) {
          if (countryCode !== correspondingCountry) {
            setValue('country_code', correspondingCountry, { shouldValidate: true, shouldDirty: true });
          }
        } else {
          setValue('country_code', '', { shouldValidate: true, shouldDirty: true });
        }
      } else {
        if (countryCode) {
          setValue('country_code', '', { shouldValidate: true, shouldDirty: true });
        }
      }
    },
    [countryCode, setValue]
  );

  return (
    <Box
      as="form"
      bg="bg-surface"
      boxShadow={useColorModeValue('sm', 'sm-dark')}
      borderRadius="lg"
      onSubmit={handleSubmit(onSubmit)}
    >
      <Stack
        spacing="5"
        px={{
          base: '4',
          md: '6',
        }}
        py={{
          base: '5',
          md: '6',
        }}
      >
        <Heading size={'md'}>{isEdit ? 'Update Region' : 'Create New Region'}</Heading>
        <Divider />
        {isSubmitting || isLoading ? (
          <Loader>Submitting...</Loader>
        ) : (
          <Stack spacing="6">
            {/* Region Type and Lifecycle - 2 columns */}
            <Stack
              direction={{
                base: 'column',
                md: 'row',
              }}
              spacing="6"
            >
              <FormControl isInvalid={!!errors.region_type} isRequired={!isEdit}>
                <Flex alignItems="center" mb={1}>
                  <FormLabel htmlFor="region_type" mb={0}>
                    Region Type
                  </FormLabel>

                  <Tooltip label="Select a region type from the list" placement="top-start">
                    <InfoIcon color="gray.500" cursor="pointer" boxSize="1em" />
                  </Tooltip>
                </Flex>

                <Select
                  id="region_type"
                  data-testid="region-type-select"
                  size="md"
                  value={regionType}
                  {...register('region_type', {
                    required: !isEdit ? 'Region type is required' : false,
                  })}
                >
                  {Object.entries(REGION_TYPES).map(([value, label]) => (
                    <option key={value} value={value}>
                      {label}
                    </option>
                  ))}
                </Select>

                <FormErrorMessage data-testid="region-type-error">{errors.region_type?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.lifecycle} isRequired={!isEdit}>
                <Flex alignItems="center" mb={1}>
                  <FormLabel htmlFor="lifecycle" mb={0}>
                    Lifecycle
                  </FormLabel>

                  <Tooltip
                    label="When you assign a lifecycle status to a region, all devices within that region inherit the same status."
                    placement="top-start"
                  >
                    <InfoIcon color="gray.500" cursor="pointer" boxSize="1em" />
                  </Tooltip>
                </Flex>

                <Select
                  id="lifecycle"
                  data-testid="lifecycle-select"
                  size="md"
                  {...register('lifecycle', {
                    required: !isEdit ? 'Lifecycle is required' : false,
                  })}
                >
                  {Object.entries(LIFE_CYCLE).map(([key, value]) => (
                    <option key={value} value={value}>
                      {key}
                    </option>
                  ))}
                </Select>
                <FormErrorMessage data-testid="lifecycle-error">{errors.lifecycle?.message}</FormErrorMessage>
              </FormControl>
            </Stack>

            {/* Region Code, Region Name, Country Code - 3 columns */}
            <Stack
              direction={{
                base: 'column',
                md: 'row',
              }}
              spacing="6"
            >
              <FormControl isInvalid={!!errors.region_code} isRequired>
                <Flex alignItems="center" mb={1}>
                  <FormLabel htmlFor="region_code" mb={0}>
                    Region Code
                  </FormLabel>
                  <Tooltip
                    label="Region code must start with 2-letter ISO country code followed by exactly 4-letter code"
                    placement="top-start"
                  >
                    <InfoIcon color="gray.500" cursor="pointer" boxSize="1em" />
                  </Tooltip>
                </Flex>
                <Input
                  id="region_code"
                  data-testid="region-code-input"
                  placeholder="e.g., GBMARL"
                  maxLength={6}
                  {...register('region_code', {
                    onChange: handleRegionCodeChange,
                  })}
                  isDisabled={isEdit}
                  bg={isEdit ? 'gray.100' : 'white'}
                  color={isEdit ? 'gray.600' : 'inherit'}
                />
                <FormErrorMessage data-testid="region-code-error">{errors.region_code?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.region_name} isRequired={!isEdit}>
                <Flex alignItems="center" mb={1}>
                  <FormLabel htmlFor="region_name" mb={0}>
                    Region Name
                  </FormLabel>
                  <Tooltip label="Enter the full name of the region" placement="top-start">
                    <InfoIcon color="gray.500" cursor="pointer" boxSize="1em" />
                  </Tooltip>
                </Flex>
                <Input
                  id="region_name"
                  data-testid="region-name-input"
                  placeholder="e.g., Marlow"
                  {...register('region_name')}
                />
                <FormErrorMessage data-testid="region-name-error">{errors.region_name?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.country_code} isRequired>
                <Flex alignItems="center" mb={1}>
                  <FormLabel htmlFor="country_code" mb={0}>
                    Country Code
                  </FormLabel>
                  <Tooltip label="Must be 3-letter ISO country code" placement="top-start">
                    <InfoIcon color="gray.500" cursor="pointer" boxSize="1em" />
                  </Tooltip>
                </Flex>
                <Input
                  id="country_code"
                  data-testid="country-code-input"
                  placeholder="e.g., GBR"
                  maxLength={3}
                  {...register('country_code', {
                    onChange: handleCountryCodeChange,
                  })}
                  isDisabled={isEdit}
                  bg={isEdit ? 'gray.100' : 'white'}
                  color={isEdit ? 'gray.600' : 'inherit'}
                />
                <FormErrorMessage data-testid="country-code-error">{errors.country_code?.message}</FormErrorMessage>
              </FormControl>
            </Stack>

            {/* Latitude, Longitude, Radius - 3 columns */}
            <Stack
              direction={{
                base: 'column',
                md: 'row',
              }}
              spacing="6"
            >
              <FormControl isInvalid={!!errors.latitude} isRequired={!isEdit}>
                <FormLabel htmlFor="latitude">Latitude</FormLabel>
                <Input
                  id="latitude"
                  data-testid="latitude-input"
                  type="number"
                  step="0.000001"
                  placeholder="-90.00 to 90.00"
                  {...register('latitude', {
                    onChange: (e) => {
                      const value = e.target.value;
                      if (value === '') return;
                      const num = parseFloat(value);
                      if (num < -90) setValue('latitude', -90, { shouldValidate: true, shouldDirty: true });
                      if (num > 90) setValue('latitude', 90, { shouldValidate: true, shouldDirty: true });
                    },
                  })}
                />
                <FormErrorMessage data-testid="latitude-error">{errors.latitude?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.longitude} isRequired={!isEdit}>
                <FormLabel htmlFor="longitude">Longitude</FormLabel>
                <Input
                  id="longitude"
                  data-testid="longitude-input"
                  type="number"
                  step="0.000001"
                  placeholder="-180.00 to 180.00"
                  {...register('longitude', {
                    onChange: (e) => {
                      const value = e.target.value;
                      if (value === '') return;
                      const num = parseFloat(value);
                      if (num < -180) setValue('longitude', -180, { shouldValidate: true, shouldDirty: true });
                      if (num > 180) setValue('longitude', 180, { shouldValidate: true, shouldDirty: true });
                    },
                  })}
                />
                <FormErrorMessage data-testid="longitude-error">{errors.longitude?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.radius} isRequired={!isEdit}>
                <FormLabel htmlFor="radius">Radius (m)</FormLabel>
                <Input
                  id="radius"
                  data-testid="radius-input"
                  type="number"
                  min="0"
                  step="1"
                  placeholder="Enter radius in meters"
                  {...register('radius', {
                    onChange: (e) => {
                      const value = e.target.value;
                      if (value === '') return;
                      const num = parseFloat(value);
                      if (num < 0) e.target.value = '0';
                    },
                  })}
                />
                <FormErrorMessage data-testid="radius-error">{errors.radius?.message}</FormErrorMessage>
              </FormControl>
            </Stack>

            {/* Description - Single column */}
            <FormControl isInvalid={!!errors.description}>
              <FormLabel htmlFor="description">Description</FormLabel>
              <Textarea
                id="description"
                data-testid="description-input"
                placeholder="Enter region description"
                rows={3}
                {...register('description')}
              />
              <FormErrorMessage data-testid="description-error">{errors.description?.message}</FormErrorMessage>
            </FormControl>

            <Divider />

            <Heading size={'sm'} mt={1} mb={1}>
              CellVizion
            </Heading>

            <Stack spacing="6">
              <Stack
                direction={{
                  base: 'column',
                  md: 'row',
                }}
                spacing="6"
              >
                <FormControl isInvalid={!!errors.deployment_id}>
                  <FormLabel htmlFor="deployment_id">Deployment ID</FormLabel>
                  <Input
                    id="deployment_id"
                    data-testid="deployment-id-input"
                    placeholder="Enter deployment ID"
                    {...register('deployment_id')}
                  />
                  <FormErrorMessage data-testid="deployment-id-error">{errors.deployment_id?.message}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.deployment_name}>
                  <FormLabel htmlFor="deployment_name">Deployment Name</FormLabel>
                  <Input
                    id="deployment_name"
                    data-testid="deployment-name-input"
                    placeholder="Enter deployment name"
                    {...register('deployment_name')}
                  />
                  <FormErrorMessage data-testid="deployment-name-error">
                    {errors.deployment_name?.message}
                  </FormErrorMessage>
                </FormControl>
              </Stack>

              <Stack
                direction={{
                  base: 'column',
                  md: 'row',
                }}
                spacing="6"
              >
                <FormControl isInvalid={!!errors.customer_id}>
                  <FormLabel htmlFor="customer_id">Customer ID</FormLabel>
                  <Input
                    id="customer_id"
                    data-testid="customer-id-input"
                    placeholder="Enter customer ID"
                    {...register('customer_id')}
                  />
                  <FormErrorMessage data-testid="customer-id-error">{errors.customer_id?.message}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.customer_name}>
                  <FormLabel htmlFor="customer_name">Customer Name</FormLabel>
                  <Input
                    id="customer_name"
                    data-testid="customer-name-input"
                    placeholder="Enter customer name"
                    {...register('customer_name')}
                  />
                  <FormErrorMessage data-testid="customer-name-error">{errors.customer_name?.message}</FormErrorMessage>
                </FormControl>
              </Stack>
            </Stack>

            <Divider />
            <FormControl isInvalid={!!errors.discovery} mb={4}>
              <FormLabel htmlFor="discovery">Discovery</FormLabel>

              <Checkbox
                id="discovery"
                data-testid="discovery-checkbox"
                {...register('discovery')}
                isChecked={discovery}
              >
                Enable Region Discovery
              </Checkbox>
              <FormErrorMessage data-testid="discovery-error">{errors.discovery?.message}</FormErrorMessage>
            </FormControl>
          </Stack>
        )}
      </Stack>
      <Flex justify="flex-end" px="6" py="4" borderTopWidth="1px" gap="3">
        <ButtonGroup spacing="3">
          <Button
            bg="red.600"
            color="white"
            variant="solid"
            size="md"
            p="4"
            mr="2"
            shadow="lg"
            _hover={{ bg: 'red', opacity: 0.9, transform: 'translateY(-5px)' }}
            _disabled={{ bg: 'red', opacity: 0.3, cursor: 'not-allowed' }}
            _focus={{ boxShadow: 'lg' }}
            isDisabled={false}
            onClick={handleCancel}
          >
            <Text>Cancel</Text>
            <Box ml="1" p="2">
              <Icon as={FaTimes} boxSize="1.2em" />
            </Box>
          </Button>

          <Button
            type="submit"
            bg="green.600"
            color="white"
            variant="solid"
            size="md"
            p="4"
            mr="2"
            shadow="lg"
            _hover={{ bg: 'green', opacity: 0.9, transform: 'translateY(-5px)' }}
            _disabled={{ bg: 'green', opacity: 0.3, cursor: 'not-allowed' }}
            _focus={{ boxShadow: 'lg' }}
            isDisabled={isEdit && !hasChanged}
          >
            <Text> {isEdit ? 'Update Region' : 'Create Region'}</Text>
            <Box ml="1" p="2">
              <Icon as={isEdit ? FiEdit : FaSave} boxSize="1.2em" />
            </Box>
          </Button>
        </ButtonGroup>
      </Flex>
    </Box>
  );
};

export default CreateRegionForm;
