import {
  Box,
  Button,
  ButtonGroup,
  Divider,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Heading,
  Icon,
  Input,
  Modal,
  ModalCloseButton,
  ModalContent,
  ModalOverlay,
  Select,
  Stack,
  Text,
  Tooltip,
  useColorModeValue,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import Loader from '../../../components/loader/Loader';
import { useCallback, useEffect, useState, useMemo } from 'react';
import { InfoIcon } from '@chakra-ui/icons';
import { FaSave, FaTimes } from 'react-icons/fa';
import { FiEdit } from 'react-icons/fi';
import { FlattenedManagerProps } from '../hooks/useManagerColumns';
import { useCreateManager, useUpdateManager } from '../hooks/useManagerMutations';

interface CreateManagerFormProps {
  isOpen: boolean;
  onClose: () => void;
  isEdit?: boolean;
  defaultValues?: Partial<FlattenedManagerProps>;
  onSuccess?: (updatedManager?: any) => void;
}

const COMPONENT_TYPES: Record<string, string> = {
  ACP: 'ACP',
  AIRSPAN: 'AIRSPAN',
  ANTHOS: 'ANTHOS',
  BLUWIRELESS: 'BLUWIRELESS',
  CUCP: 'CUCP',
  CUUP: 'CUUP',
  DRUID: 'DRUID',
  DU: 'DU',
  E500: 'E500',
  ENODEB: 'ENODEB',
  FIBROLAN: 'FIBROLAN',
  FORTIMANAGER: 'FORTIMANAGER',
  FORTINET: 'FORTINET',
  JUNIPER: 'JUNIPER',
  MOSOLABS: 'MOSOLABS',
  PARTICLE: 'PARTICLE',
  PKI: 'PKI',
  RAD_K8: 'RAD_K8',
  SERVER: 'SERVER',
  SIXWIND: 'SIXWIND',
  UPS: 'UPS',
  VRANC: 'VRANC',
};

const RAN_TYPES: Record<string, string> = {
  '4G': '4G',
  '5G': '5G',
};

// Manager form schema with comprehensive validation - no default values
const createManagerSchema = z.object({
  manager_instance: z.string().min(1, 'Manager instance is required'),
  component_type: z.string().min(1, 'Component type is required'),
  manager_url: z
    .string()
    .min(1, 'Manager URL is required')
    .regex(/^https?:\/\/.+/, 'Please enter a valid URL (http or https)'),

  ran_type: z.string().optional(),
  credentials_username: z
    .string()
    .min(1, 'Username is required')
    .regex(/^[a-zA-Z0-9._-]+$/, 'Username can only contain letters, numbers, dots, dashes, and underscores'),
  credentials_password: z.string().min(8, 'Password must be at least 8 characters'),
});

const updateManagerSchema = createManagerSchema.extend({
  manager_instance: z.string().min(1, 'Manager instance is required'),
  component_type: z.string().min(1, 'Component type is required'),
  manager_url: z
    .string()
    .min(1, 'Manager URL is required')
    .regex(/^https?:\/\/.+/, 'Please enter a valid URL'),

  ran_type: z.string().optional(),
  credentials_username: z.string().optional(),
  credentials_password: z.string().optional(),
});

type CreateManagerSchema = z.infer<typeof createManagerSchema>;

const CreateManagerForm: React.FC<CreateManagerFormProps> = ({
  isOpen,
  onClose,
  isEdit = false,
  defaultValues,
  onSuccess,
}) => {
  const createManagerMutation = useCreateManager();
  const updateManagerMutation = useUpdateManager();

  const formDefaultValues = useMemo(() => {
    if (isEdit && defaultValues) {
      return {
        manager_instance: defaultValues.manager_instance || '',
        manager_type: defaultValues.manager_type || '',
        component_type: defaultValues.component_type || '',
        manager_url: defaultValues.manager_url || '',
        version: defaultValues.version || '',
        ran_type: defaultValues.ran_type || '',
        credentials_username: '',
        credentials_password: '',
      };
    }
    // For create mode, return empty values instead of defaults
    return {
      manager_instance: '',
      manager_type: '',
      component_type: '',
      manager_url: '',
      version: '',
      ran_type: '',
      credentials_username: '',
      credentials_password: '',
    };
  }, [isEdit, defaultValues]);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isValid, isDirty },
    reset,
    setValue,
    watch,
  } = useForm<CreateManagerSchema>({
    resolver: zodResolver(isEdit ? updateManagerSchema : createManagerSchema),
    mode: 'onChange',
    defaultValues: formDefaultValues,
  });

  // Reset form when defaultValues change (for edit mode)
  useEffect(() => {
    if (defaultValues && isEdit) {
      reset(formDefaultValues);
    }
  }, [defaultValues, formDefaultValues, reset, isEdit]);

  const allValues = watch();

  const hasChanged = useMemo(() => {
    if (!isEdit || !defaultValues) return false;
    return Object.keys(formDefaultValues).some((key) => {
      const currentValue = allValues[key as keyof CreateManagerSchema];
      const defaultValue = formDefaultValues[key as keyof CreateManagerSchema];
      return currentValue !== defaultValue;
    });
  }, [allValues, formDefaultValues, isEdit, defaultValues]);

  const onSubmit = async (data: CreateManagerSchema) => {
    // Transform data for API submission
    const managerData = {
      manager_instance: data.manager_instance,

      component_type: data.component_type,
      manager_url: data.manager_url,

      ran_type: data.ran_type,
      credentials: {
        username: data.credentials_username,
        password: data.credentials_password,
      },
    };

    if (isEdit) {
      // Update manager using mutation
      updateManagerMutation.mutate(
        { manager_instance: data.manager_instance, data: managerData },
        {
          onSuccess: (result) => {
            // Update form with the returned data from API
            if (result && result.manager) {
              const updatedFormData = {
                manager_instance: result.manager.manager_instance,
                manager_type: result.manager.manager_type || '',
                component_type: result.manager.component_type,
                manager_url: result.manager.manager_url,
                version: result.manager.version || '',
                ran_type: result.manager.ran_type || '',
                credentials_username: data.credentials_username, // Keep current username
                credentials_password: '', // Clear password for security
              };

              // Update form values with the response data
              reset(updatedFormData);
            }

            if (onSuccess) {
              onSuccess(result);
            }
            // Close modal immediately - let user see the updated values
            onClose();
          },
        }
      );
    } else {
      // Create manager using mutation
      createManagerMutation.mutate(managerData, {
        onSuccess: (result) => {
          if (onSuccess) {
            onSuccess(result);
          }
          onClose();
        },
      });
    }
  };

  const handleCancel = () => {
    reset();
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="2xl" isCentered>
      <ModalOverlay bg="blackAlpha.900" />
      <ModalContent>
        <ModalCloseButton />
        <Box
          as="form"
          bg="bg-surface"
          boxShadow={useColorModeValue('sm', 'sm-dark')}
          borderRadius="lg"
          position="relative"
          onSubmit={handleSubmit(onSubmit)}
        >
          <Stack
            spacing="5"
            px={{
              base: '4',
              md: '6',
            }}
            py={{
              base: '5',
              md: '6',
            }}
          >
            <Heading size={'md'}>{isEdit ? 'Update Manager' : 'Create New Manager'}</Heading>
            <Divider />
            {/* Loading overlay */}
            {(createManagerMutation.isLoading || updateManagerMutation.isLoading) && (
              <Box
                position="absolute"
                top="0"
                left="0"
                right="0"
                bottom="0"
                bg="rgba(255, 255, 255, 0.8)"
                zIndex="1000"
                display="flex"
                alignItems="center"
                justifyContent="center"
                borderRadius="lg"
              >
                <Loader>{isEdit ? 'Updating Manager...' : 'Creating Manager...'}</Loader>
              </Box>
            )}
            <Stack spacing="6" position="relative">
              {/* Manager Instance and Manager Type - 2 columns */}
              <Stack
                direction={{
                  base: 'column',
                  md: 'row',
                }}
                spacing="6"
              >
                <FormControl isInvalid={!!errors.manager_instance} isRequired>
                  <Flex alignItems="center" mb={1}>
                    <FormLabel htmlFor="manager_instance" mb={0}>
                      Manager Instance
                    </FormLabel>
                    <Tooltip label="Unique identifier for the manager instance" placement="top-start">
                      <InfoIcon color="gray.500" cursor="pointer" boxSize="1em" />
                    </Tooltip>
                  </Flex>
                  <Input
                    id="manager_instance"
                    data-testid="manager-instance-input"
                    placeholder="e.g., acp_marlow"
                    {...register('manager_instance')}
                    isDisabled={isEdit || createManagerMutation.isLoading || updateManagerMutation.isLoading}
                    bg={isEdit ? 'gray.100' : 'white'}
                    color={isEdit ? 'gray.600' : 'inherit'}
                  />
                  <FormErrorMessage data-testid="manager-instance-error">
                    {errors.manager_instance?.message}
                  </FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.component_type} isRequired>
                  <Flex alignItems="center" mb={1}>
                    <FormLabel htmlFor="component_type" mb={0}>
                      Component Type
                    </FormLabel>
                    <Tooltip label="Select the type of manager" placement="top-start">
                      <InfoIcon color="gray.500" cursor="pointer" boxSize="1em" />
                    </Tooltip>
                  </Flex>
                  <Select
                    id="component_type"
                    data-testid="component-type-select"
                    size="md"
                    {...register('component_type')}
                    isDisabled={isEdit || createManagerMutation.isLoading || updateManagerMutation.isLoading}
                    placeholder="Select Component Type"
                  >
                    {Object.entries(COMPONENT_TYPES).map(([value, label]) => (
                      <option key={value} value={value}>
                        {label}
                      </option>
                    ))}
                  </Select>
                  <FormErrorMessage data-testid="component-type-error">
                    {errors.component_type?.message}
                  </FormErrorMessage>
                </FormControl>
              </Stack>

              {/* Manager URL and RAN Type - 2 columns */}
              <Stack
                direction={{
                  base: 'column',
                  md: 'row',
                }}
                spacing="6"
              >
                <FormControl isInvalid={!!errors.manager_url} isRequired>
                  <Flex alignItems="center" mb={1}>
                    <FormLabel htmlFor="manager_url" mb={0}>
                      Manager URL
                    </FormLabel>
                    <Tooltip label="Enter the full URL to access the manager" placement="top-start">
                      <InfoIcon color="gray.500" cursor="pointer" boxSize="1em" />
                    </Tooltip>
                  </Flex>
                  <Input
                    id="manager_url"
                    data-testid="manager-url-input"
                    placeholder="https://**************"
                    {...register('manager_url')}
                    isDisabled={createManagerMutation.isLoading || updateManagerMutation.isLoading}
                  />
                  <FormErrorMessage data-testid="manager-url-error">{errors.manager_url?.message}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.ran_type}>
                  <Flex alignItems="center" mb={1}>
                    <FormLabel htmlFor="ran_type" mb={0}>
                      RAN Type
                    </FormLabel>
                    <Tooltip label="Radio Access Network type (optional)" placement="top-start">
                      <InfoIcon color="gray.500" cursor="pointer" boxSize="1em" />
                    </Tooltip>
                  </Flex>
                  <Select
                    id="ran_type"
                    data-testid="ran-type-select"
                    size="md"
                    {...register('ran_type')}
                    isDisabled={createManagerMutation.isLoading || updateManagerMutation.isLoading}
                  >
                    <option value="">Select RAN type</option>
                    {Object.entries(RAN_TYPES).map(([value, label]) => (
                      <option key={value} value={value}>
                        {label}
                      </option>
                    ))}
                  </Select>
                  <FormErrorMessage data-testid="ran-type-error">{errors.ran_type?.message}</FormErrorMessage>
                </FormControl>
              </Stack>

              <Divider />
              <Heading size={'sm'} mt={1} mb={1}>
                Credentials
              </Heading>

              {/* Credentials - 2 columns */}
              <Stack
                direction={{
                  base: 'column',
                  md: 'row',
                }}
                spacing="6"
              >
                <FormControl isInvalid={!!errors.credentials_username} isRequired={!isEdit}>
                  <Flex alignItems="center" mb={1}>
                    <FormLabel htmlFor="credentials_username" mb={0}>
                      Username
                    </FormLabel>
                    <Tooltip label="Username for manager authentication" placement="top-start">
                      <InfoIcon color="gray.500" cursor="pointer" boxSize="1em" />
                    </Tooltip>
                  </Flex>
                  <Input
                    id="credentials_username"
                    data-testid="credentials-username-input"
                    placeholder="Enter username"
                    {...register('credentials_username')}
                    isDisabled={createManagerMutation.isLoading || updateManagerMutation.isLoading}
                  />
                  <FormErrorMessage data-testid="credentials-username-error">
                    {errors.credentials_username?.message}
                  </FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.credentials_password} isRequired={!isEdit}>
                  <Flex alignItems="center" mb={1}>
                    <FormLabel htmlFor="credentials_password" mb={0}>
                      Password
                    </FormLabel>
                    <Tooltip label="Password for manager authentication" placement="top-start">
                      <InfoIcon color="gray.500" cursor="pointer" boxSize="1em" />
                    </Tooltip>
                  </Flex>
                  <Input
                    id="credentials_password"
                    data-testid="credentials-password-input"
                    type="password"
                    placeholder="Enter password"
                    {...register('credentials_password')}
                    isDisabled={createManagerMutation.isLoading || updateManagerMutation.isLoading}
                  />
                  <FormErrorMessage data-testid="credentials-password-error">
                    {errors.credentials_password?.message}
                  </FormErrorMessage>
                </FormControl>
              </Stack>
            </Stack>
          </Stack>
          <Flex justify="flex-end" px="6" py="4" borderTopWidth="1px" gap="3">
            <ButtonGroup spacing="3">
              <Button
                bg="red.600"
                color="white"
                variant="solid"
                size="md"
                p="4"
                mr="2"
                shadow="lg"
                _hover={{ bg: 'red', opacity: 0.9, transform: 'translateY(-5px)' }}
                _disabled={{ bg: 'red', opacity: 0.3, cursor: 'not-allowed' }}
                _focus={{ boxShadow: 'lg' }}
                isDisabled={createManagerMutation.isLoading || updateManagerMutation.isLoading}
                onClick={handleCancel}
              >
                <Text>Cancel</Text>
                <Box ml="1" p="2">
                  <Icon as={FaTimes} boxSize="1.2em" />
                </Box>
              </Button>

              <Button
                type="submit"
                bg="green.600"
                color="white"
                variant="solid"
                size="md"
                p="4"
                mr="2"
                shadow="lg"
                _hover={{ bg: 'green', opacity: 0.9, transform: 'translateY(-5px)' }}
                _disabled={{ bg: 'green', opacity: 0.3, cursor: 'not-allowed' }}
                _focus={{ boxShadow: 'lg' }}
                isDisabled={isEdit && !hasChanged}
                isLoading={createManagerMutation.isLoading || updateManagerMutation.isLoading}
                loadingText={isEdit ? 'Updating...' : 'Creating...'}
              >
                <Text>{isEdit ? 'Update Manager' : 'Create Manager'}</Text>
                <Box ml="1" p="2">
                  <Icon as={isEdit ? FiEdit : FaSave} boxSize="1.2em" />
                </Box>
              </Button>
            </ButtonGroup>
          </Flex>
        </Box>
      </ModalContent>
    </Modal>
  );
};

export default CreateManagerForm;
