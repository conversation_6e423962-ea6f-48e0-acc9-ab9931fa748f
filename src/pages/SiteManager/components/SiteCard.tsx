import { DeleteIcon, EditIcon, ExternalLinkIcon, Icon } from '@chakra-ui/icons';
import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  HStack,
  Link,
  Modal,
  ModalCloseButton,
  ModalContent,
  ModalOverlay,
  Text,
  useColorModeValue,
  useDisclosure,
  Badge,
  Grid,
  GridItem,
  Divider,
  VStack,
} from '@chakra-ui/react';

import { AUTH_TOKEN_KEY, READ_WRITE_ACCESS_ROLES } from '../../../data/constants';
import useLogin from '../../../hooks/useLogin';
import { Contact, Site, Country } from '../../../types/InventoryManager.type';
import useContactList from '../hooks/useContactList';
import useCountry from '../hooks/useCountry';
import useDeleteSite from '../hooks/useDeleteSite';
import Contacts from './Contacts';
import CreateSiteForm from './CreateSiteForm';

export const SiteCard = ({ site }: { site: Site }) => {
  const {
    site_id,
    name,
    description,
    address,
    additional_info,
    latitude,
    longitude,
    country_code,
    site_cells,
    site_nodes,
  } = site;

  const country = useCountry(country_code) as Country;
  const { data: contacts } = useContactList(site_id);
  const deleteSite = useDeleteSite();
  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const checkRoleAccess = checkApplicationAccess(READ_WRITE_ACCESS_ROLES);
  const { isOpen, onOpen, onClose } = useDisclosure();

  // Color mode values - must be called at top level
  const cardBg = useColorModeValue('white', 'gray.800');
  const headerBg = useColorModeValue('gray.50', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const headingColor = useColorModeValue('gray.800', 'white');
  const textColor = useColorModeValue('gray.600', 'gray.300');
  const sectionHeadingColor = useColorModeValue('gray.700', 'gray.200');
  const descriptionColor = useColorModeValue('gray.600', 'gray.400');
  const labelColor = useColorModeValue('gray.500', 'gray.400');

  return (
    <>
      <Card boxShadow="lg" borderRadius="xl" overflow="hidden" bg={cardBg} border="1px" borderColor={borderColor}>
        <CardHeader bg={headerBg} py="4">
          <Flex justifyContent="space-between" alignItems="center">
            <VStack align="start" spacing="1" flex="1">
              <Heading as="h2" size="lg" color={headingColor} fontWeight="bold" data-testid="site-heading">
                {name} - Site
              </Heading>
              <Text fontSize="md" color={textColor} fontWeight="medium">
                Site ID: {site_id}
              </Text>
              <Badge colorScheme="green" variant="subtle" fontSize="xs">
                {country?.country_name}
              </Badge>
              {site.default_site && (
                <Badge colorScheme="blue" variant="subtle" fontSize="xs">
                  Default
                </Badge>
              )}
              ]
            </VStack>
            {checkRoleAccess && (
              <HStack spacing="2">
                <Button
                  size="sm"
                  variant="outline"
                  colorScheme="blue"
                  onClick={() => onOpen()}
                  leftIcon={<Icon as={EditIcon} />}
                >
                  Edit
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  colorScheme="red"
                  onClick={() => deleteSite.mutate(site_id)}
                  leftIcon={<Icon as={DeleteIcon} />}
                >
                  Delete
                </Button>
              </HStack>
            )}
          </Flex>
        </CardHeader>

        <CardBody p="6">
          <VStack spacing="6" align="stretch">
            {/* Description */}
            {description && (
              <>
                <Box data-testid="site-description">
                  <Heading size="sm" color={sectionHeadingColor} mb="2">
                    Description
                  </Heading>
                  <Text fontSize="sm" color={descriptionColor}>
                    {description}
                  </Text>
                </Box>
                <Divider />
              </>
            )}

            {/* Location Information */}
            <Box data-testid="site-location">
              <Heading size="sm" color={sectionHeadingColor} mb="3">
                Location Details
              </Heading>
              <Grid templateColumns="repeat(2, 1fr)" gap="4">
                <GridItem>
                  <Text fontSize="xs" color={labelColor} mb="1">
                    ADDRESS
                  </Text>
                  <Text fontSize="sm" fontWeight="medium">
                    {address}
                  </Text>
                </GridItem>
                <GridItem>
                  <Link
                    color="blue.500"
                    fontSize="sm"
                    fontWeight="medium"
                    href={`https://maps.google.com/?q=${latitude},${longitude}`}
                    isExternal
                    _hover={{ color: 'blue.600' }}
                  >
                    View on Map <ExternalLinkIcon mx="2px" />
                  </Link>
                </GridItem>
                <GridItem>
                  <Text fontSize="xs" color={labelColor} mb="1">
                    LATITUDE
                  </Text>
                  <Text fontSize="sm" fontWeight="medium">
                    {latitude}
                  </Text>
                </GridItem>
                <GridItem>
                  <Text fontSize="xs" color={labelColor} mb="1">
                    LONGITUDE
                  </Text>
                  <Text fontSize="sm" fontWeight="medium">
                    {longitude}
                  </Text>
                </GridItem>
              </Grid>
            </Box>

            {/* Additional Information */}
            {additional_info && (
              <>
                <Divider />
                <Box data-testid="site-additional-info">
                  <Heading size="sm" color={sectionHeadingColor} mb="2">
                    Additional Information
                  </Heading>
                  <Text fontSize="sm" color={descriptionColor}>
                    {additional_info}
                  </Text>
                </Box>
              </>
            )}

            <Divider />

            {/* Deployment Statistics */}
            <Box data-testid="site-deployment">
              <Heading size="sm" color={sectionHeadingColor} mb="3">
                Deployment Statistics
              </Heading>
              <Grid templateColumns="repeat(2, 1fr)" gap="4">
                <GridItem>
                  <Text fontSize="xs" color={labelColor} mb="1">
                    CELLS DEPLOYED
                  </Text>
                  <HStack spacing="2">
                    <Badge colorScheme="blue" variant="subtle" fontSize="md">
                      {site_cells?.length || 0} Cells
                    </Badge>
                  </HStack>
                </GridItem>
                <GridItem>
                  <Text fontSize="xs" color={labelColor} mb="1">
                    NODES DEPLOYED
                  </Text>
                  <HStack spacing="2">
                    <Badge colorScheme="green" variant="subtle" fontSize="md">
                      {site_nodes?.length || 0} Nodes
                    </Badge>
                  </HStack>
                </GridItem>
              </Grid>
            </Box>

            {/* Contacts */}
            {(contacts ?? [])?.length > 0 && (
              <>
                <Divider />
                <Box data-testid="site-contacts">
                  <Accordion allowMultiple>
                    <AccordionItem border="none">
                      <AccordionButton px="0" py="2">
                        <Box flex="1" textAlign="left">
                          <Heading size="sm" color={sectionHeadingColor}>
                            Contacts ({contacts?.length})
                          </Heading>
                        </Box>
                        <AccordionIcon />
                      </AccordionButton>
                      <AccordionPanel px="0">
                        <VStack spacing="3" align="stretch">
                          {contacts?.map((item: Contact, index: number) => (
                            <Contacts key={index} contact={item} />
                          ))}
                        </VStack>
                      </AccordionPanel>
                    </AccordionItem>
                  </Accordion>
                </Box>
              </>
            )}
          </VStack>
        </CardBody>
      </Card>
      <Modal isOpen={isOpen} onClose={onClose} size="2xl" isCentered>
        <ModalOverlay bg="blackAlpha.900" />
        <ModalContent>
          <ModalCloseButton />
          <CreateSiteForm
            onClose={onClose}
            isEdit={true}
            defaultSiteValues={{
              ...site,
              region: `${site.region_code}-${site.country_code}`,
            }}
          />
        </ModalContent>
      </Modal>
    </>
  );
};

export default SiteCard;
