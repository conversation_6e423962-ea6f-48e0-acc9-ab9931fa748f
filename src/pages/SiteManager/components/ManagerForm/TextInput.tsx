/**
 * Reusable text input component for manager forms
 */

import React from 'react';
import { Input } from '@chakra-ui/react';
import { UseFormRegisterReturn } from 'react-hook-form';
import { FormField } from './FormField';
import { FormFieldProps } from '../../types/manager.types';

interface TextInputProps extends FormFieldProps {
  id: string;
  type?: 'text' | 'password' | 'email' | 'url';
  register: UseFormRegisterReturn;
  error?: string;
  bg?: string;
  color?: string;
}

export const TextInput: React.FC<TextInputProps> = ({
  id,
  label,
  tooltip,
  type = 'text',
  isRequired = false,
  isDisabled = false,
  placeholder,
  testId,
  register,
  error,
  bg = 'white',
  color = 'inherit',
}) => {
  return (
    <FormField id={id} label={label} tooltip={tooltip} isRequired={isRequired} error={error}>
      <Input
        id={id}
        type={type}
        data-testid={testId || `${id}-input`}
        placeholder={placeholder}
        isDisabled={isDisabled}
        bg={bg}
        color={color}
        {...register}
      />
    </FormField>
  );
};
