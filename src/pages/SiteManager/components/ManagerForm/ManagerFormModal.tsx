/**
 * Manager Form Modal - Modular and reusable
 */

import React, { useEffect, useMemo } from 'react';
import {
  Box,
  Divider,
  Heading,
  Modal,
  ModalCloseButton,
  ModalContent,
  ModalOverlay,
  Stack,
  useColorModeValue,
  Text,
  HStack,
  Circle,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

// Internal imports
import { ManagerFormModalProps, ManagerFormState } from '../../types/manager.types';
import { UpdateRegionModal } from '../UpdateRegionModal';
import { FlattenedManagerProps } from '../../hooks/useManagerColumns';
import { PostManagerProps } from '../../../../types/InventoryManager.type';
import { MANAGER_TYPES, COMPONENT_TYPES, RAN_TYPES, FIELD_CONFIG } from '../../constants/manager.constants';
import { CreateManagerSchema, getManagerSchema } from '../../schemas/manager.schemas';
import {
  generateDefaultFormValues,
  hasFormValuesChanged,
  transformFormDataToApiPayload,
  transformApiResponseToFormData,
  getLoadingMessage,
  getModalTitle,
} from '../../utils/manager.utils';
import { useCreateManager, useUpdateManager } from '../../hooks/useManagerMutations';
import { TextInput, SelectInput, LoadingOverlay, FormButtons } from './index';

export const ManagerFormModal: React.FC<ManagerFormModalProps> = ({
  isOpen,
  onClose,
  isEdit = false,
  defaultValues,
  onSuccess,
}) => {
  // State for two-step process
  const [currentStep, setCurrentStep] = React.useState(1);
  const [createdManager, setCreatedManager] = React.useState<PostManagerProps | null>(null);

  // Mutation hooks
  const createManagerMutation = useCreateManager();
  const updateManagerMutation = useUpdateManager();

  // Form state
  const formState: ManagerFormState = {
    isLoading: createManagerMutation.isLoading || updateManagerMutation.isLoading,
    hasChanged: false,
    operation: isEdit ? 'update' : 'create',
  };

  // Generate form default values
  const formDefaultValues = useMemo(() => {
    return generateDefaultFormValues(isEdit, defaultValues);
  }, [isEdit, defaultValues]);

  // Form setup
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isDirty },
    reset,
    watch,
  } = useForm<CreateManagerSchema>({
    resolver: zodResolver(getManagerSchema(isEdit)),
    mode: 'onChange',
    defaultValues: formDefaultValues,
  });

  // Watch all values for change detection
  const allValues = watch();

  // Check if form has changed from defaults
  const hasChanged = useMemo(() => {
    if (!isEdit || !defaultValues) return false;
    return hasFormValuesChanged(allValues, formDefaultValues);
  }, [allValues, formDefaultValues, isEdit, defaultValues]);

  // Reset form when defaultValues change (for edit mode)
  useEffect(() => {
    if (defaultValues && isEdit) {
      reset(formDefaultValues);
    }
  }, [defaultValues, formDefaultValues, reset, isEdit]);

  // Form submission handler
  const onSubmit = async (data: CreateManagerSchema) => {
    const apiPayload = transformFormDataToApiPayload(data);

    if (isEdit) {
      // Update manager
      updateManagerMutation.mutate(
        { manager_instance: data.manager_instance, data: apiPayload },
        {
          onSuccess: (result) => {
            // Update form with API response
            if (result?.manager) {
              const updatedFormData = transformApiResponseToFormData(result.manager, {
                username: data.credentials_username,
                password: '',
              });
              reset(updatedFormData);
            }

            if (onSuccess) {
              onSuccess(result);
            }
            onClose();
          },
        }
      );
    } else {
      // Create manager
      createManagerMutation.mutate(apiPayload, {
        onSuccess: (result) => {
          // Move to step 2 for region management
          setCreatedManager(result);
          setCurrentStep(2);
        },
      });
    }
  };

  // Handle cancel
  const handleCancel = () => {
    reset();
    setCurrentStep(1);
    setCreatedManager(null);
    onClose();
  };

  // If we're on step 2, show the region modal
  if (currentStep === 2 && createdManager && !isEdit) {
    const tempManager: FlattenedManagerProps = {
      manager_instance: createdManager.manager_instance,
      manager_type: createdManager.manager_type || '',
      component_type: createdManager.component_type,
      manager_url: createdManager.manager_url,
      version: createdManager.version || '',
      ran_type: createdManager.ran_type || '',
      host_id: '',
      host_type: '',
      host_node_id: '',
      region_codes: '',
      region_names: '',
      regions: [],
    };

    return (
      <UpdateRegionModal
        isOpen={isOpen}
        onClose={() => {
          // Only close the entire flow when user explicitly clicks Close/Done
          setCurrentStep(1);
          setCreatedManager(null);
          if (onSuccess) {
            onSuccess(createdManager);
          }
          onClose();
        }}
        manager={tempManager}
        isStepTwo={true}
      />
    );
  }

  return (
    <Modal isOpen={isOpen} onClose={handleCancel} size="2xl" isCentered>
      <ModalOverlay bg="blackAlpha.900" />
      <ModalContent>
        <ModalCloseButton />
        <Box
          as="form"
          bg="bg-surface"
          boxShadow={useColorModeValue('sm', 'sm-dark')}
          borderRadius="lg"
          position="relative"
          onSubmit={handleSubmit(onSubmit)}
        >
          <Stack spacing="5" px={{ base: '4', md: '6' }} py={{ base: '5', md: '6' }}>
            {/* Header with Step Indicator */}
            <Box>
              {!isEdit && (
                <Box mb={4}>
                  <Text fontSize="xs" color="gray.500" mb={2} textAlign="center">
                    STEP 1 OF 2
                  </Text>
                  <HStack spacing={3} justify="center">
                    <HStack spacing={2}>
                      <Circle size="32px" bg="blue.500" color="white" boxShadow="sm">
                        <Text fontSize="sm" fontWeight="bold">
                          1
                        </Text>
                      </Circle>
                      <Box>
                        <Text fontSize="sm" fontWeight="semibold" color="blue.600">
                          Create Manager
                        </Text>
                      </Box>
                    </HStack>
                    <Box w="40px" h="2px" bg="gray.300" />
                    <HStack spacing={2}>
                      <Circle size="32px" bg="gray.300" color="gray.500">
                        <Text fontSize="sm" fontWeight="bold">
                          2
                        </Text>
                      </Circle>
                      <Box>
                        <Text fontSize="sm" color="gray.400">
                          Link Regions
                        </Text>
                      </Box>
                    </HStack>
                  </HStack>
                </Box>
              )}
              <Heading size="md" mb={3}>
                {getModalTitle(isEdit)}
              </Heading>
              <Divider />
            </Box>

            {/* Loading Overlay */}
            <LoadingOverlay isVisible={formState.isLoading} message={getLoadingMessage(isEdit)} />

            <Stack spacing="6" position="relative">
              {/* Row 1: Manager Instance and Component Type */}
              <Stack direction={{ base: 'column', md: 'row' }} spacing="6">
                <TextInput
                  id="manager_instance"
                  label={FIELD_CONFIG.MANAGER_INSTANCE.label}
                  tooltip={FIELD_CONFIG.MANAGER_INSTANCE.tooltip}
                  placeholder={FIELD_CONFIG.MANAGER_INSTANCE.placeholder}
                  isRequired
                  isDisabled={isEdit || formState.isLoading}
                  bg={isEdit ? 'gray.100' : 'white'}
                  color={isEdit ? 'gray.600' : 'inherit'}
                  register={register('manager_instance')}
                  error={errors.manager_instance?.message}
                  testId="manager-instance-input"
                />

                <SelectInput
                  id="component_type"
                  label={FIELD_CONFIG.COMPONENT_TYPE.label}
                  tooltip={FIELD_CONFIG.COMPONENT_TYPE.tooltip}
                  isRequired
                  isDisabled={isEdit || formState.isLoading}
                  options={COMPONENT_TYPES}
                  register={register('component_type')}
                  error={errors.component_type?.message}
                  testId="component-type-select"
                  emptyOptionText="Select component type"
                />
              </Stack>

              {/* Row 2: Manager URL and RAN Type */}
              <Stack direction={{ base: 'column', md: 'row' }} spacing="6">
                <TextInput
                  id="manager_url"
                  label={FIELD_CONFIG.MANAGER_URL.label}
                  tooltip={FIELD_CONFIG.MANAGER_URL.tooltip}
                  placeholder={FIELD_CONFIG.MANAGER_URL.placeholder}
                  type="url"
                  isRequired
                  isDisabled={formState.isLoading}
                  register={register('manager_url')}
                  error={errors.manager_url?.message}
                  testId="manager-url-input"
                />

                <SelectInput
                  id="ran_type"
                  label={FIELD_CONFIG.RAN_TYPE.label}
                  tooltip={FIELD_CONFIG.RAN_TYPE.tooltip}
                  isDisabled={formState.isLoading}
                  options={RAN_TYPES}
                  register={register('ran_type')}
                  error={errors.ran_type?.message}
                  testId="ran-type-select"
                  emptyOptionText="Select RAN type"
                />
              </Stack>

              {/* Credentials Section */}
              <Divider />
              <Heading size="sm" mt={1} mb={1}>
                Credentials
              </Heading>

              {/* Row 4: Username and Password */}
              <Stack direction={{ base: 'column', md: 'row' }} spacing="6">
                <TextInput
                  id="credentials_username"
                  label={FIELD_CONFIG.USERNAME.label}
                  tooltip={FIELD_CONFIG.USERNAME.tooltip}
                  placeholder={FIELD_CONFIG.USERNAME.placeholder}
                  isRequired={!isEdit}
                  isDisabled={formState.isLoading}
                  register={register('credentials_username')}
                  error={errors.credentials_username?.message}
                  testId="credentials-username-input"
                />

                <TextInput
                  id="credentials_password"
                  label={FIELD_CONFIG.PASSWORD.label}
                  tooltip={FIELD_CONFIG.PASSWORD.tooltip}
                  placeholder={FIELD_CONFIG.PASSWORD.placeholder}
                  type="password"
                  isRequired={!isEdit}
                  isDisabled={formState.isLoading}
                  register={register('credentials_password')}
                  error={errors.credentials_password?.message}
                  testId="credentials-password-input"
                />
              </Stack>
            </Stack>
          </Stack>

          {/* Form Buttons */}
          <FormButtons
            isEdit={isEdit}
            isLoading={formState.isLoading}
            hasChanged={hasChanged}
            onCancel={handleCancel}
          />
        </Box>
      </ModalContent>
    </Modal>
  );
};
