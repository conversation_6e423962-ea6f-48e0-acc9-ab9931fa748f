/**
 * Reusable form buttons component
 */

import React from 'react';
import { Button, ButtonGroup, Flex, Icon, Box, Text } from '@chakra-ui/react';
import { FaSave, FaTimes } from 'react-icons/fa';
import { FiEdit } from 'react-icons/fi';

interface FormButtonsProps {
  isEdit: boolean;
  isLoading: boolean;
  isDisabled?: boolean;
  hasChanged?: boolean;
  onCancel: () => void;
  loadingText?: string;
  submitText?: string;
  cancelText?: string;
}

export const FormButtons: React.FC<FormButtonsProps> = ({
  isEdit,
  isLoading,
  isDisabled = false,
  hasChanged = true,
  onCancel,
  loadingText,
  submitText,
  cancelText = 'Cancel',
}) => {
  const defaultSubmitText = isEdit ? 'Update Manager' : 'Create Manager';
  const defaultLoadingText = isEdit ? 'Updating...' : 'Creating...';

  return (
    <Flex justify="flex-end" px="6" py="4" borderTopWidth="1px" gap="3">
      <ButtonGroup spacing="3">
        {/* Cancel Button */}
        <Button
          bg="red.600"
          color="white"
          variant="solid"
          size="md"
          p="4"
          mr="2"
          shadow="lg"
          _hover={{ bg: 'red', opacity: 0.9, transform: 'translateY(-5px)' }}
          _disabled={{ bg: 'red', opacity: 0.3, cursor: 'not-allowed' }}
          _focus={{ boxShadow: 'lg' }}
          isDisabled={isLoading}
          onClick={onCancel}
        >
          <Box ml="1" p="2">
            <Icon as={FaTimes} boxSize="1.2em" />
          </Box>
          <Text>{cancelText}</Text>
        </Button>

        {/* Submit Button */}
        <Button
          type="submit"
          bg="green.600"
          color="white"
          variant="solid"
          size="md"
          p="4"
          mr="2"
          shadow="lg"
          _hover={{ bg: 'green', opacity: 0.9, transform: 'translateY(-5px)' }}
          _disabled={{ bg: 'green', opacity: 0.3, cursor: 'not-allowed' }}
          _focus={{ boxShadow: 'lg' }}
          isDisabled={isDisabled || (isEdit && !hasChanged)}
          isLoading={isLoading}
          loadingText={loadingText || defaultLoadingText}
        >
          <Box ml="1" p="2">
            <Icon as={isEdit ? FiEdit : FaSave} boxSize="1.2em" />
          </Box>
          <Text>{submitText || defaultSubmitText}</Text>
        </Button>
      </ButtonGroup>
    </Flex>
  );
};
