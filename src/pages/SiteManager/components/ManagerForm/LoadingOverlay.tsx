/**
 * Reusable loading overlay component
 */

import React from 'react';
import { Box } from '@chakra-ui/react';
import Loader from '../../../../components/loader/Loader';

interface LoadingOverlayProps {
  isVisible: boolean;
  message: string;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ isVisible, message }) => {
  if (!isVisible) return null;

  return (
    <Box
      position="absolute"
      top="0"
      left="0"
      right="0"
      bottom="0"
      bg="rgba(255, 255, 255, 0.8)"
      zIndex="1000"
      display="flex"
      alignItems="center"
      justifyContent="center"
      borderRadius="lg"
    >
      <Loader>{message}</Loader>
    </Box>
  );
};
