export function formatInReadableTimeDate(timestamp: string): string {
  if (typeof timestamp !== 'string') {
    throw new Error('Invalid timestamp');
  }
  timestamp = timestamp?.replace(/\.\d+/, '');
  return new Intl.DateTimeFormat('en-GB', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZone: 'UTC',
  }).format(new Date(timestamp));
}

export function formatISO8601Duration(duration: string): string {
  //P1DT41460S or T41085S
  const regex = /^P(?:(\d+)D)?T(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?$/;
  const matches = duration.match(regex);

  if (!matches) {
    return 'Invalid duration';
  }
  const days = parseInt(matches[1] || '0', 10);
  const hours = parseInt(matches[2] || '0', 10);
  const minutes = parseInt(matches[3] || '0', 10);
  const seconds = parseInt(matches[4] || '0', 10);

  // seconds into hours, minutes, and seconds
  const totalSeconds = seconds + hours * 3600 + minutes * 60;
  const finalHours = Math.floor(totalSeconds / 3600);
  const finalMinutes = Math.floor((totalSeconds % 3600) / 60);
  const finalSeconds = totalSeconds % 60;

  const adjustedHours = finalHours % 24;
  const additionalDays = Math.floor(finalHours / 24);

  // Format the output to include days, and then hours, minutes, and seconds in HH:MM:SS format
  const totalDays = days + additionalDays;
  const formattedDuration = `${totalDays} days, ${adjustedHours.toString().padStart(2, '0')}:${finalMinutes
    .toString()
    .padStart(2, '0')}:${finalSeconds.toString().padStart(2, '0')}`;

  return formattedDuration;
}
