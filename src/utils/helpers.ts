import _ from 'lodash';
import { formatInReadableTimeDate } from './formatInReadableTimeData';

//remove all null and undefined values from an object and its nested objects including arrays
export const compactObject = (val: any): any => {
  const data = Array.isArray(val) ? val.filter(Boolean) : val;
  return Object.keys(data).reduce(
    (acc: any, key: string) => {
      const value = data[key];
      if (value) acc[key] = typeof value === 'object' ? compactObject(value) : value;
      return acc;
    },
    Array.isArray(val) ? [] : {}
  );
};

//{ a: null, b: false, c: true, d: 0, e: 1, f: '', g: 'a', h: [null, false, '', true, 1, 'a'], i: { j: 0, k: false, l: 'a' } }
// { c: true, e: 1, g: 'a', h: [ true, 1, 'a' ], i: { l: 'a' } }

export function timeticksToTime(ms: number): string {
  const oneSecond = 100; // 100 timeticks/second
  const oneMinute: number = 60 * oneSecond; // 60 seconds
  const oneHour: number = oneMinute * 60; // 60 minutes
  const oneDay: number = oneHour * 24; // 24 hours

  const days: number = Math.floor(ms / oneDay);
  ms -= days * oneDay;

  const hours: number = Math.floor(ms / oneHour);
  ms -= hours * oneHour;

  const minutes: number = Math.floor(ms / oneMinute);
  ms -= minutes * oneMinute;

  const seconds: number = Math.floor(ms / oneSecond);

  return `${days.toString().padStart(2, '0')} days, ${hours.toString().padStart(2, '0')}:${minutes
    .toString()
    .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}
export function bitsToOptimal(bytes: number, decimals = 2): string {
  if (!+bytes) return '0 Bytes';

  const k = 1000;
  const dm: number = decimals < 0 ? 0 : decimals;
  const sizes: string[] = ['Bytes', 'Kb', 'Mb', 'Gb'];

  const i: number = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
}

export const statusToColor = (status: string): string => {
  const s = status?.toLowerCase();
  if (s?.includes('running') || s?.includes('ready') || s?.includes('success') || s?.includes('ok')) {
    return 'green';
  }
  if (s?.includes('pending')) {
    return 'yellow';
  }
  if (s?.includes('failed') || s?.includes('error') || s?.includes('not ready')) {
    return 'red';
  }
  if (s?.includes('succeeded')) {
    return 'blue';
  }
  return 'gray';
};

function formatTimeDuration(duration: string): string {
  const parts = duration.split(':');
  if (parts.length !== 3) {
    return 'Invalid duration';
  }
  const hours = parseInt(parts[0], 10);
  const minutes = parseInt(parts[1], 10);
  const secondsParts = parts[2].split('.');
  const seconds = parseInt(secondsParts[0], 10);
  let totalSeconds = hours * 3600 + minutes * 60 + seconds;

  const days = Math.floor(totalSeconds / 86400);
  totalSeconds %= 86400;
  const remainingHours = Math.floor(totalSeconds / 3600);
  totalSeconds %= 3600;
  const remainingMinutes = Math.floor(totalSeconds / 60);
  const remainingSeconds = totalSeconds % 60;

  // Format and return the result
  return `${days} days, ${remainingHours.toString().padStart(2, '0')}:${remainingMinutes
    .toString()
    .padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}
interface Stat {
  label: string;
  value: any;
  helpText?: string;
  renderAs?: string;
  SectionText?: string;
  optionalvalue?: any;
}
function isStat(stat: any): stat is Stat {
  return 'label' in stat && 'value' in stat;
}

export function constructStatsData(data: any, id: string | number | undefined, caller?: string | undefined): Stat[] {
  const currentUrl = new URL(window.location.href);
  let DOCKER_NMS_serv_Grafana_URL = '';
  if (process.env.NODE_ENV === 'production') {
    if (currentUrl.host === '**********') {
      DOCKER_NMS_serv_Grafana_URL = 'http://***********:3000';
    } else {
      DOCKER_NMS_serv_Grafana_URL = 'http://*************:3000';
    }
  }

  const allStats: Stat[] =
    caller == 'vsr'
      ? [
          {
            label: 'ID',
            value: data?.id,
          },
          {
            label: 'Up Time',
            value: formatTimeDuration(data?.up_time),
            helpText: 'Days, hh:mm:ss',
          },
          {
            label: 'Vsr Version',
            value: data?.vsr_data?.version,
          },
          { label: 'Agent Version', value: data.version },

          {
            label: 'License Expiry',
            value: data?.vsr_data?.license_expiry,
          },
          {
            label: 'Updated',
            value: data?.vsr_data?.updated,
          },
          {
            label: 'CPU Usage',
            value: Number(data?.vsr_data.cpu_usage ?? 0),
            renderAs: 'utilizationBar',
          },
          // {
          //   label: 'Started at',
          //   value: data?.started_at,
          // },
          // {
          //   label: 'Up Time',
          //   value: data?.up_time,
          // },
          // {
          //   label: 'Poll',
          //   value: data?.poll,
          // },
          // {
          //   label: 'Created',
          //   value: data?.created,
          // },
          // {
          //   label: 'Updated',
          //   value: data?.updated,
          // },
        ]
      : [
          {
            label: 'Component id',
            value: id,
            // helpText: '23.36%',
            // arrow: 'increase',
          },
          {
            label: 'IP address',
            value: data?.ip_address ?? 'N/A',
            // helpText: '9.05%',
            // arrow: 'decrease',
          },
          {
            label: 'Last Query Time',
            value: formatInReadableTimeDate((data?.last_query_time ?? '0').toString()),
          },
          {
            label: 'Name',
            value: data?.system?.sysName ?? 'N/A',
          },
          {
            label: 'Contact',
            value: data?.system?.sysContact ?? 'N/A',
          },
          {
            label: 'Up time',
            value: timeticksToTime(Number(data?.system?.sysUpTime ?? '0')),
            // helpText: 'Days, hh:mm:ss',
          },
          {
            label: 'Firmware version',
            value: data?.firmware ?? 'N/A',
          },
          {
            label: 'Monitored',
            value: data?.monitored ?? 'N/A',
          },
          { SectionText: 'Device', label: 'Device', value: 'Device' },
          //Device
          {
            label: 'CPU status',
            value: data?.psu?.flDevicePsuFanStatus?.toUpperCase() ?? 'N/A',
          },
          {
            label: 'PSU status',
            value: data?.psu?.flDevicePsuStatus?.toUpperCase() ?? 'N/A',
          },
          {
            label: 'Since',
            value: formatInReadableTimeDate((data?.status_change_time ?? '0').toString()),
            optionalvalue: 'Device',
          },
          { label: '', value: '' },
          {
            label: 'CPU utilization',
            value: Number(data?.cpu?.flDeviceCpuUtilization ?? '0'),
            renderAs: 'utilizationBar',
          },
          {
            label: 'Memory utilization',
            value: Number(data?.cpu?.flDeviceMemoryUtilization ?? '0'),
            renderAs: 'utilizationBar',
          },
          //sync
          { SectionText: 'Sync', label: 'Sync', value: 'Sync' },
          {
            label: 'NTP Sync status',
            value: data?.sync?.status?.flSyncCenterState ?? 'N/A',
          },
          {
            label: 'NTP Sync Status Valid Since',
            value: data?.sync?.status?.flSyncCenterStateLastChange
              ? timeticksToTime(Number(data?.sync?.status?.flSyncCenterStateLastChange))
              : 'N/A',
            // helpText: 'Days, hh:mm:ss',
          },
          {
            label: 'Selected Clock ID',
            value: data?.sync?.status?.flSyncCenterSelectedInputId ?? 'N/A',
          },
          {
            label: 'Clock type',
            value: data?.sync?.status?.flSyncCenterSelectedInputType ?? 'N/A',
          },
          {
            label: 'Selected Clock Input Valid Since',
            value: data?.sync?.status?.flSyncCenterSelectedInputLastChange
              ? timeticksToTime(Number(data?.sync?.status?.flSyncCenterSelectedInputLastChange))
              : 'N/A',
            // helpText: 'Days, hh:mm:ss',
          },
          {
            label: 'Output quality',
            value: data?.sync?.status?.flSyncCenterClockOutputQuality ?? 'N/A',
          },
          {
            label: 'Clock Output Quality Valid Since',
            value: data?.sync?.status?.flSyncCenterClockOutputQualityLastChange
              ? timeticksToTime(Number(data?.sync?.status?.flSyncCenterClockOutputQualityLastChange))
              : 'N/A',
            // helpText: 'Days, hh:mm:ss',
          },
          {
            label: 'BITS output',
            value: data?.sync?.status?.flSyncCenterBitsOutputState ?? 'N/A',
          },
          // GPS Section
          { SectionText: 'GPS', label: 'GPS', value: 'GPS' },
          {
            label: 'GPS State',
            value: data?.gps?.flGpsState ?? 'N/A',
          },
          { label: data?.gps && '', value: data?.gps && 'GPS Module' },
          {
            label: 'GPS State Valid Since',
            value: data?.gps?.flGpsStateLastChange ? timeticksToTime(Number(data?.gps?.flGpsStateLastChange)) : 'N/A',
          },
          {
            label: 'Part No',
            value: data?.gps?.flGpsModulePartNumber ?? 'N/A',
          },
          {
            label: 'GPS Time',
            value: data?.gps?.flGpsDateAndTime ? formatInReadableTimeDate(data.gps.flGpsDateAndTime.toString()) : 'N/A',
          },
          {
            label: 'Serial No',
            value: data?.gps?.flGpsModuleSerialNumber ?? 'N/A',
          },
          {
            label: 'Antenna State',
            value: data?.gps?.flGpsAntennaState ?? 'N/A',
          },
          {
            label: 'Hardware ID',
            value: data?.gps?.flGpsHardwareId ?? 'N/A',
          },
          // {
          //   label: 'Since',
          //   value: timeticksToTime(Number(data?.gps?.flGpsStateLastChange ?? '0')),
          // },
          // { label: 'key', value: 'val' },
          {
            label: 'Location',
            value: data?.gps?.flGpsLatitude ?? 'N/A',
            optionalvalue: data?.gps?.flGpsLongitude ?? 'N/A',
            renderAs: 'location',
          },
          {
            label: 'Firmware Version',
            value: data?.gps?.flGpsFirmwareVersion ?? 'N/A',
          },
          {
            label: 'Altitude (m)',
            value: data?.gps?.flGpsAltitude ?? 'N/A',
          },
          {
            label: 'Firmware Date',
            value: data?.gps?.flGpsFirmwareDate ?? 'N/A',
          },
          {
            label: 'Cable Delay (ns)',
            value: data?.gps?.flGpsCableDelay ?? 'N/A',
          },
          { label: data?.gps && '', value: data?.gps && '' },
          {
            label: '1PPS State',
            value: data?.gps?.flGps1PpsState ?? 'N/A',
          },
          {
            label: 'Tracked Satellites',
            value: data?.gps?.flGpsTrackedSatelliteCount ?? 'N/A',
          },
          // error Module Section
          { SectionText: 'Error', label: 'Error', value: 'Error' },
          {
            label: 'Last Error',
            value: data?.last_error ?? 'N/A',
          },
          {
            label: 'Error count',
            value: data?.query_error_count ?? 'N/A',
          },
          {
            label: 'Last Successful Query',
            value: formatInReadableTimeDate((data?.last_success_time ?? '0').toString()),
          },
        ];
  const updatedAllStats = allStats.filter(
    (stat: any) => isStat(stat) && !_.isNil(stat.value) && (stat.label === 'CPU Usage' || stat.value !== 0)
  );
  if (caller === 'vsr' && updatedAllStats.length % 2 !== 0) {
    updatedAllStats.push({ label: '', value: '' });
    return updatedAllStats;
  }
  return updatedAllStats;
}

export const ipv4Regex =
  /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

export const ipv6Regex =
  /^(?:[0-9a-f]{1,4}:){7}[0-9a-f]{1,4}|[0-9a-f]{1,4}(?:(:[0-9a-f]{1,4}){0,6}:[0-9a-f]{1,4}|(:[0-9a-f]{1,4}){0,5}:|(:[0-9a-f]{1,4}){0,4}:[0-9a-f]{1,4}:|(:[0-9a-f]{1,4}){0,3}:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(:[0-9a-f]{1,4}){0,2}:[0-9a-f]{1,4}:[0-9a-f]{1,4}:[0-9a-f]{1,4}|:[0-9a-f]{1,4}:[0-9a-f]{1,4}:[0-9a-f]{1,4}:[0-9a-f]{1,4}|:[0-9a-f]{1,4}:[0-9a-f]{1,4}:[0-9a-f]{1,4}|:[0-9a-f]{1,4}:[0-9a-f]{1,4})/;

export const validateIP = (value: string) => ipv4Regex.test(value) || ipv6Regex.test(value);

export const getStorageKey = (pathname: string): string => {
  if (pathname === '/cell-overview') {
    return '/cell-overview/cells';
  }
  return pathname;
};
