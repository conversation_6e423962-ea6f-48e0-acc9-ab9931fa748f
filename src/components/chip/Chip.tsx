import { Badge, ChakraProps, Icon } from '@chakra-ui/react';
import { STATUS } from '../../data/constants';

const CircleIcon = (props: ChakraProps) => (
  <Icon viewBox="0 0 200 200" {...props}>
    <path fill="currentColor" d="M 100, 100 m -75, 0 a 75,75 0 1,0 150,0 a 75,75 0 1,0 -150,0" />
  </Icon>
);

interface ChipProps {
  statusText?: string;
  hasStatusLight?: boolean;
}

const Chip = ({ statusText, hasStatusLight = false }: ChipProps) => {
  let colorScheme = 'gray';
  const displayText = statusText || 'Unknown';

  switch (statusText?.toLowerCase()) {
    case 'high':
    case 'major':
      colorScheme = 'red';
      break;
    case 'medium':
    case 'minor':
    case STATUS.ERROR:
      colorScheme = 'orange';
      break;
    case STATUS.WARNING.toLowerCase():
      colorScheme = 'yellow';
      break;
    case 'low':
    case 'none':
    case STATUS.OK:
      colorScheme = 'green';
      break;
    case STATUS.SHUTDOWN:
      colorScheme = 'gray';
      break;
    case STATUS.CRITICAL.toLowerCase():
      colorScheme = 'purple';
      break;
    // The default is 'gray', which is already set
  }

  return (
    <Badge
      borderRadius="full"
      px={3}
      py={1}
      textTransform="capitalize"
      fontWeight="medium"
      letterSpacing="wide"
      colorScheme={colorScheme}
      variant="subtle"
      boxShadow="sm"
    >
      {hasStatusLight && <CircleIcon boxSize="8px" mr="6px" />}
      {displayText}
    </Badge>
  );
};

export default Chip;
