import { Box } from '@chakra-ui/react';
import useGetClusterResourceByNodeId from '../../pages/CellOverview/hooks/services/use_Orc_GetClusterResources';
import Cluster from '../nodeComponents/server/cluster/Cluster';
import { useState } from 'react';

import { ErrorBoundary } from 'react-error-boundary';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../errorComponents/ErrorBoundaryFallback';
import NodeComponentErrorCard from '../errorComponents/NodeComponentErrorCard';
import { NODE_COMPONENT_TITLES } from '../../data/constants';
import Vsr from '../nodeComponents/server/vsr/Vsr';

const ClusterComponent = ({
  nodeOpen,
  queryNodeId,
  compName,
  cellData,
}: {
  nodeOpen: boolean;
  queryNodeId: string;
  compName: string;
  cellData?: any;
}) => {
  const { isLoading, error, data: clusterData } = useGetClusterResourceByNodeId(queryNodeId, nodeOpen);

  return (
    <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
      <Box data-testid="cells-node-pod-du-components" marginLeft="8">
        {clusterData?.pod?.error || clusterData?.pod?.error === null ? (
          <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
            <NodeComponentErrorCard
              id={clusterData?.id}
              errorData={clusterData}
              compName={NODE_COMPONENT_TITLES.DU_POD}
              testId="node-comp-error-card"
              marginSpace="8"
            />
          </ErrorBoundary>
        ) : (
          <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
            {clusterData?.resources?.map((resource: any, index: number) => (
              <>
                <Cluster data={resource} nodeId={queryNodeId} compName={compName} />
                {resource.components?.length > 0
                  ? resource.components.map((component: any, compIndex: number) => (
                      <Vsr key={`${index}-${compIndex}`} data={component} nodeId={index.toString()} />
                    ))
                  : null}
              </>
            ))}
          </ErrorBoundary>
        )}
      </Box>
    </ErrorBoundary>
  );
};

export default ClusterComponent;
