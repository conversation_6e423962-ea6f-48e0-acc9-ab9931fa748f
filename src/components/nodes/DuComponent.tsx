import { Alert, AlertIcon, Box, Text } from '@chakra-ui/react';
import { useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { NODE_COMPONENT_TITLES } from '../../data/constants';
import useGetPodNodeDetails from '../../pages/CellOverview/hooks/services/use_Orc_GetPodNodesDetails';
import { ErrorBoundaryFallback, ErrorBoundaryLogError } from '../errorComponents/ErrorBoundaryFallback';
import NodeComponentErrorCard from '../errorComponents/NodeComponentErrorCard';
import OranCard from '../nodeComponents/du-cuManager/OranCard';
import DuComponentsMenu from '../nodeComponents/du-cuManager/Du/DuComponentsMenu';
import InstanceDetails from '../nodeComponents/du-cuManager/Du/InstanceDetails';
import Loader from '../loader/Loader';
import DuComponentCard from '../nodeComponents/du-cuManager/Du';

const DuComponent = ({
  nodeOpen,
  queryNodeId,
  nodeType,
  node_serial_no,
}: {
  nodeOpen: boolean;
  queryNodeId: string;
  nodeType: string;
  node_serial_no?: any;
}) => {
  //Query

  const { isLoading, error, data } = useGetPodNodeDetails(queryNodeId, nodeOpen);

  if (isLoading) {
    return <Loader />;
  }

  return (
    <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
      <Box data-testid="cells-node-pod-du-components" marginLeft="8">
        {data?.pod?.error || data?.pod?.error === null ? (
          <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
            <NodeComponentErrorCard
              id={data?.id}
              errorData={data?.ru}
              compName={NODE_COMPONENT_TITLES.DU_POD}
              testId="node-comp-error-card"
              marginSpace="8"
            />
          </ErrorBoundary>
        ) : (
          <ErrorBoundary fallbackRender={ErrorBoundaryFallback} onError={ErrorBoundaryLogError}>
            {/* <OranCard
              nodeOpen={nodeOpen}
              queryNodeId={queryNodeId}
              type={nodeType}
              data={data}
              isLoading={isLoading}
              error={error}
            /> */}
            <DuComponentCard data={data} node_serial_no={node_serial_no} />
          </ErrorBoundary>
        )}
      </Box>
    </ErrorBoundary>
  );
};

export default DuComponent;
