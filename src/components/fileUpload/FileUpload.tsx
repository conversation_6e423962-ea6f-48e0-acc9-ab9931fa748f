import React, { useEffect, useRef, useState } from 'react';
import { Button, Input, Box, Flex } from '@chakra-ui/react';
import { UseFormReturn } from 'react-hook-form';
import { FiUpload, FiDownload } from 'react-icons/fi';
import { FileUploadTypes } from '../../data/constants';
import { DeploymentParams, InterfaceFieldData } from '../../types/duCuManager.type';
import { fieldMappings } from '../../pages/OranDuCuManager/hooks/interface/useTransformInterfaceTableData';
import transformIdentitiesTableData from '../../pages/OranDuCuManager/hooks/identities/useTransformIdentitiesTableData';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  EditedUserSettings,
  IdentityField,
} from '../../pages/OranDuCuManager/simpleView/interfaces/tables/DynamicTable';

export type DeploymentFormData = {
  deployment_name: string;
  cucp_site_name: string;
  cuup_site_name: string;
  du_site_name: string;
  du_cluster: number | null;
  cucp_cluster: number | null;
  cuup_cluster: number | null;
  f1_ip: string;
  e1_ip: string;
  ru_vendor: string | null;
  deployment_params: DeploymentParams;
};

type FileUploadProps = {
  formMethods:
    | UseFormReturn<InterfaceFieldData>
    | UseFormReturn<DeploymentFormData>
    | UseFormReturn<Record<string, string>>;
  caller?: string;
  uploadType: FileUploadTypes.import | FileUploadTypes.export | FileUploadTypes.upload | FileUploadTypes.download;
  text: string;
  editing?: Record<string, boolean>;
  setEditing?: React.Dispatch<React.SetStateAction<Record<string, boolean>>>;
  setEditedUserSettings?: React.Dispatch<React.SetStateAction<any>>;
  onBandSelection?: (selectedBand: string, selectedBandwidth?: string) => void;
  selectedOption?: any;
  getIdentitiesData?: any;
  getInterfaceData?: any;
  onFileUpload?: () => void;
  identityControl?: UseFormReturn<Record<string, string>>;
  maxCell?: number;
  onImportAttempt?: (importedData: any) => boolean | Promise<boolean>;
  editedUserSettings?: EditedUserSettings;
};

type FlatMap = {
  [cellKey: string]: string;
};

function isInterfaceFieldData(
  formMethods: UseFormReturn<InterfaceFieldData> | UseFormReturn<DeploymentFormData>
): formMethods is UseFormReturn<InterfaceFieldData> {
  return 'someSpecificInterfaceField' in formMethods.getValues();
}

// NOTE: the transform order is important here!
export function transformInterfaceForExport(flat: FlatMap): any {
  const deployment_name = flat['deployment_name'] || '';

  const du: any = {
    fh_c: {
      local_ip_address: '',
      subnet_mask: 0,
      gateway_address: '',
    },
    fh_u: {
      local_ip_address: '',
      gateway_address: '',
    },
    f1_c: {
      remote_address: '',
    },
    f1_u: {
      local_ip_address: '',
      gateway_address: '',
    },
    f1_c2: {
      remote_address: '',
    },
    f1_u2: {
      local_ip_address: '',
      gateway_address: '',
    },
    node_type: 'du',
  };

  // CU-CP side (node_type:'cu_cp')
  const cu_cp: any = {
    ng_c: {
      remote_address: '',
      gateway_address: '',
    },
    node_type: 'cu_cp',
  };

  // CU-UP side (node_type:'cu_up')
  const cu_up: any = {
    ng_u: {
      local_ip_address: '',
      gateway_address: '',
    },
    f1_u: {
      local_ip_address: '',
      gateway_address: '',
    },
    e1: {
      remote_address: '',
      local_ip_address: '',
    },
    node_type: 'cu_up',
  };

  // CU-CP2 side (only if two‐cell, otherwise we’ll delete)
  const cu_cp2: any = {
    ng_c: {
      remote_address: '',
      gateway_address: '',
    },
    node_type: 'cu_cp',
  };

  // CU-UP2 side (only if two‐cell, otherwise we’ll delete)
  const cu_up2: any = {
    ng_u: {
      local_ip_address: '',
      gateway_address: '',
    },
    f1_u: {
      local_ip_address: '',
      gateway_address: '',
    },
    e1: {
      remote_address: '',
      local_ip_address: '',
    },
    node_type: 'cu_up',
  };

  const cellKeyRegex = /^(.+?)-user_setting-(.+)-(\d+)$/;

  for (const [cellKey, rawStr] of Object.entries(flat)) {
    const m = cellKey.match(cellKeyRegex);
    if (!m) {
      continue;
    }

    const group = m[1];
    const fieldName = m[2];

    const val = rawStr || '';

    switch (group) {
      case 'du':
        switch (fieldName) {
          case 'FH-C localIpAddress':
            du.fh_c.local_ip_address = val;
            break;
          case 'FH-C subNetMask':
            du.fh_c.subnet_mask = parseInt(val, 10) || 0;
            break;
          case 'FH-C gatewayAddress':
            du.fh_c.gateway_address = val;
            break;

          case 'FH-U localIpAddress':
            du.fh_u.local_ip_address = val;
            break;
          case 'FH-U gatewayAddress':
            du.fh_u.gateway_address = val;
            break;

          case 'F1-C remoteAddress (Cell 1)':
            du.f1_c.remote_address = val;
            break;
          case 'F1-U localIpAddress (Cell 1)':
            du.f1_u.local_ip_address = val;
            break;
          case 'F1-U gatewayAddress (Cell 1)':
            du.f1_u.gateway_address = val;
            break;

          case 'F1-C remoteAddress (Cell 2)':
            du.f1_c2.remote_address = val;
            break;
          case 'F1-U localIpAddress (Cell 2)':
            du.f1_u2.local_ip_address = val;
            break;
          case 'F1-U gatewayAddress (Cell 2)':
            du.f1_u2.gateway_address = val;
            break;

          default:
            break;
        }
        break;

      case 'cu_cp':
        switch (fieldName) {
          case 'NgC remoteAddress':
            cu_cp.ng_c.remote_address = val;
            break;
          case 'NgC gatewayAddress':
            cu_cp.ng_c.gateway_address = val;
            break;
          default:
            break;
        }
        break;

      case 'cu_up':
        switch (fieldName) {
          case 'NgU localIpAddress':
            cu_up.ng_u.local_ip_address = val;
            break;
          case 'NgU gatewayAddress':
            cu_up.ng_u.gateway_address = val;
            break;

          case 'F1-U localIpAddress':
            cu_up.f1_u.local_ip_address = val;
            break;
          case 'F1-U gatewayAddress':
            cu_up.f1_u.gateway_address = val;
            break;

          case 'E1 remoteAddress':
            cu_up.e1.remote_address = val;
            break;
          case 'E1 localIpAddress':
            cu_up.e1.local_ip_address = val;
            break;
          default:
            break;
        }
        break;

      case 'cu_cp2':
        switch (fieldName) {
          case 'NgC remoteAddress':
            cu_cp2.ng_c.remote_address = val;
            break;
          case 'NgC gatewayAddress':
            cu_cp2.ng_c.gateway_address = val;
            break;
          default:
            break;
        }
        break;

      case 'cu_up2':
        switch (fieldName) {
          case 'NgU localIpAddress':
            cu_up2.ng_u.local_ip_address = val;
            break;
          case 'NgU gatewayAddress':
            cu_up2.ng_u.gateway_address = val;
            break;

          case 'F1-U localIpAddress':
            cu_up2.f1_u.local_ip_address = val;
            break;
          case 'F1-U gatewayAddress':
            cu_up2.f1_u.gateway_address = val;
            break;

          case 'E1 remoteAddress':
            cu_up2.e1.remote_address = val;
            break;
          case 'E1 localIpAddress':
            cu_up2.e1.local_ip_address = val;
            break;
          default:
            break;
        }
        break;

      default:
        break;
    }
  }

  if (du.f1_c2.remote_address === '') {
    delete du.f1_c2;
  }

  if (du.f1_u2.local_ip_address === '' && du.f1_u2.gateway_address === '') {
    delete du.f1_u2;
  }

  if (cu_cp2.ng_c.remote_address === '' && cu_cp2.ng_c.gateway_address === '') {
    delete cu_cp2.ng_c;
    delete cu_cp2.node_type;
  }

  if (
    cu_up2.ng_u.local_ip_address === '' &&
    cu_up2.ng_u.gateway_address === '' &&
    cu_up2.f1_u.local_ip_address === '' &&
    cu_up2.f1_u.gateway_address === '' &&
    cu_up2.e1.remote_address === '' &&
    cu_up2.e1.local_ip_address === ''
  ) {
    delete cu_up2.ng_u;
    delete cu_up2.f1_u;
    delete cu_up2.e1;
    delete cu_up2.node_type;
  }

  const result: any = {
    deployment_name,
    du,
    cu_cp,
    cu_up,
  };

  if (cu_cp2.node_type) {
    result.cu_cp2 = cu_cp2;
  }
  if (cu_up2.node_type) {
    result.cu_up2 = cu_up2;
  }

  return result;
}

export const transformIdentitiesForExport = (data: any) => {
  return {
    deployment_name: data?.deployment_name || '',
    shared: {
      gNodeB: { id: data['shared-gnodeb_id-user_setting-0'] || '' },
      PLMN: {
        MCC: data['shared-mcc-user_setting-1'] || '',
        MNC: data['shared-mnc-user_setting-2'] || '',
      },
      gNodeB2: { id: data['shared-gnodeb_id2-user_setting-3'] || '' },
      PLMN2: {
        MCC: data['shared-mcc2-user_setting-4'] || '',
        MNC: data['shared-mnc2-user_setting-5'] || '',
      },
      node_type: 'shared',
    },
    du: {
      tracking_area_code: data['du-tracking_area_code-user_setting-0'] || '',
      ran_area_code: data['du-ran_area_code-user_setting-1'] || '',
      physical_cell_id: data['du-physical_cell_id-user_setting-2'] || '',
      tracking_area_code2: data['du-tracking_area_code2-user_setting-3'] || '',
      ran_area_code2: data['du-ran_area_code2-user_setting-4'] || '',
      physical_cell_id2: data['du-physical_cell_id2-user_setting-5'] || '',
      node_type: 'du',
    },
  };
};

export const transformDeploymentForExport = (data: any) => {
  return {
    deployment_name: data?.deployment_name,
    du_site_name: data.du_site_name,
    cucp_site_name: data.cucp_site_name,
    cuup_site_name: data.cuup_site_name,
    du_cluster: data.du_cluster,
    cucp_cluster: data.cucp_cluster,
    cuup_cluster: data.cuup_cluster,
    f1_ip: data.f1_ip,
    e1_ip: data.e1_ip,
    ru_vendor: data.ru_vendor,
    deployment_params: data.deployment_params,
  };
};

export function generateInterfaceSchema(nestedInterfaces: Record<string, InterfaceFieldData[]>): z.ZodObject<any> {
  const shape: Record<string, z.ZodTypeAny> = {};

  const ipv4Regex = /^\d+\.\d+\.\d+\.\d+$/;
  const ipv6Regex = /:/;

  // DU group:
  nestedInterfaces.du.forEach((field, idx) => {
    const cellKey = `du-user_setting-${field.name}-${idx}`;
    if (field.name === 'FH-C subNetMask') {
      shape[cellKey] = z.string().refine(
        (val) => {
          const n = Number(val);
          return !isNaN(n) && n >= 0 && n <= 32;
        },
        { message: 'SubNetMask must be 0–32' }
      );
    } else {
      shape[cellKey] = z
        .string()
        .refine((val) => ipv4Regex.test(val) || ipv6Regex.test(val), {
          message: 'Invalid IP address format',
        })
        .optional();
    }
  });

  // CU-CP:
  nestedInterfaces.cu_cp.forEach((field, idx) => {
    const cellKey = `cu_cp-user_setting-${field.name}-${idx}`;
    shape[cellKey] = z
      .string()
      .refine((val) => ipv4Regex.test(val) || ipv6Regex.test(val), {
        message: 'Invalid IP address format',
      })
      .optional();
  });

  // CU-UP:
  nestedInterfaces.cu_up.forEach((field, idx) => {
    const cellKey = `cu_up-user_setting-${field.name}-${idx}`;
    shape[cellKey] = z
      .string()
      .refine((val) => ipv4Regex.test(val) || ipv6Regex.test(val), {
        message: 'Invalid IP address format',
      })
      .optional();
  });

  // CU-CP2 (if present):
  if (nestedInterfaces.cu_cp2) {
    nestedInterfaces.cu_cp2.forEach((field, idx) => {
      const cellKey = `cu_cp2-user_setting-${field.name}-${idx}`;
      shape[cellKey] = z
        .string()
        .refine((val) => ipv4Regex.test(val) || ipv6Regex.test(val), {
          message: 'Invalid IP address format',
        })
        .optional();
    });
  }

  // CU-UP2 (if present):
  if (nestedInterfaces.cu_up2) {
    nestedInterfaces.cu_up2.forEach((field, idx) => {
      const cellKey = `cu_up2-user_setting-${field.name}-${idx}`;
      shape[cellKey] = z
        .string()
        .refine((val) => ipv4Regex.test(val) || ipv6Regex.test(val), {
          message: 'Invalid IP address format',
        })
        .optional();
    });
  }

  return z.object(shape);
}

const FileUpload: React.FC<FileUploadProps> = ({
  formMethods,
  caller,
  uploadType,
  text,
  setEditing,
  editing,
  setEditedUserSettings,
  onBandSelection,
  selectedOption,
  getIdentitiesData,
  getInterfaceData,
  onFileUpload,
  identityControl,
  maxCell,
  onImportAttempt,
  editedUserSettings,
}) => {
  const [fileName, setFileName] = useState('FileName.json');
  const inputRef = useRef<HTMLInputElement | null>(null);

  const hasIdentityShape = (
    s: EditedUserSettings | undefined
  ): s is { shared?: Record<string, IdentityField>; du?: Record<string, IdentityField> } =>
    !!s && ('shared' in s || 'du' in s);

  const handleButtonClick = () => {
    if (uploadType === FileUploadTypes.import) {
      inputRef.current?.click();
    }
  };

  function generateIdentitiesSchema(nested: {
    shared?: Record<string, { name: string; default: string; user_setting?: string; target?: string }>;
    du?: Record<string, { name: string; default: string; user_setting?: string; target?: string }>;
  }) {
    const shape: Record<string, z.ZodTypeAny> = {};

    if (nested.shared) {
      Object.entries(nested.shared).forEach(([fieldKey, fieldObj], idx) => {
        const cellKey = `shared-${fieldKey}-user_setting-${idx}`;
        shape[cellKey] = z.string().min(1, { message: `${fieldKey} cannot be empty` });
      });
    }

    if (nested.du) {
      Object.entries(nested.du).forEach(([fieldKey, fieldObj], idx) => {
        const cellKey = `du-${fieldKey}-user_setting-${idx}`;
        shape[cellKey] = z.string().min(1, { message: `${fieldKey} cannot be empty` });
      });
    }

    return z.object(shape);
  }

  const handleFileChange = (formMethods: UseFormReturn<any>, e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (event) => {
      try {
        const importedData = JSON.parse(event.target?.result as string);

        importedData.deployment_name = selectedOption?.deployment_name || '';

        if (caller === 'simpleViewIdentities' && getIdentitiesData) {
          const { nested, flatMapForRHF } = transformIdentitiesTableData(
            importedData,
            selectedOption,
            getIdentitiesData,
            selectedOption?.ru_vendor
          );

          formMethods.reset(flatMapForRHF);
          setEditedUserSettings?.(nested);

          const initialEditingState: Record<string, boolean> = {};
          Object.keys(flatMapForRHF).forEach((cellKey) => {
            initialEditingState[cellKey] = true;
          });
          setEditing?.(initialEditingState);

          await new Promise(requestAnimationFrame);

          formMethods.control._options.resolver = zodResolver(generateIdentitiesSchema(nested));
          await formMethods.trigger();
          return;
        }

        if (caller === 'simpleViewInterfaces' && typeof onImportAttempt === 'function') {
          const accepted = await onImportAttempt(importedData);
          if (!accepted && e.target) {
            e.target.value = '';
          }
          return;
        }

        if (caller === 'simpleViewCustomResource') {
          if (typeof onImportAttempt === 'function') {
            const accepted = onImportAttempt(importedData);
            if (!accepted) {
              return;
            }
            return;
          }
          formMethods.reset(importedData);
          return;
        }
      } catch (error) {
        console.error('Error importing form data:', error);
      }
    };

    reader.readAsText(file);

    if (inputRef.current) {
      inputRef.current.value = '';
    }
  };

  const renderIcon = () => {
    return uploadType === FileUploadTypes.import ? <FiUpload /> : <FiDownload />;
  };

  const handleExport = () => {
    try {
      const flatVals = (identityControl ?? formMethods).getValues() as Record<string, any>;

      let exportData: any;

      if (caller === 'simpleViewIdentities') {
        if (!getIdentitiesData) {
          console.warn('No identities data yet—cannot export.');
          return;
        }

        const maxCells = maxCell;

        const idSettings = hasIdentityShape(editedUserSettings) ? editedUserSettings : {};
        const sharedSrc = idSettings.shared ?? getIdentitiesData!.shared;
        const duSrc = idSettings.du ?? getIdentitiesData!.du;

        const sharedEntries = Object.entries(sharedSrc).filter(([k]) => k !== 'node_type' && k !== 'gnodeb_du_name');

        const sharedIndexMap: Record<string, number> = {};
        sharedEntries.forEach(([key], idx) => {
          sharedIndexMap[key] = idx;
        });
        const readShared = (fieldKey: string): string => {
          const rowIndex = sharedIndexMap[fieldKey];
          const cellKey = `shared-${fieldKey}-user_setting-${rowIndex}`;
          const typedVal = flatVals[cellKey];
          if (typedVal !== undefined && typedVal !== '') {
            return typedVal;
          }
          return String(getIdentitiesData.shared[fieldKey]?.user_setting ?? '');
        };

        const sharedApi: any = {
          gNodeB: { id: readShared('gnodeb_id') },
          PLMN: { MCC: readShared('mcc'), MNC: readShared('mnc') },
          node_type: 'shared',
        };
        if (maxCells) {
          sharedApi.gNodeB2 = { id: readShared('gnodeb_id2') };
          sharedApi.PLMN2 = { MCC: readShared('mcc2'), MNC: readShared('mnc2') };
        }

        // build DU side
        const duEntries = Object.entries(getIdentitiesData.du).filter(
          ([k]) => k !== 'node_type' && k !== 'gnodeb_du_name'
        );
        const duIndexMap: Record<string, number> = {};
        duEntries.forEach(([key], idx) => {
          duIndexMap[key] = idx;
        });
        const readDu = (fieldKey: string): string => {
          const rowIndex = duIndexMap[fieldKey];
          const cellKey = `du-${fieldKey}-user_setting-${rowIndex}`;
          const typedVal = flatVals[cellKey];
          if (typedVal !== undefined && typedVal !== '') {
            return typedVal;
          }
          return String(getIdentitiesData.du[fieldKey]?.user_setting ?? '');
        };

        const duApi: any = {
          tracking_area_code: readDu('tracking_area_code'),
          ran_area_code: readDu('ran_area_code'),
          physical_cell_id: readDu('physical_cell_id'),
          node_type: 'du',
        };
        if (maxCells) {
          duApi.tracking_area_code2 = readDu('tracking_area_code2');
          duApi.ran_area_code2 = readDu('ran_area_code2');
          duApi.physical_cell_id2 = readDu('physical_cell_id2');
        }

        exportData = {
          deployment_name: selectedOption?.deployment_name || '',
          shared: sharedApi,
          du: duApi,
        };
      } else if (caller === 'simpleViewInterfaces') {
        exportData = transformInterfaceForExport(flatVals);
      } else if (caller === 'simpleViewCustomResource') {
        exportData = transformDeploymentForExport(flatVals);
      } else {
        console.warn(`handleExport(): caller="${caller}" not recognized. Aborting export.`);
        return;
      }

      const dataStr = JSON.stringify(exportData, null, 2);
      const blob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName.endsWith('.json') ? fileName : `${fileName}.json`;
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (rawError) {
      console.error('Error exporting data →', rawError);
      alert('An error occurred while exporting. (See console for details.)');
    }
  };

  useEffect(() => {
    const subscription = formMethods.watch((_, { type }) => {
      if (uploadType === FileUploadTypes.import || uploadType === FileUploadTypes.export) {
        formMethods.trigger();
      }
    }) as { unsubscribe: () => void };

    return () => {
      if (subscription && typeof subscription.unsubscribe === 'function') {
        subscription.unsubscribe();
      }
    };
  }, [formMethods]);

  if (uploadType === FileUploadTypes.import) {
    return (
      <Box>
        <Input
          type="file"
          ref={inputRef}
          display="none"
          accept="application/json"
          onChange={(e) => handleFileChange(formMethods, e)}
        />
        <Button leftIcon={renderIcon()} variant="outline" colorScheme="teal" onClick={handleButtonClick}>
          {text}
        </Button>
      </Box>
    );
  }

  // EXPORT button
  return (
    <Box>
      <Flex mb={2}>
        <Input
          placeholder={`Enter ${uploadType.toLowerCase()} name`}
          value={fileName}
          onChange={(e) => setFileName(e.target.value)}
          mr={2}
        />
        <Button leftIcon={renderIcon()} variant="outline" colorScheme="teal" onClick={handleExport} width="100%">
          {text}
        </Button>
      </Flex>
    </Box>
  );
};

export default FileUpload;
