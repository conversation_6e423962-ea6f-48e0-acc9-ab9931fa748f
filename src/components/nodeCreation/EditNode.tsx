/* eslint-disable react-hooks/rules-of-hooks */
import React, { useEffect, useState, useMemo } from 'react';
import { FormControl, FormLabel, Input, Select, Button, Box, Flex, Text, Icon, Stack, HStack } from '@chakra-ui/react';
import { ArrowBackIcon } from '@chakra-ui/icons';
import { useForm, Controller } from 'react-hook-form';
import MultiSelect from 'react-select';
import { useNavigate, useLocation } from 'react-router-dom';
import useGetNodeDataByNodeId from '../../pages/CellOverview/hooks/services/use_GetNodeDatabyNodeId';
import { ROLE_OF_NODE_ARRAY, ROLE_OF_NODE, LifeCycle_OPS, LIFE_CYCLE, NODE_TYPE } from '../../data/constants';
import { Node, UpdateNodeRequest } from '../../types/InventoryManager.type';
import useUpdateNode from '../cellCreation/hooks/useUpdateNode';
import useSiteList from '../cellCreation/hooks/useData';
import Loader from '../loader/Loader';
import QueryError from '../errorComponents/QueryError';
import { isEqual } from 'lodash';

interface FormData {
  nodeId: string | null;
  lifecycle: string | LIFE_CYCLE;
  site_name: string;
  roles: { label: string; value: string }[];
  site_id?: number; // Make optional since it's derived from site_name
}

const EditNode: React.FC = () => {
  const { control, handleSubmit, reset, watch } = useForm<FormData>();
  const navigate = useNavigate();
  const location = useLocation();
  const state = location.state as { nodeType: NODE_TYPE; nodeId: string } | null;
  const nodeType = state?.nodeType;
  const nodeId = state?.nodeId || '';
  const heading = `Edit ${nodeType}`;

  // State to track original form values for comparison
  const [originalFormData, setOriginalFormData] = useState<FormData | null>(null);

  // Watch all form values to detect changes
  const watchedValues = watch();

  const { data: siteList = [] } = useSiteList();
  const { isLoading, error, data: nodeData } = useGetNodeDataByNodeId(nodeId);
  const selectedNode: Node | undefined = nodeData?.find((node) => node.node_id === nodeId);

  // Check if form has been modified
  const hasFormChanged = useMemo(() => {
    if (!originalFormData) return false;

    // Compare current form values with original values
    return !isEqual(watchedValues, originalFormData);
  }, [watchedValues, originalFormData]);

  useEffect(() => {
    if (selectedNode) {
      const rolesArray: ROLE_OF_NODE[] = selectedNode.roles as ROLE_OF_NODE[];
      // Convert roles from enum to {label, value} format for use with react-select
      const rolesForSelect = rolesArray.map((roleEnum) => {
        return { label: roleEnum, value: roleEnum };
      });

      const formData: FormData = {
        nodeId: selectedNode.node_id,
        lifecycle: selectedNode.lifecycle,
        site_name: selectedNode.site_name,
        roles: rolesForSelect, // converted roles for form default values
        site_id: selectedNode.site_id,
      };

      // Reset form with initial values
      reset(formData);

      // Store original form data for comparison
      setOriginalFormData(formData);
    }
  }, [selectedNode, reset, setOriginalFormData]);

  const { updateNodeMutation } = useUpdateNode();

  const onSubmit = (data: FormData) => {
    const lifecycle = data.lifecycle as LIFE_CYCLE;
    const rolesToSend = data.roles.map((role) => role.value);
    const siteId = siteList.find((site) => site.name === data.site_name)?.site_id;

    const updateNodeData: UpdateNodeRequest = {
      ...data,
      lifecycle,
      roles: rolesToSend,
      site_id: siteId, // array of strings for roles
    };
    updateNodeMutation({
      node_id: nodeId,
      node: updateNodeData,
    }).then(() => {
      navigate('/cell-overview/nodes');
    });
  };

  if (isLoading) return <Loader />;
  if (error) return <QueryError error={error} />;

  return (
    <Box data-testid="editNode" p={5} bg="white" borderRadius="lg" boxShadow="md">
      <Flex alignItems="center" mb={5}>
        <Icon
          as={ArrowBackIcon}
          w={5}
          h={5}
          mr={4}
          onClick={() => {
            navigate(-1);
          }}
        />
        <Text data-testid="editNode-heading" fontSize="2xl" fontWeight="bold">
          {heading}
        </Text>
      </Flex>
      <form onSubmit={handleSubmit(onSubmit)}>
        <HStack spacing={4} alignItems="baseline">
          <FormControl id="nodeId" mb={4} isRequired>
            <FormLabel>Node Id</FormLabel>
            <Controller
              name="nodeId"
              control={control}
              defaultValue=""
              render={() => <Input isDisabled value={nodeId} />}
            />
          </FormControl>
          <FormControl id="lifeCycle" mb={4} isRequired>
            <FormLabel htmlFor="lifecycle">Life Cycle</FormLabel>
            <Controller
              name="lifecycle"
              control={control}
              render={({ field }) => (
                <Select {...field}>
                  {LifeCycle_OPS.map((item) => (
                    <option key={item} value={item}>
                      {item}
                    </option>
                  ))}
                </Select>
              )}
            />
          </FormControl>
          <FormControl id="roles" mb={4}>
            <FormLabel htmlFor="roles">Roles</FormLabel>
            <Controller
              name="roles"
              control={control}
              render={({ field: { onChange, value } }) => (
                <MultiSelect
                  isMulti
                  data-testid="editNode-roles"
                  options={ROLE_OF_NODE_ARRAY}
                  value={ROLE_OF_NODE_ARRAY.filter((option) => value?.some((role) => role.value === option.value))}
                  getOptionLabel={(option) => option.label}
                  getOptionValue={(option) => option.value}
                  onChange={(selectedOptions) => {
                    // selected options back to the array of objects format
                    const updatedRoles = selectedOptions.map((option) => ({
                      label: option.label,
                      value: option.value,
                    }));
                    onChange(updatedRoles);
                  }}
                />
              )}
            />
          </FormControl>

          <FormControl id="site" mb={4} isRequired>
            <FormLabel htmlFor="site">Site</FormLabel>
            <Controller
              name="site_name"
              control={control}
              render={({ field }) => (
                <Select {...field}>
                  {siteList.map((item, idx) => (
                    <option key={idx} value={item.name}>
                      {item.name}
                    </option>
                  ))}
                </Select>
              )}
            />
          </FormControl>
        </HStack>
        <Stack align="end" my="8">
          <Button colorScheme="teal" type="submit" data-testid="editNode-submit" isDisabled={!hasFormChanged}>
            Update Node
          </Button>
        </Stack>
      </form>
    </Box>
  );
};

export default EditNode;
