export const rolesMultiSelectDarkStyles = {
  menuList: (provided: any) => ({
    ...provided,
    maxHeight: '200px',
    backgroundColor: '#2D3748',
  }),
  option: (provided: any, state: { isFocused: any }) => ({
    ...provided,
    backgroundColor: state.isFocused ? '#99C8FF' : '#2D3748',
    color: state.isFocused ? 'white' : 'white',

    ':hover': {
      backgroundColor: '#99C8FF',
      color: 'black',
    },
  }),
};

export const rolesMultiSelectLightStyles = {
  menuList: (provided: any) => ({
    ...provided,
    maxHeight: '200px',
    backgroundColor: 'ffffff',
  }),
  option: (provided: any, state: { isFocused: any }) => ({
    ...provided,
    backgroundColor: state.isFocused ? '#1966D2' : 'white',
    color: state.isFocused ? 'white' : 'black',

    ':hover': {
      backgroundColor: '#1966D2',
      color: 'white',
    },
  }),
};

export const rolesCustomDarkTheme = (theme: any) => {
  return {
    ...theme,
    background: '#2D3748',
    colors: {
      ...theme.colors,
      primary25: '#2D3748',
      primary: '#2D3748',
    },
  };
};

export const rolesCustomLightTheme = (theme: any) => {
  return {
    ...theme,
    background: 'ffffff',
    colors: {
      ...theme.colors,
      primary25: '#ffffff',
      primary: '#ffffff',
    },
  };
};
