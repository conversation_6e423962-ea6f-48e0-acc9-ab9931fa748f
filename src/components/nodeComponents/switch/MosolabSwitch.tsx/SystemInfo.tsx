import {
  <PERSON>,
  Flex,
  Stack,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>ackD<PERSON><PERSON>,
  CardHeader,
  CardBody,
  Card,
  Center,
  Divider,
} from '@chakra-ui/react';

import { MosolabsSystemInfo } from '../../../../types/orchestrator.types';
import { UnstyledTable } from '../../airspan/utils';
import { TimeElapsed } from '../../server/acp/AcpCard';
import { LedOnIcon } from './Icon';
import { LedOffIcon } from './Icon';

interface CardProps {
  systemInfo: MosolabsSystemInfo | undefined;
  showContent: boolean;
  toggleContent: () => void;
}

export const MosolabSystemInfo = ({ systemInfo, showContent, toggleContent }: CardProps) => {
  if (!systemInfo) return null;

  const tableData = {
    'Model Name': systemInfo?.systemInfoModelName,
    'MAC Address': systemInfo?.systemInfoMACAddress,
    'Serial Number': systemInfo?.systemInfoSerialNumber,
    'Hardware Version': systemInfo?.systemInfoHardwareVersion,
    'Software Version': systemInfo?.systemInfoSoftwareVersion,
    'Sys Name': systemInfo?.systemInfoSysName,
    'Sys Location': systemInfo?.systemInfoSysLocation,
    'Sys Contact': systemInfo?.systemInfoSysContact,
    'Sys Group': systemInfo?.systemInfoSysGroup,
    'Last Boot Time': systemInfo?.systemInfoLastBootTime,
  };

  return (
    <Card>
      <Stack divider={<StackDivider />} spacing="0">
        <CardHeader
          _hover={{
            bg: 'gray.100',
            cursor: 'pointer',
            boxShadow: `0 0 12px gray.100`,
          }}
          transition="background-color 0.2s ease, box-shadow 0.2s ease"
          borderRadius="lg"
          onClick={toggleContent}
          bg="gray.100"
        >
          <Flex width="100%" position="relative" height="20px" alignItems="center">
            <Text fontSize="lg" position="absolute" left="0">
              System Info
            </Text>
            <Text fontSize="lg" position="absolute" left="50%" transform="translateX(-50%)">
              Serial Number: {systemInfo?.systemInfoSerialNumber}
            </Text>
          </Flex>
        </CardHeader>
        {showContent && (
          <CardBody>
            <UnstyledTable tableData={tableData} firstColWidth="40%" />
          </CardBody>
        )}
      </Stack>
    </Card>
  );
};

export const LED = ({ systemInfo, showContent, toggleContent }: CardProps) => {
  if (!systemInfo) return null;

  const ledData = {
    'Power LED': systemInfo?.systemLEDsPowerLEDIsLit === true ? <LedOnIcon /> : <LedOffIcon />,
    'System LED': systemInfo?.systemLEDsSystemLEDIsLit === true ? <LedOnIcon /> : <LedOffIcon />,
    'POE Max LED': systemInfo?.systemLEDsPoeMaxLEDIsLit === true ? <LedOnIcon /> : <LedOffIcon />,
    'Fan LED': systemInfo?.systemLEDsFanLEDIsLit === true ? <LedOnIcon /> : <LedOffIcon />,
    'GPS LED': systemInfo?.systemLEDsGPSLEDIsLit === true ? <LedOnIcon /> : <LedOffIcon />,
  };

  return (
    <Card>
      <Stack divider={<StackDivider />} spacing="0">
        <CardHeader
          _hover={{
            bg: 'gray.100',
            cursor: 'pointer',
            boxShadow: `0 0 12px gray.100`,
          }}
          transition="background-color 0.2s ease, box-shadow 0.2s ease"
          borderRadius="lg"
          onClick={toggleContent}
          bg="gray.100"
        >
          <Flex width="100%" position="relative" height="20px" alignItems="center">
            <Text fontSize="lg" position="absolute" left="0">
              LED
            </Text>
            <Flex position="absolute" left="50%" transform="translateX(-50%)">
              <Text fontSize="lg" mt="2">
                Power:{' '}
              </Text>
              <Text>{systemInfo?.systemLEDsPowerLEDIsLit === true ? <LedOnIcon /> : <LedOffIcon />}</Text>
            </Flex>
          </Flex>
        </CardHeader>
        {showContent && (
          <CardBody>
            <Flex wrap="wrap">
              <UnstyledTable tableData={ledData} />
            </Flex>
          </CardBody>
        )}
      </Stack>
    </Card>
  );
};
