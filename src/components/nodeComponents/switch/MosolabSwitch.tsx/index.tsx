import { MosolabsResponse } from '../../../../types/orchestrator.types';
import {
  Card,
  CardBody,
  Stack,
  Text,
  CardHeader,
  Flex,
  Box,
  Button,
  HStack,
  VStack,
  Badge,
  Icon,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  useColorModeValue,
} from '@chakra-ui/react';
import { LED, MosolabSystemInfo } from './SystemInfo';
import { MosolabSystemNtp } from './Ntp';
import { MosolabSystemPtp } from './Ptp';
import { MosolabPort } from './Port';
import { MosolabPoe } from './Poe';
import { DeviceHardwareMonitor } from './Hardware';
import { useState, useMemo } from 'react';

import { TimeElapsed } from '../../server/acp/AcpCard';
import { UnstyledTable } from '../../airspan/utils';
import { Fi<PERSON>lock, FiServer, FiXCircle } from 'react-icons/fi';
import { StatusToColor } from '../../../../data/constants';
import { StaticStatusCircleIcon } from '../../../icons/StatusIcon';

// Device Error Display Component
const DeviceErrorDisplay = ({ errorData }: { errorData: any }) => {
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const errorBg = useColorModeValue('red.50', 'red.900');
  const cardBg = useColorModeValue('white', 'gray.800');

  const statusColor = StatusToColor[errorData.status as keyof typeof StatusToColor] || 'red';

  return (
    <VStack spacing={6} align="stretch">
      {/* Error Alert */}
      <Alert status="error" borderRadius="lg" bg={errorBg}>
        <AlertIcon as={FiXCircle} color="red" boxSize={25} />
        <Box>
          <AlertTitle fontSize="lg" fontWeight="bold">
            {errorData.system.name} Error
          </AlertTitle>
          <AlertDescription fontSize="md">{errorData.system.description}</AlertDescription>
        </Box>
      </Alert>

      {/* Device Information and Timing Information - Side by Side */}
      <HStack spacing={4} align="stretch">
        {/* Device Information */}
        <Box bg={cardBg} p={6} borderRadius="lg" border="1px solid" borderColor={borderColor} width="50%">
          <HStack spacing={3} mb={4}>
            <Icon as={FiServer} color="blue.500" boxSize={5} />
            <Text fontSize="lg" fontWeight="bold">
              Device Information
            </Text>
          </HStack>

          <UnstyledTable
            tableData={{
              'Component ID': errorData.component_id,
              'Node Type': errorData.node_type,
              'IP Address': errorData.ip_address,
              Monitored: errorData.monitored === 'Y' ? true : false,
              Status: (
                <Badge colorScheme={statusColor} variant="solid" px={3} py={1}>
                  {errorData.status}
                </Badge>
              ),
            }}
            useKeyColon={true}
          />
        </Box>

        {/* Timing Information */}
        <Box bg={cardBg} p={6} borderRadius="lg" border="1px solid" borderColor={borderColor} width="50%">
          <HStack spacing={3} mb={4}>
            <Icon as={FiClock} color="purple.500" boxSize={5} />
            <Text fontSize="lg" fontWeight="bold">
              Timing Information
            </Text>
          </HStack>

          <UnstyledTable
            tableData={{
              'Status Changed': (
                <HStack spacing={2}>
                  <TimeElapsed initialUptime={errorData.status_change_time} />
                  <Text color="gray.500">ago</Text>
                </HStack>
              ),
              'Last Success': (
                <HStack spacing={2}>
                  <TimeElapsed initialUptime={errorData.last_success_time} />
                  <Text color="gray.500">ago</Text>
                </HStack>
              ),
              'Last Query': (
                <HStack spacing={2}>
                  <TimeElapsed initialUptime={errorData.last_query_time} />
                  <Text color="gray.500">ago</Text>
                </HStack>
              ),
            }}
            useKeyColon={true}
          />
        </Box>
      </HStack>
    </VStack>
  );
};

// Custom useMap hook implementation
function useMap<K, V>(initialMap = new Map<K, V>()) {
  const [map, setMap] = useState(initialMap);

  const actions = useMemo(
    () => ({
      set: (key: K, value: V) => {
        setMap((prev) => {
          const newMap = new Map(prev);
          newMap.set(key, value);
          return newMap;
        });
      },
      delete: (key: K) => {
        setMap((prev) => {
          const newMap = new Map(prev);
          newMap.delete(key);
          return newMap;
        });
      },
      clear: () => {
        setMap(new Map<K, V>());
      },
      setAll: (entries: [K, V][]) => {
        setMap(new Map(entries));
      },
    }),
    []
  );

  return [map, actions] as const;
}

interface MosolabsSwitchProps {
  data?: MosolabsResponse;
  id?: string | number;
  marginSpace?: string | number;
  caller?: string;
  nodeType?: string;
}

// Card keys type for type safety
type CardKey = 'systemInfo' | 'led' | 'poe' | 'ptp' | 'ntp' | 'hardware';

const MosolabsSwitch: React.FC<MosolabsSwitchProps> = ({ data, marginSpace, caller }) => {
  const systemInfo = data?.system?.info;
  const poe = data?.system?.poe;
  const ntp = data?.system?.ntp;
  const ptp = data?.system?.ptp;
  const ports = data?.ports;

  // Initial card states
  const initialCardStates: [CardKey, boolean][] = [
    ['systemInfo', true],
    ['led', true],
    ['poe', true],
    ['ptp', true],
    ['ntp', true],
    ['hardware', true],
  ];

  // Centralized state using useMap
  const [expandedCards, cardActions] = useMap<CardKey, boolean>(new Map(initialCardStates));

  // Toggle a specific card's expanded state
  const toggleCard = (cardName: CardKey) => {
    cardActions.set(cardName, !expandedCards.get(cardName));
  };

  // Expand or collapse all cards
  const expandAll = () => {
    initialCardStates.forEach(([key]) => {
      cardActions.set(key, true);
    });
  };

  const collapseAll = () => {
    initialCardStates.forEach(([key]) => {
      cardActions.set(key, false);
    });
  };

  // Check if all cards are expanded or collapsed
  const allExpanded = Array.from(expandedCards.values()).every((value) => value);
  const allCollapsed = Array.from(expandedCards.values()).every((value) => !value);

  return data?.status === 'OK' ? (
    <Card width="100%" marginRight={marginSpace} borderRadius="lg">
      <CardHeader>
        <Flex justifyContent="space-between" alignItems="center">
          <Text fontSize="x-large" my="4">
            Mosolabs Switch
          </Text>
          <HStack spacing="2">
            <Button size="sm" colorScheme={allExpanded ? 'teal' : 'gray'} onClick={expandAll}>
              Expand All
            </Button>
            <Button size="sm" colorScheme={allCollapsed ? 'teal' : 'gray'} onClick={collapseAll}>
              Collapse All
            </Button>
          </HStack>
        </Flex>
      </CardHeader>
      <CardBody borderTop="1px solid #e2e2e2">
        <Stack spacing="4" width="100%">
          <Flex flexDirection="column" gap="4" width="100%">
            <Flex flexDirection="row" gap="4" flexWrap="wrap" width="100%">
              <Box flex="1" height="fit-content">
                {systemInfo && (
                  <MosolabSystemInfo
                    systemInfo={systemInfo}
                    showContent={expandedCards.get('systemInfo') ?? true}
                    toggleContent={() => toggleCard('systemInfo')}
                  />
                )}
              </Box>
              <Box flex="1" maxWidth="20%" height="fit-content">
                {systemInfo && (
                  <LED
                    systemInfo={systemInfo}
                    showContent={expandedCards.get('led') ?? true}
                    toggleContent={() => toggleCard('led')}
                  />
                )}
              </Box>
              <Box flex="1" height="fit-content">
                {poe && (
                  <MosolabPoe
                    poe={poe}
                    showContent={expandedCards.get('poe') ?? true}
                    toggleContent={() => toggleCard('poe')}
                  />
                )}
              </Box>
            </Flex>
            <Flex flexDirection="row" gap="4" flexWrap="wrap" width="100%" alignItems="flex-start">
              <Box flex="1" maxWidth="31%" height="fit-content">
                {ptp && (
                  <MosolabSystemPtp
                    ptp={ptp}
                    showContent={expandedCards.get('ptp') ?? true}
                    toggleContent={() => toggleCard('ptp')}
                  />
                )}
              </Box>
              <Box flex="1" maxWidth="25%" height="fit-content">
                {ntp && (
                  <MosolabSystemNtp
                    ntp={ntp}
                    showContent={expandedCards.get('ntp') ?? true}
                    toggleContent={() => toggleCard('ntp')}
                  />
                )}
              </Box>
              <Box flex="1" maxWidth="44%" height="fit-content">
                {systemInfo && (
                  <DeviceHardwareMonitor
                    systemInfo={systemInfo}
                    showContent={expandedCards.get('hardware') ?? true}
                    toggleContent={() => toggleCard('hardware')}
                  />
                )}
              </Box>
            </Flex>
          </Flex>
          <Box width="100%" mt="0">
            {ports && <MosolabPort ports={ports} />}
          </Box>
        </Stack>
      </CardBody>
    </Card>
  ) : (
    <Card width="100%" marginRight={marginSpace} borderRadius="lg">
      <CardHeader>
        <Flex justifyContent="space-between" alignItems="center">
          <Text fontSize="x-large" my="4">
            Mosolabs Switch
          </Text>
          <StaticStatusCircleIcon color={StatusToColor[data?.status as keyof typeof StatusToColor]} size={40} />
        </Flex>
      </CardHeader>
      <CardBody borderTop="1px solid #e2e2e2">
        <DeviceErrorDisplay errorData={data} />
      </CardBody>
    </Card>
  );
};

export default MosolabsSwitch;
