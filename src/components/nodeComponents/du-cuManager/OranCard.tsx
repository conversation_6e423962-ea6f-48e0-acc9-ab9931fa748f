import { ChevronDownIcon } from '@chakra-ui/icons';
import {
  AlertDialog,
  AlertDialogBody,
  AlertDialogContent,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogOverlay,
  Badge,
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  Icon,
  IconButton,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  Stack,
  StackDivider,
  Text,
  useColorModeValue,
  useDisclosure,
  useTheme,
} from '@chakra-ui/react';
import { isEmpty } from 'lodash';
import { useCallback, useEffect, useRef, useState } from 'react';
import { FaInfoCircle } from 'react-icons/fa';
import { FiLock, FiUnlock, FiCheckCircle, FiXCircle } from 'react-icons/fi';
import {
  AUTH_TOKEN_KEY,
  READ_ONLY_ACCESS_ROLES,
  ROLE_OF_NODE,
  TRAFFIC_OK_GREEN,
  TRAFFIC_WARNING_ORANGE,
} from '../../../data/constants';
import useLogin from '../../../hooks/useLogin';
import { ActionPhase } from '../../../pages/CellOverview/hooks/services/use_Orc_ServerTasks';
import { usePostActionOranDuApiCall } from '../../../pages/CellOverview/hooks/services/use_Radysis_OranDuTasks';
import ModalSubComponentRenderer from '../../../pages/OranDuCuManager/hooks/ModalSubComponentRenderer';
import useCustomResource from '../../../pages/OranDuCuManager/hooks/services/useCustomResource';
import { formatInReadableTimeDate } from '../../../utils/formatInReadableTimeData';
import { recursiveSearch } from '../../../utils/recursiveSearch';
import Loader from '../../loader/Loader';
import ComponentModal from '../ComponentModal';
import { RenderDetails } from '../server/cluster/SubClusterCard';
import { getIconForAction, getIconForActionButton } from './actionIcons';
import { actionButtonColor, OranRuActionType } from './OranRuActionTypes';
import { formatActionText } from './RadioCard';
import useDeploymentFinder from './useDeploymentFinder';

type UsePostActionParams = {
  local_node_id: string;
  selectedOption: ActionPhase | '';
  actionType: OranRuActionType | '';
  caller: string;
  deploymentInfo: any;
  nodeType: string;
  setTriggerGetAction: (value: boolean) => void;
  setPostActionData: (value: any) => void;
};

const usePostAction = ({
  local_node_id,
  selectedOption,
  actionType,
  caller,
  deploymentInfo,
  nodeType,
  setTriggerGetAction,
  setPostActionData,
}: UsePostActionParams) => {
  const { triggerPostAction, isMutating } = usePostActionOranDuApiCall(
    deploymentInfo?.deploymentName || '',
    nodeType,
    actionType,
    setTriggerGetAction,
    setPostActionData
  );

  const isDeploymentReady = deploymentInfo?.deploymentName && deploymentInfo?.siteName;

  const initiatePostAction = useCallback(() => {
    if (selectedOption && actionType && isDeploymentReady) {
      triggerPostAction();
    }
  }, [selectedOption, actionType, isDeploymentReady, triggerPostAction]);

  return { initiatePostAction, isMutating };
};

const ActionButton = ({
  actionType,
  onClick,
  isDisabled,
}: {
  actionType: OranRuActionType;
  onClick: () => void;
  isDisabled: boolean;
}) => {
  const buttonIcon = actionType && getIconForActionButton(actionType);
  const buttonColor = actionButtonColor[actionType];
  return (
    <Button
      bg={buttonColor}
      color="white"
      variant="solid"
      size="sm"
      mr="2"
      onClick={onClick}
      _hover={{ bg: buttonColor, opacity: 0.9 }}
      _disabled={{ bg: buttonColor, opacity: 0.3, cursor: 'not-allowed' }}
      _focus={{ boxShadow: 'none' }}
      isDisabled={isDisabled}
    >
      <Text>{formatActionText(buttonIcon || '')}</Text>
      <Box ml="1">{buttonIcon ? getIconForAction(buttonIcon) : null}</Box>
    </Button>
  );
};

const LockStatusIcon = ({ lockStatus }: { lockStatus: 'Locked' | 'Unlocked' }) => {
  return lockStatus === 'Locked' ? <FiLock size={30} color="red" /> : <FiUnlock size={30} color="green" />;
};

const OranCard = ({
  nodeOpen,
  queryNodeId,
  type,
  data,
  isLoading,
  error,
}: {
  nodeOpen: boolean;
  queryNodeId: string;
  type: string;
  data: any;
  isLoading: boolean;
  error: any;
}) => {
  const { pod, id } = data || {};
  const { status, detail, crs } = pod || {};
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isCrOpen, onOpen: onCrOpen, onClose: onCrClose } = useDisclosure();
  const {
    isOpen: isSelectReasonAlertOpen,
    onOpen: onSelectReasonAlertOpen,
    onClose: onSelectReasonAlertClose,
  } = useDisclosure();
  const [actionType, setActionType] = useState<OranRuActionType | ''>('');
  const [selectedOption, setSelectedOption] = useState<ActionPhase | ''>('');
  const [triggerGetAction, setTriggerGetAction] = useState<boolean>(false);
  const [postActionData, setPostActionData] = useState<any[] | null>(null);
  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const isReadOnlyAccess = checkApplicationAccess(READ_ONLY_ACCESS_ROLES);
  const cancelRef = useRef<HTMLButtonElement>(null);
  const colorModeValue = useColorModeValue('sm', 'sm-dark');
  const flattenedData: any = recursiveSearch(detail);
  const local_node_id = id;
  const nodeType = type;
  const caller = 'CellViewOranCard';
  const clockStatus = crs?.DuCellConfig?.content?.spec?.NRDUCell[0]?.administrativeState;

  // API calls
  const { getAllDeployments } = useCustomResource();

  //Get all Deployments
  const {
    data: allDeployments,
    isLoading: isAllDeploymentsLoading,
    error: allDeploymentsError,
  } = getAllDeployments('edit');

  const deploymentInfo = useDeploymentFinder(crs, allDeployments, nodeType);

  //API
  const { initiatePostAction, isMutating } = usePostAction({
    local_node_id,
    selectedOption,
    actionType,
    caller,
    deploymentInfo: isAllDeploymentsLoading ? null : deploymentInfo,
    nodeType,
    setTriggerGetAction,
    setPostActionData,
  });

  const conditionsDataStructure = {
    alarms: [],
    name: id,
    nodes: [{ conditions: detail?.conditions, containers: [], node: '', status: status, storage: {} }],
    ptpState: {},
    status: status,
  };

  const handleActionButtonClick = (actionType: OranRuActionType) => {
    onSelectReasonAlertOpen();
    setActionType(actionType);
  };

  const handleSelect = (value: string) => {
    if (Object.values(ActionPhase).includes(value as ActionPhase)) {
      setSelectedOption((prevState) => (prevState === value ? '' : (value as ActionPhase)));
    }
  };

  const handleConfirmClick = () => {
    onSelectReasonAlertClose();
    initiatePostAction();
  };

  const handleCancelClick = () => {
    onSelectReasonAlertClose();
  };

  if (isLoading) {
    return <Loader />;
  }

  if (error) {
    return <div>Error loading data</div>;
  }

  if (!data) {
    return <div>No data available</div>;
  }

  return (
    <>
      <Card width="100%" marginRight="4" borderRadius="lg">
        <CardHeader>
          <Box display="flex" justifyContent="space-between" mb="2">
            <Flex alignItems="center">
              <Text fontSize="x-large" mr="2">
                {type}
              </Text>
              {nodeType === ROLE_OF_NODE.DU && <LockStatusIcon lockStatus={clockStatus} />}
            </Flex>
            {/* NOTE: handle no crs and deployments */}
            <Box>
              <ActionButton
                actionType={OranRuActionType.RESTART}
                onClick={() => handleActionButtonClick(OranRuActionType.RESTART)}
                isDisabled={
                  isMutating || isReadOnlyAccess || !crs || isEmpty(crs) || !allDeployments || isEmpty(allDeployments)
                }
              />
              {nodeType === ROLE_OF_NODE.DU && (
                <>
                  <ActionButton
                    actionType={OranRuActionType.LOCK}
                    onClick={() => handleActionButtonClick(OranRuActionType.LOCK)}
                    isDisabled={
                      isMutating ||
                      isReadOnlyAccess ||
                      clockStatus === 'Locked' ||
                      !crs ||
                      isEmpty(crs) ||
                      !allDeployments ||
                      isEmpty(allDeployments)
                    }
                  />
                  <ActionButton
                    actionType={OranRuActionType.UNLOCK}
                    onClick={() => handleActionButtonClick(OranRuActionType.UNLOCK)}
                    isDisabled={
                      isMutating ||
                      isReadOnlyAccess ||
                      clockStatus === 'Unlocked' ||
                      !crs ||
                      isEmpty(crs) ||
                      !allDeployments ||
                      isEmpty(allDeployments)
                    }
                  />
                </>
              )}
            </Box>
          </Box>
        </CardHeader>
        <CardBody borderTop="1px solid #e2e2e2">
          <Stack spacing="4">
            <Stack divider={<StackDivider />} spacing="4">
              <Flex justifyContent="center">
                {/* NOTE: handle no crs and deployments */}
                {isEmpty(allDeployments) ||
                  (isEmpty(crs) && (
                    <Flex alignItems="center">
                      <Text color={TRAFFIC_WARNING_ORANGE} fontSize="large" fontWeight="bold">
                        {type} not managed by NMS so actions are unavailable.
                      </Text>
                    </Flex>
                  ))}
              </Flex>
              <Flex>
                <Text pt="2" fontSize="sm" mr="4">
                  Host ip:{' '}
                  <Badge pl="2" pr="2">
                    {detail?.host_ip}
                  </Badge>
                </Text>
                <Text pt="2" fontSize="sm">
                  Pod ip:{' '}
                  <Badge pl="2" pr="2">
                    {detail?.pod_ip}
                  </Badge>
                </Text>
              </Flex>

              <Flex>
                <Text pt="2" fontSize="sm" mr="4">
                  Phase:{' '}
                  <Badge pl="2" pr="2">
                    {detail?.phase}
                  </Badge>
                </Text>
                <Text pt="2" fontSize="sm" mr="4">
                  QOS class :{' '}
                  <Badge pl="2" pr="2">
                    {detail?.qos_class}
                  </Badge>
                </Text>
              </Flex>

              <Box>
                <Heading size="xs" textTransform="uppercase">
                  Container status
                </Heading>

                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    Image:{' '}
                    <Badge pl="2" pr="2">
                      {detail?.container_statuses[0]?.image}
                    </Badge>
                  </Text>
                </Box>
                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    name:{' '}
                    <Badge pl="2" pr="2">
                      {detail?.container_statuses[0]?.name}
                    </Badge>
                  </Text>
                </Box>
                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    ready:
                    <Badge pl="2" pr="2" background="transparent">
                      {detail?.container_statuses[0]?.ready ? (
                        <FiCheckCircle size="25" color="green" />
                      ) : (
                        <FiXCircle size="25" color="red" />
                      )}
                    </Badge>
                  </Text>
                </Box>
                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    Started:{' '}
                    <Badge pl="2" pr="2" background="transparent">
                      {detail?.container_statuses[0]?.started ? (
                        <FiCheckCircle size="25" color="green" />
                      ) : (
                        <FiXCircle size="25" color="red" />
                      )}
                    </Badge>
                  </Text>
                </Box>
                <Box display="flex" flexWrap="wrap">
                  <Text pt="2" pl="2" fontSize="sm">
                    Started at:
                    <Badge pl="2" pr="2">
                      {detail?.container_statuses?.[0]?.state?.running?.started_at
                        ? formatInReadableTimeDate(detail?.container_statuses?.[0]?.state?.running?.started_at)
                        : 'N/A'}
                    </Badge>
                  </Text>
                </Box>
              </Box>
              <Box
                p="6"
                m="4"
                boxShadow={{
                  base: 'none',
                  md: colorModeValue,
                }}
              >
                <RenderDetails
                  type={type}
                  caller="pod"
                  heading="Conditions"
                  details={conditionsDataStructure.nodes[0].conditions}
                />
              </Box>

              <Button onClick={onOpen}>Pod details Data</Button>
              <Button onClick={onCrOpen}>Custom resource Data</Button>
            </Stack>
          </Stack>
        </CardBody>
      </Card>

      {/* Modal flattened data */}
      <ComponentModal isOpen={isOpen} onClose={onClose} flattenedData={flattenedData} />
      {/* Modal custom resource */}
      <Modal isOpen={isCrOpen} onClose={onCrClose} size="5xl" isCentered>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            <Heading as="h2" textAlign="center" mb="8">
              {detail?.container_statuses[0].name} custom resource
            </Heading>
            <Text fontSize="sm" pb="4">{`press ctrl+f(win) / cmd+f(mac) to search`}</Text>
            <Text fontSize="sm">{`click on the menu icon(3 dot upper right) > find & edit > find`}</Text>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {/* NOTE: handle no crs and deployments */}
            {crs && !isEmpty(crs) ? (
              <ModalSubComponentRenderer data={crs} />
            ) : (
              <Text textAlign="center" fontSize="lg" color="gray.500">
                No data available for this custom resource.
              </Text>
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
      {/* Action button dialogue */}
      <AlertDialog
        isOpen={isSelectReasonAlertOpen}
        onClose={onSelectReasonAlertClose}
        isCentered
        leastDestructiveRef={cancelRef}
        size="lg"
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              <Flex flexDirection="row">Select a reason for the {actionType} action</Flex>
            </AlertDialogHeader>
            <AlertDialogBody>
              <Menu>
                <MenuButton
                  as={Button}
                  rightIcon={<Icon as={ChevronDownIcon} />}
                  variant="outline"
                  colorScheme={actionButtonColor[actionType as keyof typeof actionButtonColor]}
                  size="sm"
                >
                  {selectedOption || 'Click to select an option'}
                </MenuButton>
                <MenuList>
                  {Object.entries(ActionPhase).map(([key, value]) => (
                    <MenuItem
                      key={key}
                      onClick={() => handleSelect(value)}
                      _hover={{
                        backgroundColor:
                          actionType && actionButtonColor[actionType] ? actionButtonColor[actionType] : 'gray.500',
                        color: 'white',
                      }}
                    >
                      {value}
                    </MenuItem>
                  ))}
                </MenuList>
              </Menu>
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button onClick={handleCancelClick} colorScheme="blue" ref={cancelRef}>
                Cancel
              </Button>
              <Button
                bg={actionType && actionButtonColor[actionType] ? actionButtonColor[actionType] : 'gray.500'}
                color="white"
                _hover={{
                  bg: actionType && actionButtonColor[actionType] ? actionButtonColor[actionType] : 'gray.500',
                  opacity: 0.9,
                }}
                onClick={handleConfirmClick}
                isDisabled={isEmpty(selectedOption)}
                _disabled={{
                  bg: actionType && actionButtonColor[actionType] ? actionButtonColor[actionType] : 'gray.500',
                  opacity: 0.3,
                  cursor: 'not-allowed',
                }}
                ml={3}
              >
                Confirm
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </>
  );
};

export default OranCard;
