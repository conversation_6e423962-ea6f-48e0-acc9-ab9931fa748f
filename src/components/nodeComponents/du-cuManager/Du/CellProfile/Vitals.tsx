import {
  Card,
  CardBody,
  CardHeader,
  HStack,
  Heading,
  Icon,
  Text,
  VStack,
  Box,
  Badge,
  SimpleGrid,
  Divider,
  Collapse,
  Progress,
  Alert,
  AlertIcon,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
} from '@chakra-ui/react';
import { useState } from 'react';
import {
  FiActivity,
  FiCheckCircle,
  FiXCircle,
  FiRss,
  FiCornerRightUp,
  FiSettings,
  FiBarChart,
  FiShare2,
  FiGitCommit,
  FiMaximize,
  FiShuffle,
  FiCpu,
  FiWifi,
  FiTarget,
  FiPower,
  FiLayers,
  FiTrendingUp,
  FiShield,
  FiRadio,
  FiClock,
  FiAlertTriangle,
} from 'react-icons/fi';
import CommonConfigurationStatus from '../CommonConfigurationStatus';
import CommonMetricsGrid from '../CommonMetricsComponent';

// Enhanced Metric Card Component
const EnhancedMetricCard = ({
  icon,
  label,
  value,
  unit = '',
  colorScheme,
  helpText,
  progress,
  status,
}: {
  icon: any;
  label: string;
  value: string | number;
  unit?: string;
  colorScheme: string;
  helpText?: string;
  progress?: number;
  status?: 'good' | 'warning' | 'critical';
}) => {
  const statusColor =
    status === 'good' ? 'green' : status === 'warning' ? 'orange' : status === 'critical' ? 'red' : colorScheme;

  return (
    <Stat
      p={4}
      bg={`${statusColor}.50`}
      borderRadius="lg"
      borderWidth="1px"
      borderColor={`${statusColor}.200`}
      textAlign="center"
      position="relative"
    >
      <HStack justify="center" mb={2}>
        <Icon as={icon} color={`${statusColor}.500`} boxSize={5} />
        <StatLabel fontSize="sm" color={`${statusColor}.600`} fontWeight="medium">
          {label}
        </StatLabel>
      </HStack>
      <StatNumber fontSize="2xl" color={`${statusColor}.800`} fontFamily="mono">
        {value}
        {unit}
      </StatNumber>
      {helpText && (
        <StatHelpText fontSize="xs" color={`${statusColor}.600`} mt={1}>
          {helpText}
        </StatHelpText>
      )}
      {progress !== undefined && (
        <Progress value={progress} colorScheme={statusColor} size="sm" mt={2} borderRadius="full" />
      )}
      {status && (
        <Icon
          as={status === 'good' ? FiCheckCircle : status === 'warning' ? FiAlertTriangle : FiXCircle}
          position="absolute"
          top={2}
          right={2}
          color={`${statusColor}.500`}
          boxSize={4}
        />
      )}
    </Stat>
  );
};

const CellProfileVitals = ({ profile, colorScheme }: { profile: any; colorScheme: string }) => {
  const [showContent, setShowContent] = useState(true);
  const mac = profile?.mac || {};
  const tddSlot = profile?.tddSlot || {};
  const pfs = profile?.pfs || {};

  return (
    <Card variant="outline" shadow="md" borderColor={`${colorScheme}.200`} overflow="hidden">
      <CardHeader
        py={3}
        cursor="pointer"
        onClick={() => setShowContent(!showContent)}
        bg={showContent ? `${colorScheme}.50` : 'transparent'}
        _hover={{ bg: `${colorScheme}.100` }}
        transition="all 0.2s ease-in-out"
      >
        <HStack justify="space-between" align="center">
          <HStack spacing={3}>
            <Icon as={FiActivity} boxSize={6} color={`${colorScheme}.600`} />
            <Box>
              <Heading size="md" color={`${colorScheme}.700`}>
                Cell Profile Vitals
              </Heading>
              <Text fontSize="sm" fontWeight="bold" color={`${colorScheme}.600`}>
                An overview of runtime status and performance
              </Text>
            </Box>
          </HStack>
        </HStack>
      </CardHeader>
      <Collapse in={showContent} animateOpacity>
        <CardBody py={4}>
          <VStack spacing={6} align="stretch">
            {/* Configuration Status */}
            <CommonConfigurationStatus
              title="Cell Profile Configuration Status"
              colorScheme={colorScheme}
              columns={{ base: 1, md: 2, lg: 3 }}
              items={[
                {
                  label: 'IRC Enabled',
                  value: profile?.ircEnabled,
                  icon: FiRss,
                },
                {
                  label: 'Start RNTI Present',
                  value: profile?.isStartRntiPres,
                  icon: FiCornerRightUp,
                },
                {
                  label: 'PDSCH Rate Matching (CORESET)',
                  value: profile?.enablePdschRateMatchCoreset,
                  icon: FiSettings,
                },
                {
                  label: 'PDSCH Rate Matching (CSI-RS)',
                  value: profile?.enablePdschRateMatchCsiRs,
                  icon: FiBarChart,
                },
                {
                  label: 'DL Pre-Coder Enabled',
                  value: profile?.nDlPreCoderEnable,
                  icon: FiShare2,
                },
                {
                  label: 'Rate Matching (SSB/PBCH)',
                  value: profile?.enableRateMatchSsbPbch,
                  icon: FiGitCommit,
                },
                {
                  label: 'DCI Format 0_1 Enabled',
                  value: mac.enableDciFormat0_1,
                  icon: FiMaximize,
                },
                {
                  label: 'DL PRB Randomization',
                  value: mac.enableDlPrbRandomization,
                  icon: FiShuffle,
                },
              ]}
            />

            <Divider borderColor={`${colorScheme}.200`} />

            {/* Performance Metrics */}
            <CommonMetricsGrid
              title="MAC Performance Metrics"
              colorScheme={colorScheme}
              columns={{ base: 2, md: 3, lg: 4 }}
              metrics={{
                dlBlerTolerance: `${mac.dlBlerTolerance}%`,
                ulBlerTolerance: `${mac.ulBlerTolerance}%`,
                maxDlHqTx: mac.maxDlHqTx,
                maxUlHqTx: mac.maxUlHqTx,
                dlRank: mac.dlRank,
                ulRank: mac.ulRank,
                minPathlossTh: `${mac.minPathlossTh} dB`,
                maxPathlossTh: `${mac.maxPathlossTh} dB`,
                cellCentreUeThresh: mac.cellCentreUePlThreshold,
                cellEdgeUeThresh: mac.cellEdgeUePlThreshold,
                maxDlUePerTTI: mac.maxDlUePerTTI,
                maxUlUePerTTI: mac.maxUlUePerTTI,
                initialDlMcs: mac.initialDlMcs,
                initialUlMcs: mac.initialUlMcs,
                maxDlMcs: mac.maxDlMcs,
                maxUlMcs: mac.maxUlMcs,
              }}
            />

            <Divider borderColor={`${colorScheme}.200`} />

            {/* TDD & PFS Metrics */}
            <CommonMetricsGrid
              title="TDD Slot & PFS Configuration"
              colorScheme={colorScheme}
              columns={{ base: 2, md: 3, lg: 4 }}
              metrics={{
                dlSlots: tddSlot.numDlSlot,
                ulSlots: tddSlot.numUlSlot,
                dlSymbols: tddSlot.numDlSymbol,
                ulSymbols: tddSlot.numUlSymbol,
                fairnessCoeff: pfs.fairnessCoeff,
                tptCoeff: pfs.tptCoEff,
                pdbCoeff: pfs.pdbCoeff,
                gbrCoeff: pfs.gbrServedRateCoeff,
              }}
            />
          </VStack>
        </CardBody>
      </Collapse>
    </Card>
  );
};

export default CellProfileVitals;
