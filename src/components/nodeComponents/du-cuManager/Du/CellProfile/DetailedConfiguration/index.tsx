import {
  Accordion,
  <PERSON>,
  <PERSON>Header,
  CardBody,
  HStack,
  Icon,
  Heading,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Box,
  Text,
  Badge,
  Button,
  Flex,
} from '@chakra-ui/react';
import { useState } from 'react';
import { FiSettings, FiCpu, FiLink, FiDatabase, FiRadio, FiSliders, FiLayers, FiChevronUp } from 'react-icons/fi';

import { LinkConfiguration } from './LinkConfiguration';
import { MACConfiguration } from './MACConfiguration';
import { CSIConfiguration } from './CSIConfiguration';
import { DRXConfiguration } from './DRXConfiguration';
import { BeamMappingConfiguration } from './BeamMappingConfiguration';
import { AdvancedConfiguration } from './AdvancedConfiguration';
import { RLCConfiguration } from './RLCConfiguration';
import { SystemConfiguration } from './SystemConfiguration';

// Centralized accordion configuration
const accordionConfig = [
  {
    id: 'link',
    title: 'Link Configuration',
    icon: FiLink,
    dataKey: 'link',
    component: LinkConfiguration,
    colorScheme: 'blue',
    badge: (spec: Record<string, unknown>) => {
      const dl = spec.dl as Record<string, unknown>;
      const ul = spec.ul as Record<string, unknown>;
      return dl && ul ? '2 links' : null;
    },
  },
  {
    id: 'mac',
    title: 'MAC Configuration',
    icon: FiCpu,
    dataKey: 'mac',
    component: MACConfiguration,
    colorScheme: 'purple',
    badge: (spec: Record<string, unknown>) => {
      const mac = spec.mac as Record<string, unknown>;
      return mac ? 'configured' : null;
    },
  },
  {
    id: 'csi',
    title: 'CSI Configuration',
    icon: FiSettings,
    dataKey: 'csi',
    component: CSIConfiguration,
    colorScheme: 'blue',
    badge: (spec: Record<string, unknown>) => {
      const csi = spec.csi as Record<string, unknown>;
      return csi ? 'active' : null;
    },
  },
  {
    id: 'drx',
    title: 'DRX Configuration',
    icon: FiDatabase,
    dataKey: 'DrxData',
    component: DRXConfiguration,
    colorScheme: 'cyan',
    badge: (spec: Record<string, unknown>) => {
      const drx = spec.DrxData as Record<string, unknown>;
      return drx ? 'enabled' : null;
    },
  },
  {
    id: 'beamMapping',
    title: 'Beam ID Mapping',
    icon: FiRadio,
    dataKey: 'beamIdMapping',
    component: BeamMappingConfiguration,
    colorScheme: 'orange',
    badge: (spec: Record<string, unknown>) => {
      const beamMapping = spec.beamIdMapping as unknown[];
      return Array.isArray(beamMapping) ? `${beamMapping.length} beams` : null;
    },
  },
  {
    id: 'rlc',
    title: 'RLC Configuration',
    icon: FiLayers,
    dataKey: 'rlc',
    component: RLCConfiguration,
    colorScheme: 'purple',
    badge: (spec: Record<string, unknown>) => {
      const rlc = spec.rlc as Record<string, unknown>;
      return rlc ? 'configured' : null;
    },
  },
  {
    id: 'system',
    title: 'System Configuration',
    icon: FiCpu,
    dataKey: 'system',
    component: SystemConfiguration,
    colorScheme: 'indigo',
    badge: () => null,
  },
  {
    id: 'advanced',
    title: 'Advanced Configuration',
    icon: FiSliders,
    dataKey: 'advanced',
    component: AdvancedConfiguration,
    colorScheme: 'teal',
    badge: () => null,
  },
];

interface CellProfileDetailedConfigurationProps {
  spec: Record<string, unknown>;
  colorScheme: string;
}

const CellProfileDetailedConfiguration = ({ spec, colorScheme }: CellProfileDetailedConfigurationProps) => {
  const [expandedItems, setExpandedItems] = useState<number[]>([]);
  const hasExpandedItems = expandedItems.length > 0;
  const collapseAll = () => {
    setExpandedItems([]);
  };

  return (
    <Card variant="outline" shadow="lg" borderColor={`${colorScheme}.200`}>
      <CardHeader py={6}>
        <Flex justifyContent="space-between" alignItems="center">
          <HStack spacing={3}>
            <Box
              bg={`${colorScheme}.500`}
              bgGradient={`linear(135deg, ${colorScheme}.400, ${colorScheme}.600)`}
              borderRadius="full"
              p={2}
              shadow="md"
            >
              <Icon as={FiSettings} boxSize={7} color="white" />
            </Box>
            <Heading size="md" color={`${colorScheme}.700`}>
              Cell Profile Detailed Configuration
            </Heading>
          </HStack>
          <Button
            size="md"
            colorScheme={colorScheme}
            variant="outline"
            onClick={collapseAll}
            isDisabled={!hasExpandedItems}
            mr="4"
            shadow="lg"
            _hover={{
              shadow: 'md',
              transform: 'translateY(-5px)',
            }}
            _active={{
              shadow: 'md',
              transform: 'translateY(-5px)',
            }}
            _focus={{
              shadow: 'md',
              transform: 'translateY(-5px)',
            }}
            transition="all 0.3s ease-in-out"
          >
            <Icon as={FiChevronUp} boxSize={6} />
            Collapse All
          </Button>
        </Flex>
      </CardHeader>
      <CardBody pt={0}>
        <Accordion
          allowMultiple
          mx="4"
          index={expandedItems}
          onChange={(expandedIndex) => setExpandedItems(expandedIndex as number[])}
        >
          {accordionConfig.map((config) => {
            let data;

            // Handle special cases for multi-data configurations
            if (config.id === 'link') {
              data = {
                dlConfiguration: spec.dl,
                ulConfiguration: spec.ul,
              };
            } else if (config.id === 'advanced') {
              data = {
                l1Info: spec.l1Info,
                ltenrCoTraficPatternType: spec.ltenrCoTraficPatternType,
                measGap: spec.measGap,
                pCCH: spec.pCCH,
                pfs: spec.pfs,
              };
            } else if (config.id === 'system') {
              data = {
                'si-SchedInfo': spec['si-SchedInfo'],
                tddSlot: spec.tddSlot,
                uePositioning: spec.uePositioning,
                xran: spec.xran,
              };
            } else {
              data = spec[config.dataKey];
            }

            return (
              <AccordionItem
                key={config.id}
                mb={3}
                shadow="md"
                _hover={{
                  shadow: 'md',
                  transform: 'translateY(-5px)',
                }}
                transition="all 0.3s ease-in-out"
                borderColor={`${colorScheme}.100`}
                borderWidth="1px"
                borderRadius="lg"
              >
                <h2>
                  <AccordionButton
                    py={4}
                    px={5}
                    shadow="md"
                    borderRadius="lg"
                    _hover={{ bg: `${colorScheme}.100` }}
                    _expanded={{ bg: `${colorScheme}.100` }}
                  >
                    <Box as="span" flex="1" textAlign="left" fontWeight="bold" fontSize="lg">
                      <HStack spacing={3}>
                        <Box
                          bg={`${colorScheme}.500`}
                          bgGradient={`linear(135deg, ${colorScheme}.400, ${colorScheme}.600)`}
                          borderRadius="full"
                          p={2}
                          shadow="md"
                        >
                          <Icon as={config.icon} boxSize={5} color="white" />
                        </Box>
                        <Text>{config.title}</Text>
                        {config.badge(spec) && (
                          <Badge colorScheme={colorScheme} variant="solid" borderRadius="full">
                            {config.badge(spec)}
                          </Badge>
                        )}
                      </HStack>
                    </Box>
                    <AccordionIcon />
                  </AccordionButton>
                </h2>
                <AccordionPanel>
                  <config.component data={data as any} />
                </AccordionPanel>
              </AccordionItem>
            );
          })}
        </Accordion>
      </CardBody>
    </Card>
  );
};

export default CellProfileDetailedConfiguration;
