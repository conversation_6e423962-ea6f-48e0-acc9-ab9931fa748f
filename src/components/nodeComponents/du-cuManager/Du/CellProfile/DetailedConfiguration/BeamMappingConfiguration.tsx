import React from 'react';
import {
  Box,
  Card,
  CardBody,
  HStack,
  Icon,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  Text,
  VStack,
  Badge,
  Alert,
  AlertIcon,
  As,
  Wrap,
  WrapItem,
} from '@chakra-ui/react';
import { FiRadio, FiGrid, FiLayers, FiTarget, FiZap, FiWifi, FiSettings, FiActivity } from 'react-icons/fi';
import { CopyButton } from '../../common';

interface BeamMappingConfigurationProps {
  data: any;
}

const StatBox = ({
  icon,
  label,
  value,
  colorScheme,
  copyable = false,
}: {
  icon: As;
  label: string;
  value: string | number;
  colorScheme: string;
  copyable?: boolean;
}) => (
  <Stat
    p={2}
    bg={`${colorScheme}.50`}
    borderRadius="md"
    textAlign="center"
    borderWidth="1px"
    borderColor={`${colorScheme}.100`}
  >
    <StatLabel fontSize="xs" color={`${colorScheme}.600`} fontWeight="medium">
      <HStack justify="center" spacing={1}>
        <Icon as={icon} boxSize={3} />
        <Text>{label}</Text>
      </HStack>
    </StatLabel>
    <HStack justify="center" spacing={1} mt={0.5}>
      <StatNumber fontSize="lg" color={`${colorScheme}.800`} fontFamily="mono">
        {value}
      </StatNumber>
      {copyable && <CopyButton text={String(value)} label={label} />}
    </HStack>
  </Stat>
);

const BeamIDSequence = ({
  beamIds,
  title,
  icon,
  colorScheme,
}: {
  beamIds: number[];
  title: string;
  icon: As;
  colorScheme: string;
}) => {
  if (!beamIds || beamIds.length === 0) return null;

  const sortedBeamIds = [...beamIds].sort((a, b) => a - b);

  return (
    <HStack
      spacing={2}
      p={2}
      bg={`${colorScheme}.50`}
      borderRadius="md"
      borderWidth="1px"
      borderColor={`${colorScheme}.200`}
      align="flex-start"
    >
      <Icon as={icon} color={`${colorScheme}.500`} boxSize={4} mt={0.5} />
      <Text fontWeight="bold" fontSize="sm" color={`${colorScheme}.700`} minW="120px" flexShrink={0}>
        {title}
      </Text>
      <Box flex={1}>
        <Wrap spacing={1}>
          {sortedBeamIds.map((beamId, index) => (
            <Text key={index} fontSize="sm" color={`${colorScheme}.800`} fontFamily="mono">
              {beamId}
              {index < sortedBeamIds.length - 1 ? ' |' : ''}
            </Text>
          ))}
        </Wrap>
      </Box>
      <Badge size="sm" colorScheme={colorScheme} variant="solid" flexShrink={0}>
        {sortedBeamIds.length}
      </Badge>
    </HStack>
  );
};

const CSIPortConfiguration = ({ data }: { data: any }) => (
  <VStack spacing={1} align="stretch">
    <Text fontSize="sm" fontWeight="bold" color="gray.700" mb={2}>
      CSI Port Configuration Mapping
    </Text>
    <BeamIDSequence beamIds={data.csi2Port?.beamId} title="CSI 2-Port" icon={FiRadio} colorScheme="blue" />
    <BeamIDSequence beamIds={data.csi4Port?.beamId} title="CSI 4-Port" icon={FiRadio} colorScheme="green" />
    <BeamIDSequence beamIds={data.csi8Port?.beamId} title="CSI 8-Port" icon={FiRadio} colorScheme="purple" />
    <BeamIDSequence beamIds={data.csi16Port?.beamId} title="CSI 16-Port" icon={FiRadio} colorScheme="orange" />
    <BeamIDSequence beamIds={data.csi32Port?.beamId} title="CSI 32-Port" icon={FiRadio} colorScheme="cyan" />
  </VStack>
);

const ChannelConfiguration = ({ data }: { data: any }) => (
  <VStack spacing={1} align="stretch">
    <Text fontSize="sm" fontWeight="bold" color="gray.700" mb={2}>
      Channel Configuration Mapping
    </Text>
    <BeamIDSequence beamIds={data.pdcch} title="PDCCH" icon={FiWifi} colorScheme="teal" />
    <BeamIDSequence beamIds={data.pdschNonMmimoMode} title="PDSCH Non-MIMO" icon={FiActivity} colorScheme="pink" />
    <BeamIDSequence beamIds={data.ssb} title="SSB" icon={FiSettings} colorScheme="purple" />
  </VStack>
);

const BeamMappingOverview = ({ data }: { data: any }) => {
  const totalBeams = [
    ...(data.csi2Port?.beamId || []),
    ...(data.csi4Port?.beamId || []),
    ...(data.csi8Port?.beamId || []),
    ...(data.csi16Port?.beamId || []),
    ...(data.csi32Port?.beamId || []),
    ...(data.pdcch || []),
    ...(data.pdschNonMmimoMode || []),
    ...(data.ssb || []),
  ];

  const uniqueBeams = [...new Set(totalBeams)].sort((a, b) => a - b);

  return (
    <VStack spacing={1} align="stretch">
      <Text fontSize="sm" fontWeight="bold" color="gray.700" mb={2}>
        Beam Mapping Overview
      </Text>
      <HStack
        spacing={2}
        p={2}
        bg="purple.50"
        borderRadius="md"
        borderWidth="1px"
        borderColor="purple.200"
        align="flex-start"
      >
        <Icon as={FiRadio} color="purple.500" boxSize={4} mt={0.5} />
        <Text fontWeight="bold" fontSize="sm" color="purple.700" minW="120px" flexShrink={0}>
          All Beam IDs
        </Text>
        <Box flex={1}>
          <Wrap spacing={1}>
            {uniqueBeams.map((beamId, index) => (
              <Text key={index} fontSize="sm" color="purple.800" fontFamily="mono">
                {beamId}
                {index < uniqueBeams.length - 1 ? ' |' : ''}
              </Text>
            ))}
          </Wrap>
        </Box>
        <Badge size="sm" colorScheme="purple" variant="solid" flexShrink={0}>
          {uniqueBeams.length} unique
        </Badge>
      </HStack>
    </VStack>
  );
};

export const BeamMappingConfiguration: React.FC<BeamMappingConfigurationProps> = ({ data }) => {
  if (!data) {
    return (
      <Alert status="info" borderRadius="lg">
        <AlertIcon />
        <Text>No beam mapping configuration data available.</Text>
      </Alert>
    );
  }

  return (
    <Card borderWidth="1px" borderRadius="lg" overflow="hidden" variant="outline" border="none">
      <CardBody px={2} py={0}>
        <VStack spacing={4} align="stretch" p={3}>
          <BeamMappingOverview data={data} />
          <CSIPortConfiguration data={data} />
          <ChannelConfiguration data={data} />
        </VStack>
      </CardBody>
    </Card>
  );
};
