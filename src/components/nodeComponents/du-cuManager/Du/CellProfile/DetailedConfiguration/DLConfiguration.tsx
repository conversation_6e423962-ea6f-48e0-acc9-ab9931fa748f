import React from 'react';
import {
  Box,
  Card,
  CardBody,
  HStack,
  Icon,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
  VStack,
  Badge,
  Alert,
  AlertIcon,
  As,
} from '@chakra-ui/react';
import { FiArrowDown, FiSettings, FiRadio, FiLayers, FiGrid, FiCpu, FiActivity, FiTarget, FiZap } from 'react-icons/fi';
import { CopyButton } from '../../common';

interface DLConfigurationProps {
  data: any;
}

const StatBox = ({
  icon,
  label,
  value,
  colorScheme,
  copyable = false,
}: {
  icon: As;
  label: string;
  value: string | number;
  colorScheme: string;
  copyable?: boolean;
}) => (
  <Stat
    p={3}
    bg={`${colorScheme}.50`}
    borderRadius="md"
    textAlign="center"
    borderWidth="1px"
    borderColor={`${colorScheme}.100`}
  >
    <StatLabel fontSize="sm" color={`${colorScheme}.600`} fontWeight="medium">
      <HStack justify="center" spacing={1.5}>
        <Icon as={icon} boxSize={4} />
        <Text>{label}</Text>
      </HStack>
    </StatLabel>
    <HStack justify="center" spacing={2} mt={1}>
      <StatNumber fontSize="xl" color={`${colorScheme}.800`} fontFamily="mono">
        {value}
      </StatNumber>
      {copyable && <CopyButton text={String(value)} label={label} />}
    </HStack>
  </Stat>
);

const DLCommonConfig = ({ dlCmn }: { dlCmn: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 3 }} spacing={4}>
      <StatBox
        icon={FiCpu}
        label="BWP Inactivity Timer"
        value={dlCmn?.bwpInactivityTimer || 'N/A'}
        colorScheme="blue"
      />
      <StatBox icon={FiGrid} label="Reserved CCE" value={dlCmn?.numCceRsvdForCmnPdcch || 0} colorScheme="green" />
      <StatBox
        icon={FiLayers}
        label="Sub-Carrier Configs"
        value={dlCmn?.subCarrierCfg?.length || 0}
        colorScheme="purple"
      />
    </SimpleGrid>

    {/* BWP Configuration */}
    {dlCmn?.dlBwp && dlCmn.dlBwp.length > 0 && (
      <Box p={4} borderWidth="1px" borderRadius="lg" bg="blue.50" borderColor="blue.200">
        <HStack mb={3}>
          <Icon as={FiSettings} color="blue.500" />
          <Text fontWeight="bold" fontSize="md" color="blue.700">
            Bandwidth Part (BWP) Configuration
          </Text>
        </HStack>
        {dlCmn.dlBwp.map((bwp: any, index: number) => (
          <Box key={index} p={3} bg="white" borderRadius="md" mb={2}>
            <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3} mb={3}>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  BWP ID
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {bwp.id}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  MU
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {bwp.mu}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Number of RBs
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {bwp.numRb}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Cyclic Prefix
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {bwp.cyclicPrefix}
                </Text>
              </Box>
            </SimpleGrid>

            {/* Control Resource Set Search Space Configuration */}
            {bwp.cntrlResourceSetSearchSpaceCfg && (
              <Box p={2} bg="gray.50" borderRadius="md">
                <Text fontSize="xs" fontWeight="bold" color="gray.600" mb={2}>
                  Control Resource Set Search Space Config
                </Text>
                <SimpleGrid columns={{ base: 2, md: 5 }} spacing={2}>
                  <Box>
                    <Text fontSize="xs" color="gray.500">
                      Aggr Lvl 1
                    </Text>
                    <Text fontSize="xs" fontWeight="bold">
                      {bwp.cntrlResourceSetSearchSpaceCfg.aggrLvl1Candidates}
                    </Text>
                  </Box>
                  <Box>
                    <Text fontSize="xs" color="gray.500">
                      Aggr Lvl 2
                    </Text>
                    <Text fontSize="xs" fontWeight="bold">
                      {bwp.cntrlResourceSetSearchSpaceCfg.aggrLvl2Candidates}
                    </Text>
                  </Box>
                  <Box>
                    <Text fontSize="xs" color="gray.500">
                      Aggr Lvl 4
                    </Text>
                    <Text fontSize="xs" fontWeight="bold">
                      {bwp.cntrlResourceSetSearchSpaceCfg.aggrLvl4Candidates}
                    </Text>
                  </Box>
                  <Box>
                    <Text fontSize="xs" color="gray.500">
                      Aggr Lvl 8
                    </Text>
                    <Text fontSize="xs" fontWeight="bold">
                      {bwp.cntrlResourceSetSearchSpaceCfg.aggrLvl8Candidates}
                    </Text>
                  </Box>
                  <Box>
                    <Text fontSize="xs" color="gray.500">
                      Aggr Lvl 16
                    </Text>
                    <Text fontSize="xs" fontWeight="bold">
                      {bwp.cntrlResourceSetSearchSpaceCfg.aggrLvl16Candidates}
                    </Text>
                  </Box>
                </SimpleGrid>

                {bwp.cntrlResourceSetSearchSpaceCfg.type3SearchSpaceCfg && (
                  <Box mt={2} p={2} bg="white" borderRadius="md">
                    <Text fontSize="xs" fontWeight="bold" color="gray.600" mb={1}>
                      Type 3 Search Space Config
                    </Text>
                    <SimpleGrid columns={{ base: 2, md: 3 }} spacing={2}>
                      <Box>
                        <Text fontSize="xs" color="gray.500">
                          Enabled
                        </Text>
                        <Badge
                          colorScheme={
                            bwp.cntrlResourceSetSearchSpaceCfg.type3SearchSpaceCfg.enableType3SearchSpace
                              ? 'green'
                              : 'red'
                          }
                          variant="solid"
                          size="sm"
                        >
                          {bwp.cntrlResourceSetSearchSpaceCfg.type3SearchSpaceCfg.enableType3SearchSpace ? 'Yes' : 'No'}
                        </Badge>
                      </Box>
                      <Box>
                        <Text fontSize="xs" color="gray.500">
                          Avg Aggr Level
                        </Text>
                        <Text fontSize="xs" fontWeight="bold">
                          {bwp.cntrlResourceSetSearchSpaceCfg.type3SearchSpaceCfg.avgAggregationLvl}
                        </Text>
                      </Box>
                      <Box>
                        <Text fontSize="xs" color="gray.500">
                          Configurable
                        </Text>
                        <Badge
                          colorScheme={
                            bwp.cntrlResourceSetSearchSpaceCfg.type3SearchSpaceCfg.isCandidatesPerAggrLvlConfigurable
                              ? 'green'
                              : 'red'
                          }
                          variant="solid"
                          size="sm"
                        >
                          {bwp.cntrlResourceSetSearchSpaceCfg.type3SearchSpaceCfg.isCandidatesPerAggrLvlConfigurable
                            ? 'Yes'
                            : 'No'}
                        </Badge>
                      </Box>
                    </SimpleGrid>
                  </Box>
                )}
              </Box>
            )}
          </Box>
        ))}
      </Box>
    )}

    {/* Sub-Carrier Configuration */}
    {dlCmn?.subCarrierCfg && dlCmn.subCarrierCfg.length > 0 && (
      <Box p={4} borderWidth="1px" borderRadius="lg" bg="purple.50" borderColor="purple.200">
        <HStack mb={3}>
          <Icon as={FiRadio} color="purple.500" />
          <Text fontWeight="bold" fontSize="md" color="purple.700">
            Sub-Carrier Configuration
          </Text>
        </HStack>
        {dlCmn.subCarrierCfg.map((config: any, index: number) => (
          <Box key={index} p={3} bg="white" borderRadius="md" mb={2}>
            <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Config ID
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {config.id}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Carrier BW
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {config.carrierBw}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  SCS
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {config.subCarrierSpacing}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Offset
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {config.offsetToCarrier}
                </Text>
              </Box>
            </SimpleGrid>
          </Box>
        ))}
      </Box>
    )}
  </VStack>
);

const DLCAConfig = ({ dlCA }: { dlCA: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiTarget} label="CA Distribution Type" value={dlCA?.CaBoDistType || 'N/A'} colorScheme="orange" />
      <StatBox
        icon={FiActivity}
        label="SCell Active Threshold"
        value={dlCA?.ScellActiveThr || 'N/A'}
        colorScheme="green"
      />
      <StatBox
        icon={FiZap}
        label="SCell Active BO Threshold"
        value={dlCA?.ScellActiveBoThr || 'N/A'}
        colorScheme="blue"
      />
      <StatBox icon={FiSettings} label="CA Activation Type" value={dlCA?.caActType || 'N/A'} colorScheme="purple" />
    </SimpleGrid>

    <Box p={4} borderWidth="1px" borderRadius="lg" bg="orange.50" borderColor="orange.200">
      <Text fontSize="sm" color="gray.600" mb={2}>
        <strong>SCell Deactivation Threshold:</strong> {dlCA?.ScellDeActiveThr || 'N/A'}
      </Text>
    </Box>
  </VStack>
);

const DLDMRSConfig = ({ dlDmrs }: { dlDmrs: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiRadio} label="DMRS Type" value={dlDmrs?.dmrsTypeDl || 'N/A'} colorScheme="cyan" />
      <StatBox
        icon={FiLayers}
        label="Additional Positions"
        value={dlDmrs?.dmrsAdditionalPosDl || 'N/A'}
        colorScheme="teal"
      />
      <StatBox icon={FiGrid} label="Max Length" value={dlDmrs?.maxLenDl || 'N/A'} colorScheme="blue" />
      <StatBox icon={FiCpu} label="CDM Groups" value={dlDmrs?.numCdmGrpWithoutData || 'N/A'} colorScheme="green" />
    </SimpleGrid>
  </VStack>
);

export const DLConfiguration: React.FC<DLConfigurationProps> = ({ data }) => {
  if (!data) {
    return (
      <Alert status="info" borderRadius="lg">
        <AlertIcon />
        <Text>No downlink configuration data available.</Text>
      </Alert>
    );
  }

  const { dlCA, dlCmn, dlDmrs } = data;

  return (
    <Card borderWidth="1px" borderRadius="lg" overflow="hidden" variant="outline" border="none">
      <CardBody px={2} py={0}>
        <Tabs variant="soft-rounded" colorScheme="blue" align="start">
          <TabList mt={4} mx={4} flexWrap="wrap" gap={2}>
            <Tab
              shadow="md"
              _focus={{ boxShadow: 'none' }}
              _hover={{ bg: 'blue.200', transform: 'translateY(-2px)' }}
              transition="all 0.2s ease"
            >
              <HStack>
                <Icon as={FiArrowDown} boxSize={5} color="blue.500" />
                <Text fontWeight="medium" fontSize="md">
                  DL Common
                </Text>
              </HStack>
            </Tab>

            {dlCA && (
              <Tab
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'orange.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
              >
                <HStack>
                  <Icon as={FiLayers} boxSize={5} color="orange.500" />
                  <Text fontWeight="medium" fontSize="md">
                    Carrier Aggregation
                  </Text>
                </HStack>
              </Tab>
            )}

            <Tab
              shadow="md"
              _focus={{ boxShadow: 'none' }}
              _hover={{ bg: 'cyan.200', transform: 'translateY(-2px)' }}
              transition="all 0.2s ease"
            >
              <HStack>
                <Icon as={FiRadio} boxSize={5} color="cyan.500" />
                <Text fontWeight="medium" fontSize="md">
                  DMRS
                </Text>
              </HStack>
            </Tab>
          </TabList>

          <TabPanels mt={4} mx={4}>
            <TabPanel p={4}>
              <DLCommonConfig dlCmn={dlCmn} />
            </TabPanel>

            {dlCA && (
              <TabPanel p={4}>
                <DLCAConfig dlCA={dlCA} />
              </TabPanel>
            )}

            <TabPanel p={4}>
              <DLDMRSConfig dlDmrs={dlDmrs} />
            </TabPanel>
          </TabPanels>
        </Tabs>
      </CardBody>
    </Card>
  );
};
