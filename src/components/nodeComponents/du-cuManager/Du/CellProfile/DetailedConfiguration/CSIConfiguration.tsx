import React from 'react';
import {
  Box,
  Card,
  CardBody,
  HStack,
  Icon,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
  VStack,
  Badge,
  Alert,
  AlertIcon,
  As,
  Divider,
} from '@chakra-ui/react';
import {
  FiBarChart2,
  FiSettings,
  FiRadio,
  FiLayers,
  FiGrid,
  FiActivity,
  FiTarget,
  FiZap,
  FiWifi,
  FiPower,
  FiCheckCircle,
  FiXCircle,
  FiEye,
  FiTrendingUp,
  FiHash,
} from 'react-icons/fi';
import { CopyButton, getConfigStatus } from '../../common';

interface CSIConfigurationProps {
  data: any;
}

const StatBox = ({
  icon,
  label,
  value,
  colorScheme,
  copyable = false,
  unit = '',
  isStatusValue = false,
}: {
  icon: As;
  label: string;
  value: string | number | boolean;
  colorScheme: string;
  copyable?: boolean;
  unit?: string;
  isStatusValue?: boolean;
}) => {
  const { status_color, status_icon, status_text } = getConfigStatus(value.toString());

  return (
    <Stat
      p={3}
      bg={`${colorScheme}.50`}
      borderRadius="md"
      textAlign="center"
      borderWidth="1px"
      borderColor={`${colorScheme}.100`}
    >
      <StatLabel fontSize="sm" color={`${colorScheme}.600`} fontWeight="medium">
        <HStack justify="center" spacing={1.5}>
          <Icon as={icon} boxSize={4} />
          <Text>{label}</Text>
        </HStack>
      </StatLabel>
      <HStack justify="center" spacing={2} mt={1}>
        <StatNumber fontSize="xl" color={`${colorScheme}.800`} fontFamily="mono">
          {isStatusValue ? (
            <Badge colorScheme={status_color} variant="solid" px={3} py={1} borderRadius="full">
              <HStack spacing={1}>
                <Icon as={status_icon} boxSize={4} />
                <Text>{status_text}</Text>
              </HStack>
            </Badge>
          ) : (
            `${value}${unit}`
          )}
        </StatNumber>
        {copyable && <CopyButton text={String(value)} label={label} />}
      </HStack>
    </Stat>
  );
};

const CSIRSResourceCard = ({ resource, index }: { resource: any; index: number }) => (
  <VStack spacing={4} align="stretch">
    <HStack mb={3} justify="space-between">
      <HStack>
        <Icon as={FiWifi} color="blue.500" />
        <Text fontWeight="bold" fontSize="md" color="blue.700">
          CSI-RS Resource {resource.id}
        </Text>
      </HStack>
      <Badge colorScheme="blue" variant="outline">
        {resource.reportCfgType}
      </Badge>
    </HStack>

    {/* Basic Configuration Overview */}
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3} mb={3}>
      <StatBox icon={FiHash} label="Resource ID" value={resource.id} colorScheme="blue" copyable />
      <StatBox
        icon={FiRadio}
        label="CDM Type"
        value={resource.cdmType?.replace('CSI_RS_CDM_TYPE_', '') || 'N/A'}
        colorScheme="green"
      />
      <StatBox
        icon={FiGrid}
        label="Density"
        value={resource.csiRsDensity?.replace('CSI_RS_DENSITY_', '') || 'N/A'}
        colorScheme="purple"
      />
      <StatBox
        icon={FiActivity}
        label="Periodicity"
        value={resource.csiRsPeriodicity?.replace('CSI_RS_RES_PRDCTY_', '') || 'N/A'}
        colorScheme="orange"
      />
    </SimpleGrid>

    {/* Nested Tabs for Detailed Configuration */}
    <Tabs variant="soft-rounded" colorScheme="gray" align="start" size="sm">
      <TabList mt={2} mx={2} flexWrap="wrap" gap={2}>
        <Tab
          shadow="md"
          _focus={{ boxShadow: 'none' }}
          _hover={{ bg: 'blue.100', transform: 'translateY(-1px)' }}
          transition="all 0.2s ease"
          _selected={{ bg: 'blue.100', shadow: 'md', transform: 'translateY(-1px)' }}
        >
          <HStack>
            <Icon as={FiSettings} boxSize={4} color="blue.500" />
            <Text fontWeight="medium" fontSize="sm">
              Frequency & Symbol
            </Text>
          </HStack>
        </Tab>

        <Tab
          shadow="md"
          _focus={{ boxShadow: 'none' }}
          _hover={{ bg: 'orange.100', transform: 'translateY(-1px)' }}
          transition="all 0.2s ease"
          _selected={{ bg: 'orange.100', shadow: 'md', transform: 'translateY(-1px)' }}
        >
          <HStack>
            <Icon as={FiPower} boxSize={4} color="orange.500" />
            <Text fontWeight="medium" fontSize="sm">
              Power & Features
            </Text>
          </HStack>
        </Tab>

        {resource.csiImElementPattern && (
          <Tab
            shadow="md"
            _focus={{ boxShadow: 'none' }}
            _hover={{ bg: 'purple.100', transform: 'translateY(-1px)' }}
            transition="all 0.2s ease"
            _selected={{ bg: 'purple.100', shadow: 'md', transform: 'translateY(-1px)' }}
          >
            <HStack>
              <Icon as={FiTarget} boxSize={4} color="purple.500" />
              <Text fontWeight="medium" fontSize="sm">
                CSI-IM Pattern
              </Text>
            </HStack>
          </Tab>
        )}

        {(resource.typ1RptCfg || resource.typ2RptCfg) && (
          <Tab
            shadow="md"
            _focus={{ boxShadow: 'none' }}
            _hover={{ bg: 'green.100', transform: 'translateY(-1px)' }}
            transition="all 0.2s ease"
            _selected={{ bg: 'green.100', shadow: 'md', transform: 'translateY(-1px)' }}
          >
            <HStack>
              <Icon as={FiBarChart2} boxSize={4} color="green.500" />
              <Text fontWeight="medium" fontSize="sm">
                Report Config
              </Text>
            </HStack>
          </Tab>
        )}
      </TabList>

      <TabPanels mt={3} mx={2}>
        {/* Frequency & Symbol Configuration Tab */}
        <TabPanel p={3}>
          <VStack spacing={4} align="stretch">
            {/* Frequency Configuration */}
            <Box p={3} bg="blue.50" borderRadius="md" borderWidth="1px" borderColor="blue.200">
              <Text fontWeight="bold" fontSize="sm" color="blue.700" mb={3}>
                Frequency Configuration
              </Text>
              <SimpleGrid columns={{ base: 1, md: 3 }} spacing={3}>
                <Box>
                  <Text fontSize="xs" color="gray.500" fontWeight="medium">
                    Frequency Start CRB
                  </Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {resource.csiFreqStartCrb}
                  </Text>
                </Box>
                <Box>
                  <Text fontSize="xs" color="gray.500" fontWeight="medium">
                    Frequency Num CRB
                  </Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {resource.csiFreqNumCrb}
                  </Text>
                </Box>
                <Box>
                  <Text fontSize="xs" color="gray.500" fontWeight="medium">
                    Row Bitmap
                  </Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {resource.rowBitmap}
                  </Text>
                </Box>
              </SimpleGrid>
            </Box>

            {/* Symbol Configuration */}
            <Box p={3} bg="cyan.50" borderRadius="md" borderWidth="1px" borderColor="cyan.200">
              <Text fontWeight="bold" fontSize="sm" color="cyan.700" mb={3}>
                Symbol Configuration
              </Text>
              <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
                <Box>
                  <Text fontSize="xs" color="gray.500" fontWeight="medium">
                    First Symbol
                  </Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {resource.firstSym}
                  </Text>
                </Box>
                <Box>
                  <Text fontSize="xs" color="gray.500" fontWeight="medium">
                    Second Symbol
                  </Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {resource.secondSym}
                  </Text>
                </Box>
                <Box>
                  <Text fontSize="xs" color="gray.500" fontWeight="medium">
                    Second Symbol Present
                  </Text>
                  <Badge
                    colorScheme={resource.secondSymPres ? 'green' : 'red'}
                    variant="solid"
                    px={3}
                    py={1}
                    borderRadius="full"
                  >
                    <HStack spacing={1}>
                      <Icon as={resource.secondSymPres ? FiCheckCircle : FiXCircle} boxSize={4} />
                      <Text>{resource.secondSymPres ? 'Yes' : 'No'}</Text>
                    </HStack>
                  </Badge>
                </Box>
                <Box>
                  <Text fontSize="xs" color="gray.500" fontWeight="medium">
                    Row Type
                  </Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {resource.csiRsRowType?.replace('CSI_FREQ_DOM_ALLOC_', '') || 'N/A'}
                  </Text>
                </Box>
              </SimpleGrid>
            </Box>
          </VStack>
        </TabPanel>

        {/* Power Control & Features Tab */}
        <TabPanel p={3}>
          <VStack spacing={4} align="stretch">
            {/* Power Control */}
            <Box p={3} bg="orange.50" borderRadius="md" borderWidth="1px" borderColor="orange.200">
              <Text fontWeight="bold" fontSize="sm" color="orange.700" mb={3}>
                Power Control
              </Text>
              <SimpleGrid columns={{ base: 1, md: 3 }} spacing={3}>
                <Box>
                  <Text fontSize="xs" color="gray.500" fontWeight="medium">
                    Power Control Offset
                  </Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {resource.csiRsPwrControlOffset}
                  </Text>
                </Box>
                <Box>
                  <Text fontSize="xs" color="gray.500" fontWeight="medium">
                    Power Control Offset SS
                  </Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {resource.csiRsPwrControlOffsetSS}
                  </Text>
                </Box>
                <Box>
                  <Text fontSize="xs" color="gray.500" fontWeight="medium">
                    Report Periodicity
                  </Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {resource.csiRptPeriodicity?.replace('csiRptPeriodicity_', '') || 'N/A'}
                  </Text>
                </Box>
              </SimpleGrid>
            </Box>

            {/* Feature Enablement */}
            <Box p={3} bg="green.50" borderRadius="md" borderWidth="1px" borderColor="green.200">
              <Text fontWeight="bold" fontSize="sm" color="green.700" mb={3}>
                Feature Enablement
              </Text>
              <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
                <Box>
                  <Text fontSize="xs" color="gray.500" fontWeight="medium">
                    CSI-RS Enable
                  </Text>
                  <Badge
                    colorScheme={resource.isCsiRsEnable ? 'green' : 'red'}
                    variant="solid"
                    px={3}
                    py={1}
                    borderRadius="full"
                  >
                    <HStack spacing={1}>
                      <Icon as={resource.isCsiRsEnable ? FiCheckCircle : FiXCircle} boxSize={4} />
                      <Text>{resource.isCsiRsEnable ? 'Enabled' : 'Disabled'}</Text>
                    </HStack>
                  </Badge>
                </Box>
                <Box>
                  <Text fontSize="xs" color="gray.500" fontWeight="medium">
                    CSI-IM Enable
                  </Text>
                  <Badge
                    colorScheme={resource.isCsiImEnable ? 'green' : 'red'}
                    variant="solid"
                    px={3}
                    py={1}
                    borderRadius="full"
                  >
                    <HStack spacing={1}>
                      <Icon as={resource.isCsiImEnable ? FiCheckCircle : FiXCircle} boxSize={4} />
                      <Text>{resource.isCsiImEnable ? 'Enabled' : 'Disabled'}</Text>
                    </HStack>
                  </Badge>
                </Box>
                <Box>
                  <Text fontSize="xs" color="gray.500" fontWeight="medium">
                    CRI-RSRP Enable
                  </Text>
                  <Badge
                    colorScheme={resource.isCriRsrpEnable ? 'green' : 'red'}
                    variant="solid"
                    px={3}
                    py={1}
                    borderRadius="full"
                  >
                    <HStack spacing={1}>
                      <Icon as={resource.isCriRsrpEnable ? FiCheckCircle : FiXCircle} boxSize={4} />
                      <Text>{resource.isCriRsrpEnable ? 'Enabled' : 'Disabled'}</Text>
                    </HStack>
                  </Badge>
                </Box>
                <Box>
                  <Text fontSize="xs" color="gray.500" fontWeight="medium">
                    N1N2
                  </Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {resource.n1n2?.replace('RGR_CB_TYPE1_SINGLE_PANEL_RSTRCN_', '') || 'N/A'}
                  </Text>
                </Box>
              </SimpleGrid>
            </Box>
          </VStack>
        </TabPanel>

        {/* CSI-IM Pattern Tab */}
        {resource.csiImElementPattern && (
          <TabPanel p={3}>
            <Box p={3} bg="purple.50" borderRadius="md" borderWidth="1px" borderColor="purple.200">
              <Text fontWeight="bold" fontSize="sm" color="purple.700" mb={3}>
                CSI-IM Element Pattern
              </Text>
              <SimpleGrid columns={{ base: 1, md: 3 }} spacing={3}>
                <Box>
                  <Text fontSize="xs" color="gray.500" fontWeight="medium">
                    Pattern Type
                  </Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {resource.csiImElementPattern.csiImPatternType?.replace('CSI_IM_', '') || 'N/A'}
                  </Text>
                </Box>
                <Box>
                  <Text fontSize="xs" color="gray.500" fontWeight="medium">
                    Subcarrier Location
                  </Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {resource.csiImElementPattern.csiImSubcarrierLocation?.replace('CSI_IM_SC_LOCATION_', '') || 'N/A'}
                  </Text>
                </Box>
                <Box>
                  <Text fontSize="xs" color="gray.500" fontWeight="medium">
                    Symbol Location
                  </Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {resource.csiImElementPattern.csiImSymbolLocation}
                  </Text>
                </Box>
              </SimpleGrid>
            </Box>
          </TabPanel>
        )}

        {/* Report Configuration Tab */}
        {(resource.typ1RptCfg || resource.typ2RptCfg) && (
          <TabPanel p={3}>
            <VStack spacing={4} align="stretch">
              {/* Type 1 Report Configuration */}
              {resource.typ1RptCfg && (
                <Box p={3} bg="green.50" borderRadius="md" borderWidth="1px" borderColor="green.200">
                  <Text fontWeight="bold" fontSize="sm" color="green.700" mb={3}>
                    Type 1 Report Configuration
                  </Text>
                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={3}>
                    <Box>
                      <Text fontSize="xs" color="gray.500" fontWeight="medium">
                        Aperiodic CSI Enable
                      </Text>
                      <Badge
                        colorScheme={resource.typ1RptCfg.aperCsiEnable ? 'green' : 'red'}
                        variant="solid"
                        px={3}
                        py={1}
                        borderRadius="full"
                      >
                        <HStack spacing={1}>
                          <Icon as={resource.typ1RptCfg.aperCsiEnable ? FiCheckCircle : FiXCircle} boxSize={4} />
                          <Text>{resource.typ1RptCfg.aperCsiEnable ? 'Enabled' : 'Disabled'}</Text>
                        </HStack>
                      </Badge>
                    </Box>
                    <Box>
                      <Text fontSize="xs" color="gray.500" fontWeight="medium">
                        Aperiodic CSI SCell Enable
                      </Text>
                      <Badge
                        colorScheme={resource.typ1RptCfg.aperCsiScellEnable ? 'green' : 'red'}
                        variant="solid"
                        px={3}
                        py={1}
                        borderRadius="full"
                      >
                        <HStack spacing={1}>
                          <Icon as={resource.typ1RptCfg.aperCsiScellEnable ? FiCheckCircle : FiXCircle} boxSize={4} />
                          <Text>{resource.typ1RptCfg.aperCsiScellEnable ? 'Enabled' : 'Disabled'}</Text>
                        </HStack>
                      </Badge>
                    </Box>
                  </SimpleGrid>
                </Box>
              )}

              {/* Type 2 Report Configuration */}
              {resource.typ2RptCfg && (
                <Box p={3} bg="teal.50" borderRadius="md" borderWidth="1px" borderColor="teal.200">
                  <Text fontWeight="bold" fontSize="sm" color="teal.700" mb={3}>
                    Type 2 Report Configuration
                  </Text>
                  <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
                    <Box>
                      <Text fontSize="xs" color="gray.500" fontWeight="medium">
                        Number of Beams
                      </Text>
                      <Text fontSize="sm" fontWeight="bold">
                        {resource.typ2RptCfg.numberOfBeams}
                      </Text>
                    </Box>
                    <Box>
                      <Text fontSize="xs" color="gray.500" fontWeight="medium">
                        Phase Alphabet Size
                      </Text>
                      <Text fontSize="sm" fontWeight="bold">
                        {resource.typ2RptCfg.phaseAplhabetSize}
                      </Text>
                    </Box>
                    <Box>
                      <Text fontSize="xs" color="gray.500" fontWeight="medium">
                        Subband Amplitude
                      </Text>
                      <Badge
                        colorScheme={resource.typ2RptCfg.subbandAmplitude ? 'green' : 'red'}
                        variant="solid"
                        px={3}
                        py={1}
                        borderRadius="full"
                      >
                        <HStack spacing={1}>
                          <Icon as={resource.typ2RptCfg.subbandAmplitude ? FiCheckCircle : FiXCircle} boxSize={4} />
                          <Text>{resource.typ2RptCfg.subbandAmplitude ? 'Enabled' : 'Disabled'}</Text>
                        </HStack>
                      </Badge>
                    </Box>
                    <Box>
                      <Text fontSize="xs" color="gray.500" fontWeight="medium">
                        Aperiodic CSI Enable
                      </Text>
                      <Badge
                        colorScheme={resource.typ2RptCfg.aperCsiEnable ? 'green' : 'red'}
                        variant="solid"
                        px={3}
                        py={1}
                        borderRadius="full"
                      >
                        <HStack spacing={1}>
                          <Icon as={resource.typ2RptCfg.aperCsiEnable ? FiCheckCircle : FiXCircle} boxSize={4} />
                          <Text>{resource.typ2RptCfg.aperCsiEnable ? 'Enabled' : 'Disabled'}</Text>
                        </HStack>
                      </Badge>
                    </Box>
                  </SimpleGrid>
                </Box>
              )}
            </VStack>
          </TabPanel>
        )}
      </TabPanels>
    </Tabs>
  </VStack>
);

const CSIRSOverview = ({ csiRs }: { csiRs: any[] }) => {
  if (!csiRs || csiRs.length === 0) {
    return (
      <Alert status="info" borderRadius="lg">
        <AlertIcon />
        <Text>No CSI-RS resources available.</Text>
      </Alert>
    );
  }

  return (
    <VStack spacing={4} align="stretch">
      {/* Overview Statistics */}
      <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
        <StatBox icon={FiWifi} label="Total CSI-RS Resources" value={csiRs?.length || 0} colorScheme="blue" />
        <StatBox
          icon={FiCheckCircle}
          label="Enabled Resources"
          value={csiRs?.filter((rs) => rs.isCsiRsEnable).length || 0}
          colorScheme="green"
        />
        <StatBox
          icon={FiEye}
          label="CSI-IM Enabled"
          value={csiRs?.filter((rs) => rs.isCsiImEnable).length || 0}
          colorScheme="purple"
        />
      </SimpleGrid>

      <Divider />

      {/* Individual CSI-RS Resources as Tabs */}
      <Tabs variant="soft-rounded" colorScheme="purple" align="start">
        <TabList mt={2} mx={2} flexWrap="wrap" gap={2}>
          {csiRs?.map((resource, index) => {
            const { status_color, status_icon, status_text } = getConfigStatus(resource.isCsiRsEnable.toString());
            return (
              <Tab
                key={resource.id}
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'purple.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
              >
                <HStack>
                  <Icon as={FiWifi} boxSize={4} color="purple.500" />
                  <Text fontWeight="medium" fontSize="sm">
                    CSI-RS {resource.id}
                  </Text>
                  <Badge colorScheme={status_color} variant="solid" px={3} py={1} borderRadius="full">
                    <HStack spacing={1}>
                      <Icon as={status_icon} boxSize={4} />
                      <Text>{status_text}</Text>
                    </HStack>
                  </Badge>
                </HStack>
              </Tab>
            );
          })}
        </TabList>

        <TabPanels mt={4} mx={2}>
          {csiRs?.map((resource, index) => (
            <TabPanel key={resource.id} p={4}>
              <CSIRSResourceCard resource={resource} index={index} />
            </TabPanel>
          ))}
        </TabPanels>
      </Tabs>
    </VStack>
  );
};

const CSITRSConfig = ({ csiTrs }: { csiTrs: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox
        icon={FiZap}
        label="TRS Enable"
        value={csiTrs?.isTrsEnable || false}
        colorScheme="cyan"
        isStatusValue={true}
      />
      <StatBox icon={FiActivity} label="TRS Periodicity" value={csiTrs?.trsPeriodicity || 'N/A'} colorScheme="teal" />
      <StatBox icon={FiGrid} label="TRS Offset" value={csiTrs?.trsOffset || 0} colorScheme="blue" />
      <StatBox icon={FiLayers} label="TRS Num RB" value={csiTrs?.trsNumRb || 0} colorScheme="green" />
    </SimpleGrid>

    <Box p={4} borderWidth="1px" borderRadius="lg" bg="cyan.50" borderColor="cyan.200">
      <HStack mb={3}>
        <Icon as={FiSettings} color="cyan.500" />
        <Text fontWeight="bold" fontSize="md" color="cyan.700">
          TRS Symbol Configuration
        </Text>
      </HStack>
      <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            First Symbol in First Slot
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {csiTrs?.trsFirstSymbolInFirstSlot || 'N/A'}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            First Symbol in Second Slot
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {csiTrs?.trsFirstSymbolInSecondSlot || 'N/A'}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            TRS Start RB
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {csiTrs?.trsStartRb || 0}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            Power Control Offset SS
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {csiTrs?.trsPwrControlOffsetSS || 'N/A'}
          </Text>
        </Box>
      </SimpleGrid>
    </Box>
  </VStack>
);

export const CSIConfiguration: React.FC<CSIConfigurationProps> = ({ data }) => {
  if (!data) {
    return (
      <Alert status="info" borderRadius="lg">
        <AlertIcon />
        <Text>No CSI configuration data available.</Text>
      </Alert>
    );
  }

  const { csiRs, csiTrs } = data;

  return (
    <Card borderWidth="1px" borderRadius="lg" overflow="hidden" variant="outline" border="none">
      <CardBody px={2} py={0}>
        <Tabs variant="soft-rounded" colorScheme="blue" align="start">
          <TabList mt={4} mx={4} flexWrap="wrap" gap={2}>
            <Tab
              shadow="md"
              _focus={{ boxShadow: 'none' }}
              _hover={{ bg: 'blue.200', transform: 'translateY(-2px)' }}
              transition="all 0.2s ease"
            >
              <HStack>
                <Icon as={FiBarChart2} boxSize={5} color="blue.500" />
                <Text fontWeight="medium" fontSize="md">
                  CSI-RS
                </Text>
                <Badge size="sm">{csiRs?.length || 0}</Badge>
              </HStack>
            </Tab>

            <Tab
              shadow="md"
              _focus={{ boxShadow: 'none' }}
              _hover={{ bg: 'cyan.200', transform: 'translateY(-2px)' }}
              transition="all 0.2s ease"
            >
              <HStack>
                <Icon as={FiZap} boxSize={5} color="cyan.500" />
                <Text fontWeight="medium" fontSize="md">
                  CSI-TRS
                </Text>
              </HStack>
            </Tab>
          </TabList>

          <TabPanels mt={4} mx={4}>
            <TabPanel p={4}>
              <CSIRSOverview csiRs={csiRs} />
            </TabPanel>

            <TabPanel p={4}>
              <CSITRSConfig csiTrs={csiTrs} />
            </TabPanel>
          </TabPanels>
        </Tabs>
      </CardBody>
    </Card>
  );
};
