import React from 'react';
import {
  Box,
  Card,
  CardBody,
  HStack,
  Icon,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
  VStack,
  Badge,
  Alert,
  AlertIcon,
  As,
} from '@chakra-ui/react';
import {
  FiCpu,
  FiSettings,
  FiRadio,
  FiLayers,
  FiGrid,
  FiActivity,
  FiTarget,
  FiZap,
  FiWifi,
  FiPower,
  FiCheckCircle,
  FiXCircle,
  FiArrowDown,
  FiArrowUp,
  FiTrendingUp,
  FiTrendingDown,
  FiSliders,
  FiBarChart2,
  FiThermometer,
  FiAperture,
} from 'react-icons/fi';
import { CopyButton } from '../../common';

interface MACConfigurationProps {
  data: any;
}

const StatBox = ({
  icon,
  label,
  value,
  colorScheme,
  copyable = false,
  unit = '',
}: {
  icon: As;
  label: string;
  value: string | number | boolean;
  colorScheme: string;
  copyable?: boolean;
  unit?: string;
}) => (
  <Stat
    p={3}
    bg={`${colorScheme}.50`}
    borderRadius="md"
    textAlign="center"
    borderWidth="1px"
    borderColor={`${colorScheme}.100`}
  >
    <StatLabel fontSize="sm" color={`${colorScheme}.600`} fontWeight="medium">
      <HStack justify="center" spacing={1.5}>
        <Icon as={icon} boxSize={4} />
        <Text>{label}</Text>
      </HStack>
    </StatLabel>
    <HStack justify="center" spacing={2} mt={1}>
      <StatNumber fontSize="xl" color={`${colorScheme}.800`} fontFamily="mono">
        {typeof value === 'boolean' ? (
          <Badge colorScheme={value ? 'green' : 'red'} variant="solid" px={3} py={1} borderRadius="full">
            <HStack spacing={1}>
              <Icon as={value ? FiCheckCircle : FiXCircle} boxSize={4} />
              <Text>{value ? 'Enabled' : 'Disabled'}</Text>
            </HStack>
          </Badge>
        ) : (
          `${value}${unit}`
        )}
      </StatNumber>
      {copyable && <CopyButton text={String(value)} label={label} />}
    </HStack>
  </Stat>
);

const GeneralMACConfig = ({ data }: { data: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiCpu} label="Cyclic Prefix Type" value={data.cyclicPrefixType || 'N/A'} colorScheme="blue" />
      <StatBox icon={FiRadio} label="DL Modulation" value={data.dlModulation || 'N/A'} colorScheme="green" />
      <StatBox icon={FiRadio} label="UL Modulation" value={data.ulModulation || 'N/A'} colorScheme="purple" />
      <StatBox icon={FiLayers} label="Number of SSB" value={data.numSsb || 0} colorScheme="orange" />
    </SimpleGrid>

    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiTarget} label="Initial DL MCS" value={data.initialDlMcs || 0} colorScheme="cyan" />
      <StatBox icon={FiTarget} label="Initial UL MCS" value={data.initialUlMcs || 0} colorScheme="teal" />
      <StatBox icon={FiArrowDown} label="Max DL MCS" value={data.maxDlMcs || 0} colorScheme="blue" />
      <StatBox icon={FiArrowUp} label="Max UL MCS" value={data.maxUlMcs || 0} colorScheme="green" />
    </SimpleGrid>

    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiActivity} label="DL Rank" value={data.dlRank || 0} colorScheme="blue" />
      <StatBox icon={FiActivity} label="UL Rank" value={data.ulRank || 0} colorScheme="green" />
      <StatBox icon={FiWifi} label="DL Antenna Ports" value={data.dlNumAntPorts || 0} colorScheme="purple" />
      <StatBox icon={FiWifi} label="UL Antenna Ports" value={data.ulNumOfAntPorts || 0} colorScheme="orange" />
    </SimpleGrid>
  </VStack>
);

const HARQConfig = ({ data }: { data: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiArrowDown} label="Max DL HARQ TX" value={data.maxDlHqTx || 0} colorScheme="blue" />
      <StatBox icon={FiArrowUp} label="Max UL HARQ TX" value={data.maxUlHqTx || 0} colorScheme="green" />
      <StatBox icon={FiTarget} label="Max MSG4 HARQ TX" value={data.maxMsg4HqTx || 0} colorScheme="purple" />
      <StatBox icon={FiZap} label="Max VoNR DL TB HARQ TX" value={data.maxDlVonrTbHqTx || 0} colorScheme="orange" />
    </SimpleGrid>

    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiZap} label="Max VoNR UL TB HARQ TX" value={data.maxUlVonrTbHqTx || 0} colorScheme="cyan" />
      <StatBox
        icon={FiTrendingDown}
        label="DL BLER Tolerance"
        value={data.dlBlerTolerance || 0}
        unit="%"
        colorScheme="blue"
      />
      <StatBox
        icon={FiTrendingUp}
        label="UL BLER Tolerance"
        value={data.ulBlerTolerance || 0}
        unit="%"
        colorScheme="green"
      />
      <StatBox icon={FiGrid} label="DL Block Window Size" value={data.dlBlockWindowSize || 0} colorScheme="purple" />
    </SimpleGrid>
  </VStack>
);

const SchedulingConfig = ({ data }: { data: any }) => (
  <VStack spacing={4} align="stretch">
    {/* Header Row */}
    <SimpleGrid columns={{ base: 1, md: 5 }} spacing={4} alignItems="end">
      <Box>
        <Text fontSize="sm" fontWeight="bold" color="gray.700" textAlign="center">
          Parameter
        </Text>
      </Box>
      <Box>
        <HStack justify="center" mb={2}>
          <Icon as={FiTarget} color="blue.500" boxSize={4} />
          <Text fontSize="sm" fontWeight="bold" color="blue.700" textAlign="center">
            Max UE per TTI
          </Text>
        </HStack>
      </Box>
      <Box>
        <HStack justify="center" mb={2}>
          <Icon as={FiZap} color="green.500" boxSize={4} />
          <Text fontSize="sm" fontWeight="bold" color="green.700" textAlign="center">
            Max GBR per TTI
          </Text>
        </HStack>
      </Box>
      <Box>
        <HStack justify="center" mb={2}>
          <Icon as={FiGrid} color="purple.500" boxSize={4} />
          <Text fontSize="sm" fontWeight="bold" color="purple.700" textAlign="center">
            Max Non-GBR per TTI
          </Text>
        </HStack>
      </Box>
      <Box>
        <HStack justify="center" mb={2}>
          <Icon as={FiWifi} color="orange.500" boxSize={4} />
          <Text fontSize="sm" fontWeight="bold" color="orange.700" textAlign="center">
            Max Voice per TTI
          </Text>
        </HStack>
      </Box>
    </SimpleGrid>

    {/* Downlink Row */}
    <SimpleGrid
      columns={{ base: 1, md: 5 }}
      spacing={4}
      p={3}
      bg="blue.50"
      borderRadius="md"
      borderWidth="1px"
      borderColor="blue.200"
    >
      <Box>
        <HStack>
          <Icon as={FiArrowDown} color="blue.500" boxSize={4} />
          <Text fontSize="sm" fontWeight="bold" color="blue.700">
            Downlink
          </Text>
        </HStack>
      </Box>
      <Box textAlign="center">
        <Text fontSize="lg" fontWeight="bold" color="blue.800" fontFamily="mono">
          {data.maxDlUePerTTI}
        </Text>
      </Box>
      <Box textAlign="center">
        <Text fontSize="lg" fontWeight="bold" color="green.800" fontFamily="mono">
          {data.maxDlUeWithGbrPerTti}
        </Text>
      </Box>
      <Box textAlign="center">
        <Text fontSize="lg" fontWeight="bold" color="purple.800" fontFamily="mono">
          {data.maxDlUeWithNonGbrPerTti}
        </Text>
      </Box>
      <Box textAlign="center">
        <Text fontSize="lg" fontWeight="bold" color="orange.800" fontFamily="mono">
          {data.maxDlUeWithVoicePerTti}
        </Text>
      </Box>
    </SimpleGrid>

    {/* Uplink Row */}
    <SimpleGrid
      columns={{ base: 1, md: 5 }}
      spacing={4}
      p={3}
      bg="green.50"
      borderRadius="md"
      borderWidth="1px"
      borderColor="green.200"
    >
      <Box>
        <HStack>
          <Icon as={FiArrowUp} color="green.500" boxSize={4} />
          <Text fontSize="sm" fontWeight="bold" color="green.700">
            Uplink
          </Text>
        </HStack>
      </Box>
      <Box textAlign="center">
        <Text fontSize="lg" fontWeight="bold" color="blue.800" fontFamily="mono">
          {data.maxUlUePerTTI}
        </Text>
      </Box>
      <Box textAlign="center">
        <Text fontSize="lg" fontWeight="bold" color="green.800" fontFamily="mono">
          {data.maxUlUeWithGbrPerTti}
        </Text>
      </Box>
      <Box textAlign="center">
        <Text fontSize="lg" fontWeight="bold" color="purple.800" fontFamily="mono">
          {data.maxUlUeWithNonGbrPerTti}
        </Text>
      </Box>
      <Box textAlign="center">
        <Text fontSize="lg" fontWeight="bold" color="orange.800" fontFamily="mono">
          {data.maxUlUeWithVoicePerTti}
        </Text>
      </Box>
    </SimpleGrid>
  </VStack>
);

const LinkAdaptationConfig = ({ data }: { data: any }) => (
  <VStack spacing={4} align="stretch">
    {/* Header Row */}
    <SimpleGrid columns={{ base: 1, md: 5 }} spacing={4} alignItems="end">
      <Box>
        <Text fontSize="sm" fontWeight="bold" color="gray.700" textAlign="center">
          Parameter
        </Text>
      </Box>
      <Box>
        <HStack justify="center" mb={2}>
          <Icon as={FiTrendingUp} color="cyan.500" boxSize={4} />
          <Text fontSize="sm" fontWeight="bold" color="cyan.700" textAlign="center">
            LA Step Up
          </Text>
        </HStack>
      </Box>
      <Box>
        <HStack justify="center" mb={2}>
          <Icon as={FiTrendingDown} color="red.500" boxSize={4} />
          <Text fontSize="sm" fontWeight="bold" color="red.700" textAlign="center">
            LA Step Down
          </Text>
        </HStack>
      </Box>
      <Box>
        <HStack justify="center" mb={2}>
          <Icon as={FiActivity} color="purple.500" boxSize={4} />
          <Text fontSize="sm" fontWeight="bold" color="purple.700" textAlign="center">
            Seed OLLA Interval
          </Text>
        </HStack>
      </Box>
      <Box>
        <HStack justify="center" mb={2}>
          <Icon as={FiBarChart2} color="orange.500" boxSize={4} />
          <Text fontSize="sm" fontWeight="bold" color="orange.700" textAlign="center">
            Timing Window Size
          </Text>
        </HStack>
      </Box>
    </SimpleGrid>

    {/* Downlink Row */}
    <SimpleGrid
      columns={{ base: 1, md: 5 }}
      spacing={4}
      p={3}
      bg="purple.50"
      borderRadius="md"
      borderWidth="1px"
      borderColor="purple.200"
    >
      <Box>
        <HStack>
          <Icon as={FiArrowDown} color="purple.500" boxSize={4} />
          <Text fontSize="sm" fontWeight="bold" color="purple.700">
            Downlink
          </Text>
        </HStack>
      </Box>
      <Box textAlign="center">
        <Text fontSize="lg" fontWeight="bold" color="cyan.800" fontFamily="mono">
          {data.dlLaStepup}
        </Text>
      </Box>
      <Box textAlign="center">
        <Text fontSize="lg" fontWeight="bold" color="red.800" fontFamily="mono">
          {data.dlLaStepdown}
        </Text>
      </Box>
      <Box textAlign="center">
        <Text fontSize="lg" fontWeight="bold" color="purple.800" fontFamily="mono">
          {data.dlSeedOllaInterval}
        </Text>
      </Box>
      <Box textAlign="center">
        <Text fontSize="lg" fontWeight="bold" color="orange.800" fontFamily="mono">
          {data.dlTimingWindowSize}
        </Text>
      </Box>
    </SimpleGrid>

    {/* Uplink Row */}
    <SimpleGrid
      columns={{ base: 1, md: 5 }}
      spacing={4}
      p={3}
      bg="orange.50"
      borderRadius="md"
      borderWidth="1px"
      borderColor="orange.200"
    >
      <Box>
        <HStack>
          <Icon as={FiArrowUp} color="orange.500" boxSize={4} />
          <Text fontSize="sm" fontWeight="bold" color="orange.700">
            Uplink
          </Text>
        </HStack>
      </Box>
      <Box textAlign="center">
        <Text fontSize="lg" fontWeight="bold" color="cyan.800" fontFamily="mono">
          {data.ulLaStepup}
        </Text>
      </Box>
      <Box textAlign="center">
        <Text fontSize="lg" fontWeight="bold" color="red.800" fontFamily="mono">
          {data.ulLaStepdown}
        </Text>
      </Box>
      <Box textAlign="center">
        <Text fontSize="lg" fontWeight="bold" color="purple.800" fontFamily="mono">
          {data.ulSeedOllaInterval}
        </Text>
      </Box>
      <Box textAlign="center">
        <Text fontSize="lg" fontWeight="bold" color="orange.800" fontFamily="mono">
          {data.ulTimingWindowSize}
        </Text>
      </Box>
    </SimpleGrid>
  </VStack>
);

const PowerControlConfig = ({ data }: { data: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiPower} label="Max Accumulated TPC" value={data.maxAccumulatedTpc || 0} colorScheme="blue" />
      <StatBox icon={FiPower} label="Min Accumulated TPC" value={data.minAccumulatedTpc || 0} colorScheme="green" />
      <StatBox icon={FiSliders} label="TPC Update Interval" value={data.tpcUpdateInterval || 0} colorScheme="purple" />
      <StatBox icon={FiTarget} label="Target SINR Offset" value={data.targetSinrOffset || 0} colorScheme="orange" />
    </SimpleGrid>

    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox
        icon={FiThermometer}
        label="Min Target SINR Index"
        value={data.minTargetSinrIdx || 0}
        colorScheme="cyan"
      />
      <StatBox
        icon={FiThermometer}
        label="Max Target SINR Index"
        value={data.maxTargetSinrIdx || 0}
        colorScheme="teal"
      />
      <StatBox
        icon={FiAperture}
        label="Min Pathloss Threshold"
        value={data.minPathlossTh || 0}
        unit=" dB"
        colorScheme="blue"
      />
      <StatBox
        icon={FiAperture}
        label="Max Pathloss Threshold"
        value={data.maxPathlossTh || 0}
        unit=" dB"
        colorScheme="green"
      />
    </SimpleGrid>

    {/* PHR Configuration */}
    {data.phrConfig && (
      <Box p={4} borderWidth="1px" borderRadius="lg" bg="blue.50" borderColor="blue.200">
        <HStack mb={3}>
          <Icon as={FiPower} color="blue.500" />
          <Text fontWeight="bold" fontSize="md" color="blue.700">
            Power Headroom Report (PHR) Configuration
          </Text>
        </HStack>
        <SimpleGrid columns={{ base: 2, md: 3 }} spacing={3}>
          <Box>
            <Text fontSize="xs" color="gray.500" fontWeight="medium">
              PHR Periodic Timer
            </Text>
            <Text fontSize="sm" fontWeight="bold">
              {data.phrConfig.phrPeriodicTimer}
            </Text>
          </Box>
          <Box>
            <Text fontSize="xs" color="gray.500" fontWeight="medium">
              PHR Prohibit Timer
            </Text>
            <Text fontSize="sm" fontWeight="bold">
              {data.phrConfig.phrProhibitPeriodicTimer}
            </Text>
          </Box>
          <Box>
            <Text fontSize="xs" color="gray.500" fontWeight="medium">
              TX Power Change Factor
            </Text>
            <Text fontSize="sm" fontWeight="bold">
              {data.phrConfig.phrTxPwrChangeFactor}
            </Text>
          </Box>
        </SimpleGrid>
        <SimpleGrid columns={{ base: 2, md: 3 }} spacing={3} mt={3}>
          <Box>
            <Text fontSize="xs" color="gray.500" fontWeight="medium">
              PHR Mode Other CG
            </Text>
            <Badge
              colorScheme={data.phrConfig.phrModeOtherCg ? 'green' : 'red'}
              variant="solid"
              px={3}
              py={1}
              borderRadius="full"
            >
              <HStack spacing={1}>
                <Icon as={data.phrConfig.phrModeOtherCg ? FiCheckCircle : FiXCircle} boxSize={4} />
                <Text>{data.phrConfig.phrModeOtherCg ? 'Enabled' : 'Disabled'}</Text>
              </HStack>
            </Badge>
          </Box>
          <Box>
            <Text fontSize="xs" color="gray.500" fontWeight="medium">
              PHR Type2 Other Cell
            </Text>
            <Badge
              colorScheme={data.phrConfig.phrType2OtherCell ? 'green' : 'red'}
              variant="solid"
              px={3}
              py={1}
              borderRadius="full"
            >
              <HStack spacing={1}>
                <Icon as={data.phrConfig.phrType2OtherCell ? FiCheckCircle : FiXCircle} boxSize={4} />
                <Text>{data.phrConfig.phrType2OtherCell ? 'Enabled' : 'Disabled'}</Text>
              </HStack>
            </Badge>
          </Box>
          <Box>
            <Text fontSize="xs" color="gray.500" fontWeight="medium">
              Scheduled PHR Handle
            </Text>
            <Badge
              colorScheme={data.phrConfig.schdPhrHdl ? 'green' : 'red'}
              variant="solid"
              px={3}
              py={1}
              borderRadius="full"
            >
              <HStack spacing={1}>
                <Icon as={data.phrConfig.schdPhrHdl ? FiCheckCircle : FiXCircle} boxSize={4} />
                <Text>{data.phrConfig.schdPhrHdl ? 'Enabled' : 'Disabled'}</Text>
              </HStack>
            </Badge>
          </Box>
        </SimpleGrid>
      </Box>
    )}
  </VStack>
);

const AdvancedConfig = ({ data }: { data: any }) => (
  <VStack spacing={4} align="stretch">
    <Box p={4} borderWidth="1px" borderRadius="lg" bg="purple.50" borderColor="purple.200">
      <HStack mb={3}>
        <Icon as={FiSettings} color="purple.500" />
        <Text fontWeight="bold" fontSize="md" color="purple.700">
          Feature Enablement
        </Text>
      </HStack>
      <SimpleGrid columns={{ base: 2, md: 3 }} spacing={3}>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            Enable DCI Format 0_1
          </Text>
          <Badge
            colorScheme={data.enableDciFormat0_1 ? 'green' : 'red'}
            variant="solid"
            px={3}
            py={1}
            borderRadius="full"
          >
            <HStack spacing={1}>
              <Icon as={data.enableDciFormat0_1 ? FiCheckCircle : FiXCircle} boxSize={4} />
              <Text>{data.enableDciFormat0_1 ? 'Enabled' : 'Disabled'}</Text>
            </HStack>
          </Badge>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            Enable DL PRB Randomization
          </Text>
          <Badge
            colorScheme={data.enableDlPrbRandomization ? 'green' : 'red'}
            variant="solid"
            px={3}
            py={1}
            borderRadius="full"
          >
            <HStack spacing={1}>
              <Icon as={data.enableDlPrbRandomization ? FiCheckCircle : FiXCircle} boxSize={4} />
              <Text>{data.enableDlPrbRandomization ? 'Enabled' : 'Disabled'}</Text>
            </HStack>
          </Badge>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            Enable UL PRB Randomization
          </Text>
          <Badge
            colorScheme={data.enableUlPrbRandomization ? 'green' : 'red'}
            variant="solid"
            px={3}
            py={1}
            borderRadius="full"
          >
            <HStack spacing={1}>
              <Icon as={data.enableUlPrbRandomization ? FiCheckCircle : FiXCircle} boxSize={4} />
              <Text>{data.enableUlPrbRandomization ? 'Enabled' : 'Disabled'}</Text>
            </HStack>
          </Badge>
        </Box>
      </SimpleGrid>
    </Box>

    {/* OCNS Configuration */}
    {data.ocnsConfig && (
      <Box p={4} borderWidth="1px" borderRadius="lg" bg="orange.50" borderColor="orange.200">
        <HStack mb={3}>
          <Icon as={FiRadio} color="orange.500" />
          <Text fontWeight="bold" fontSize="md" color="orange.700">
            OCNS Configuration
          </Text>
        </HStack>
        <SimpleGrid columns={{ base: 2, md: 3 }} spacing={3}>
          <Box>
            <Text fontSize="xs" color="gray.500" fontWeight="medium">
              OCNS Load Percentage
            </Text>
            <Text fontSize="sm" fontWeight="bold">
              {data.ocnsConfig.ocnsLoadPercentage}%
            </Text>
          </Box>
          <Box>
            <Text fontSize="xs" color="gray.500" fontWeight="medium">
              OCNS MCS
            </Text>
            <Text fontSize="sm" fontWeight="bold">
              {data.ocnsConfig.ocnsMcs}
            </Text>
          </Box>
          <Box>
            <Text fontSize="xs" color="gray.500" fontWeight="medium">
              OCNS Rank
            </Text>
            <Text fontSize="sm" fontWeight="bold">
              {data.ocnsConfig.ocnsRank}
            </Text>
          </Box>
        </SimpleGrid>
      </Box>
    )}

    {/* UL RB Limit per MCS */}
    {data.ulRbLimitPerMcs && (
      <Box p={4} borderWidth="1px" borderRadius="lg" bg="green.50" borderColor="green.200">
        <HStack mb={3}>
          <Icon as={FiGrid} color="green.500" />
          <Text fontWeight="bold" fontSize="md" color="green.700">
            UL RB Limit per MCS
          </Text>
        </HStack>
        <SimpleGrid columns={{ base: 2, md: 5 }} spacing={2}>
          {Object.entries(data.ulRbLimitPerMcs).map(([key, value]) => (
            <Box key={key}>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                {key.replace('mcs', 'MCS ').replace('RbLimit', '')}
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {value as string} RBs
              </Text>
            </Box>
          ))}
        </SimpleGrid>
      </Box>
    )}
  </VStack>
);

export const MACConfiguration: React.FC<MACConfigurationProps> = ({ data }) => {
  if (!data) {
    return (
      <Alert status="info" borderRadius="lg">
        <AlertIcon />
        <Text>No MAC configuration data available.</Text>
      </Alert>
    );
  }

  return (
    <Card borderWidth="1px" borderRadius="lg" overflow="hidden" variant="outline" border="none">
      <CardBody px={2} py={0}>
        <Tabs variant="soft-rounded" colorScheme="purple" align="start">
          <TabList mt={4} mx={4} flexWrap="wrap" gap={2}>
            <Tab
              shadow="md"
              _focus={{ boxShadow: 'none' }}
              _hover={{ bg: 'purple.200', transform: 'translateY(-2px)' }}
              transition="all 0.2s ease"
            >
              <HStack>
                <Icon as={FiCpu} boxSize={5} color="purple.500" />
                <Text fontWeight="medium" fontSize="md">
                  General
                </Text>
              </HStack>
            </Tab>

            <Tab
              shadow="md"
              _focus={{ boxShadow: 'none' }}
              _hover={{ bg: 'blue.200', transform: 'translateY(-2px)' }}
              transition="all 0.2s ease"
            >
              <HStack>
                <Icon as={FiActivity} boxSize={5} color="blue.500" />
                <Text fontWeight="medium" fontSize="md">
                  HARQ
                </Text>
              </HStack>
            </Tab>

            <Tab
              shadow="md"
              _focus={{ boxShadow: 'none' }}
              _hover={{ bg: 'green.200', transform: 'translateY(-2px)' }}
              transition="all 0.2s ease"
            >
              <HStack>
                <Icon as={FiGrid} boxSize={5} color="green.500" />
                <Text fontWeight="medium" fontSize="md">
                  Scheduling
                </Text>
              </HStack>
            </Tab>

            <Tab
              shadow="md"
              _focus={{ boxShadow: 'none' }}
              _hover={{ bg: 'orange.200', transform: 'translateY(-2px)' }}
              transition="all 0.2s ease"
            >
              <HStack>
                <Icon as={FiBarChart2} boxSize={5} color="orange.500" />
                <Text fontWeight="medium" fontSize="md">
                  Link Adaptation
                </Text>
              </HStack>
            </Tab>

            <Tab
              shadow="md"
              _focus={{ boxShadow: 'none' }}
              _hover={{ bg: 'cyan.200', transform: 'translateY(-2px)' }}
              transition="all 0.2s ease"
            >
              <HStack>
                <Icon as={FiPower} boxSize={5} color="cyan.500" />
                <Text fontWeight="medium" fontSize="md">
                  Power Control
                </Text>
              </HStack>
            </Tab>

            <Tab
              shadow="md"
              _focus={{ boxShadow: 'none' }}
              _hover={{ bg: 'teal.200', transform: 'translateY(-2px)' }}
              transition="all 0.2s ease"
            >
              <HStack>
                <Icon as={FiSliders} boxSize={5} color="teal.500" />
                <Text fontWeight="medium" fontSize="md">
                  Advanced
                </Text>
              </HStack>
            </Tab>
          </TabList>

          <TabPanels mt={4} mx={4}>
            <TabPanel p={4}>
              <GeneralMACConfig data={data} />
            </TabPanel>

            <TabPanel p={4}>
              <HARQConfig data={data} />
            </TabPanel>

            <TabPanel p={4}>
              <SchedulingConfig data={data} />
            </TabPanel>

            <TabPanel p={4}>
              <LinkAdaptationConfig data={data} />
            </TabPanel>

            <TabPanel p={4}>
              <PowerControlConfig data={data} />
            </TabPanel>

            <TabPanel p={4}>
              <AdvancedConfig data={data} />
            </TabPanel>
          </TabPanels>
        </Tabs>
      </CardBody>
    </Card>
  );
};
