import React from 'react';
import {
  Box,
  Card,
  CardBody,
  HStack,
  Icon,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
  VStack,
  Badge,
  Alert,
  AlertIcon,
  As,
} from '@chakra-ui/react';
import {
  FiArrowUp,
  FiSettings,
  FiRadio,
  FiLayers,
  FiGrid,
  FiCpu,
  FiActivity,
  FiTarget,
  FiZap,
  FiWifi,
  FiPower,
  FiCheckCircle,
  FiXCircle,
} from 'react-icons/fi';
import { CopyButton } from '../../common';

interface ULConfigurationProps {
  data: any;
}

const StatBox = ({
  icon,
  label,
  value,
  colorScheme,
  copyable = false,
}: {
  icon: As;
  label: string;
  value: string | number;
  colorScheme: string;
  copyable?: boolean;
}) => (
  <Stat
    p={3}
    bg={`${colorScheme}.50`}
    borderRadius="md"
    textAlign="center"
    borderWidth="1px"
    borderColor={`${colorScheme}.100`}
  >
    <StatLabel fontSize="sm" color={`${colorScheme}.600`} fontWeight="medium">
      <HStack justify="center" spacing={1.5}>
        <Icon as={icon} boxSize={4} />
        <Text>{label}</Text>
      </HStack>
    </StatLabel>
    <HStack justify="center" spacing={2} mt={1}>
      <StatNumber fontSize="xl" color={`${colorScheme}.800`} fontFamily="mono">
        {value}
      </StatNumber>
      {copyable && <CopyButton text={String(value)} label={label} />}
    </HStack>
  </Stat>
);

const ULCommonConfig = ({ ulCmn }: { ulCmn: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 3 }} spacing={4}>
      <StatBox
        icon={FiSettings}
        label="Additional UL Coreset"
        value={ulCmn?.addnlUlCoresetEnable ? 'Enabled' : 'Disabled'}
        colorScheme={ulCmn?.addnlUlCoresetEnable ? 'green' : 'red'}
      />
      <StatBox
        icon={FiLayers}
        label="Sub-Carrier Configs"
        value={ulCmn?.subCarrierCfg?.length || 0}
        colorScheme="purple"
      />
      <StatBox icon={FiGrid} label="UL BWP Configs" value={ulCmn?.ulBwpCfg?.length || 0} colorScheme="blue" />
    </SimpleGrid>

    {/* Sub-Carrier Configuration */}
    {ulCmn?.subCarrierCfg && ulCmn.subCarrierCfg.length > 0 && (
      <Box p={4} borderWidth="1px" borderRadius="lg" bg="purple.50" borderColor="purple.200">
        <HStack mb={3}>
          <Icon as={FiRadio} color="purple.500" />
          <Text fontWeight="bold" fontSize="md" color="purple.700">
            Sub-Carrier Configuration
          </Text>
        </HStack>
        {ulCmn.subCarrierCfg.map((config: any, index: number) => (
          <Box key={index} p={3} bg="white" borderRadius="md" mb={2}>
            <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Config ID
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {config.id}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Carrier BW
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {config.carrierBw}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  SCS
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {config.subCarrierSpacing}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Offset
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {config.offsetToCarrier}
                </Text>
              </Box>
            </SimpleGrid>
          </Box>
        ))}
      </Box>
    )}
  </VStack>
);

const ULBWPConfig = ({ ulBwpCfg }: { ulBwpCfg: any[] }) => (
  <VStack spacing={4} align="stretch">
    {ulBwpCfg?.map((bwp: any, index: number) => (
      <Box key={index} p={4} borderWidth="1px" borderRadius="lg" bg="blue.50" borderColor="blue.200">
        <HStack mb={3}>
          <Icon as={FiSettings} color="blue.500" />
          <Text fontWeight="bold" fontSize="md" color="blue.700">
            BWP Configuration {bwp.id}
          </Text>
        </HStack>

        {/* Basic BWP Info */}
        <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4} mb={4}>
          <StatBox icon={FiGrid} label="BWP ID" value={bwp.id} colorScheme="blue" />
          <StatBox icon={FiRadio} label="MU" value={bwp.mu || 'N/A'} colorScheme="green" />
          <StatBox icon={FiLayers} label="Number of RBs" value={bwp.numRb || 'N/A'} colorScheme="purple" />
          <StatBox icon={FiCpu} label="Cyclic Prefix" value={bwp.cyclicPrefix || 'N/A'} colorScheme="orange" />
        </SimpleGrid>

        {/* PUCCH Configuration */}
        <Box p={3} bg="white" borderRadius="md" mb={3}>
          <Text fontWeight="bold" fontSize="sm" color="gray.700" mb={2}>
            PUCCH Configuration
          </Text>
          <SimpleGrid columns={{ base: 2, md: 3 }} spacing={3}>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                Group Hopping
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {bwp.pucchGrpHopping}
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                Resource Common
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {bwp.pucchResourceCmn}
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                Common Config Present
              </Text>
              <Badge colorScheme={bwp.pucchCmnCfgPres ? 'green' : 'red'} variant="solid">
                <Icon as={bwp.pucchCmnCfgPres ? FiCheckCircle : FiXCircle} mr={1} />
                {bwp.pucchCmnCfgPres ? 'Yes' : 'No'}
              </Badge>
            </Box>
          </SimpleGrid>
        </Box>

        {/* PUSCH Configuration */}
        <Box p={3} bg="white" borderRadius="md" mb={3}>
          <Text fontWeight="bold" fontSize="sm" color="gray.700" mb={2}>
            PUSCH Configuration
          </Text>
          <SimpleGrid columns={{ base: 2, md: 3 }} spacing={3}>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                Transform Precoder
              </Text>
              <Badge colorScheme={bwp.puschTransformPrecoder ? 'green' : 'red'} variant="solid">
                <Icon as={bwp.puschTransformPrecoder ? FiCheckCircle : FiXCircle} mr={1} />
                {bwp.puschTransformPrecoder ? 'Enabled' : 'Disabled'}
              </Badge>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                Codebook Subset
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {bwp.puschCodebookSubset}
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                TX Config
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {bwp.puschTxCfg}
              </Text>
            </Box>
          </SimpleGrid>
        </Box>

        {/* Power Configuration */}
        {bwp.puschPwrCfg && (
          <Box p={3} bg="white" borderRadius="md" mb={3}>
            <Text fontWeight="bold" fontSize="sm" color="gray.700" mb={2}>
              Power Configuration
            </Text>
            <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  P0 Nominal
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {bwp.puschPwrCfg.p0nominal} dBm
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Alpha
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {bwp.puschPwrCfg.alpha}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  MSG3 Alpha
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {bwp.puschPwrCfg.msg3alpha}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Accumulated
                </Text>
                <Badge colorScheme={bwp.puschPwrCfg.isAccumulated ? 'green' : 'red'} variant="solid">
                  <Icon as={bwp.puschPwrCfg.isAccumulated ? FiCheckCircle : FiXCircle} mr={1} />
                  {bwp.puschPwrCfg.isAccumulated ? 'Yes' : 'No'}
                </Badge>
              </Box>
            </SimpleGrid>
          </Box>
        )}

        {/* RACH Configuration */}
        {bwp.rachCfgInfo && (
          <Box p={3} bg="white" borderRadius="md" mb={3}>
            <Text fontWeight="bold" fontSize="sm" color="gray.700" mb={2}>
              RACH Configuration
            </Text>
            <SimpleGrid columns={{ base: 2, md: 3 }} spacing={3} mb={3}>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  CB Preambles per SSB
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {bwp.rachCfgInfo.cbPreamblePerSsb}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Content Resolution Timer
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {bwp.rachCfgInfo.contentResolutionTmr}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Max MSG3 TX
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {bwp.rachCfgInfo.maxMsg3Tx}
                </Text>
              </Box>
            </SimpleGrid>

            {bwp.rachCfgInfo.rachGenCfg && (
              <Box p={2} bg="gray.50" borderRadius="md">
                <Text fontSize="xs" fontWeight="bold" color="gray.600" mb={1}>
                  General RACH Config
                </Text>
                <SimpleGrid columns={{ base: 2, md: 4 }} spacing={2}>
                  <Box>
                    <Text fontSize="xs" color="gray.500">
                      PRACH Config Index
                    </Text>
                    <Text fontSize="xs" fontWeight="bold">
                      {bwp.rachCfgInfo.rachGenCfg.prachCfgIdx}
                    </Text>
                  </Box>
                  <Box>
                    <Text fontSize="xs" color="gray.500">
                      MSG1 FDM
                    </Text>
                    <Text fontSize="xs" fontWeight="bold">
                      {bwp.rachCfgInfo.rachGenCfg.msg1Fdm}
                    </Text>
                  </Box>
                  <Box>
                    <Text fontSize="xs" color="gray.500">
                      Preamble TX Max
                    </Text>
                    <Text fontSize="xs" fontWeight="bold">
                      {bwp.rachCfgInfo.rachGenCfg.preambleTransMax}
                    </Text>
                  </Box>
                  <Box>
                    <Text fontSize="xs" color="gray.500">
                      Power Ramping Step
                    </Text>
                    <Text fontSize="xs" fontWeight="bold">
                      {bwp.rachCfgInfo.rachGenCfg.pwrRampingStep}
                    </Text>
                  </Box>
                </SimpleGrid>
              </Box>
            )}
          </Box>
        )}

        {/* SRS Configuration */}
        {bwp.srsCfg && (
          <Box p={3} bg="white" borderRadius="md" mb={3}>
            <Text fontWeight="bold" fontSize="sm" color="gray.700" mb={2}>
              SRS Configuration
            </Text>
            <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Alpha
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {bwp.srsCfg.alpha}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Max SRS UE per Slot
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {bwp.srsCfg.maxSrsUePerSlot}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Number of RBs
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {bwp.srsCfg.numRBs}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Resource Type
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {bwp.srsCfg.srsResourceType}
                </Text>
              </Box>
            </SimpleGrid>
          </Box>
        )}
      </Box>
    ))}
  </VStack>
);

const ULDMRSConfig = ({ ulDmrs }: { ulDmrs: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiRadio} label="DMRS Type" value={ulDmrs?.dmrsTypeUl || 'N/A'} colorScheme="cyan" />
      <StatBox
        icon={FiLayers}
        label="Additional Positions"
        value={ulDmrs?.dmrsAdditionalPosUl || 'N/A'}
        colorScheme="teal"
      />
      <StatBox icon={FiGrid} label="Max Length" value={ulDmrs?.maxLenUl || 'N/A'} colorScheme="blue" />
      <StatBox icon={FiCpu} label="CDM Groups" value={ulDmrs?.numCdmGrpWithoutData || 'N/A'} colorScheme="green" />
    </SimpleGrid>

    {/* Transform Precoding Configuration */}
    <Box p={4} borderWidth="1px" borderRadius="lg" bg="cyan.50" borderColor="cyan.200">
      <HStack mb={3}>
        <Icon as={FiZap} color="cyan.500" />
        <Text fontWeight="bold" fontSize="md" color="cyan.700">
          Transform Precoding Configuration
        </Text>
      </HStack>

      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
        <Box p={3} bg="white" borderRadius="md">
          <Text fontWeight="bold" fontSize="sm" color="gray.700" mb={2}>
            Disabled Configuration
          </Text>
          <Badge
            colorScheme={ulDmrs?.transPrecodingDisabledUl?.istransPrecodingDisabled ? 'green' : 'red'}
            variant="solid"
          >
            <Icon as={ulDmrs?.transPrecodingDisabledUl?.istransPrecodingDisabled ? FiCheckCircle : FiXCircle} mr={1} />
            {ulDmrs?.transPrecodingDisabledUl?.istransPrecodingDisabled ? 'Disabled' : 'Not Disabled'}
          </Badge>
        </Box>

        <Box p={3} bg="white" borderRadius="md">
          <Text fontWeight="bold" fontSize="sm" color="gray.700" mb={2}>
            Enabled Configuration
          </Text>
          <VStack align="start" spacing={2}>
            <Badge
              colorScheme={ulDmrs?.transPrecodingEnabledUl?.istransPrecodingEnabled ? 'green' : 'red'}
              variant="solid"
            >
              <Icon as={ulDmrs?.transPrecodingEnabledUl?.istransPrecodingEnabled ? FiCheckCircle : FiXCircle} mr={1} />
              {ulDmrs?.transPrecodingEnabledUl?.istransPrecodingEnabled ? 'Enabled' : 'Not Enabled'}
            </Badge>
            <Text fontSize="xs" color="gray.600">
              nPUSCH ID: {ulDmrs?.transPrecodingEnabledUl?.nPUSCHId || 'N/A'}
            </Text>
            <Text fontSize="xs" color="gray.600">
              Seq Group Hopping: {ulDmrs?.transPrecodingEnabledUl?.seqGrpHopping || 'N/A'}
            </Text>
            <Text fontSize="xs" color="gray.600">
              Seq Hopping: {ulDmrs?.transPrecodingEnabledUl?.seqHopping || 'N/A'}
            </Text>
          </VStack>
        </Box>
      </SimpleGrid>
    </Box>
  </VStack>
);

export const ULConfiguration: React.FC<ULConfigurationProps> = ({ data }) => {
  if (!data) {
    return (
      <Alert status="info" borderRadius="lg">
        <AlertIcon />
        <Text>No uplink configuration data available.</Text>
      </Alert>
    );
  }

  const { ulCmn, ulDmrs } = data;

  return (
    <Card borderWidth="1px" borderRadius="lg" overflow="hidden" variant="outline" border="none">
      <CardBody px={2} py={0}>
        <Tabs variant="soft-rounded" colorScheme="green" align="start">
          <TabList mt={4} mx={4} flexWrap="wrap" gap={2}>
            <Tab
              shadow="md"
              _focus={{ boxShadow: 'none' }}
              _hover={{ bg: 'green.200', transform: 'translateY(-2px)' }}
              transition="all 0.2s ease"
            >
              <HStack>
                <Icon as={FiArrowUp} boxSize={5} color="green.500" />
                <Text fontWeight="medium" fontSize="md">
                  UL Common
                </Text>
              </HStack>
            </Tab>

            {ulCmn?.ulBwpCfg && ulCmn.ulBwpCfg.length > 0 && (
              <Tab
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'blue.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
              >
                <HStack>
                  <Icon as={FiGrid} boxSize={5} color="blue.500" />
                  <Text fontWeight="medium" fontSize="md">
                    BWP Configuration
                  </Text>
                </HStack>
              </Tab>
            )}

            <Tab
              shadow="md"
              _focus={{ boxShadow: 'none' }}
              _hover={{ bg: 'cyan.200', transform: 'translateY(-2px)' }}
              transition="all 0.2s ease"
            >
              <HStack>
                <Icon as={FiRadio} boxSize={5} color="cyan.500" />
                <Text fontWeight="medium" fontSize="md">
                  DMRS
                </Text>
              </HStack>
            </Tab>
          </TabList>

          <TabPanels mt={4} mx={4}>
            <TabPanel p={4}>
              <ULCommonConfig ulCmn={ulCmn} />
            </TabPanel>

            {ulCmn?.ulBwpCfg && ulCmn.ulBwpCfg.length > 0 && (
              <TabPanel p={4}>
                <ULBWPConfig ulBwpCfg={ulCmn.ulBwpCfg} />
              </TabPanel>
            )}

            <TabPanel p={4}>
              <ULDMRSConfig ulDmrs={ulDmrs} />
            </TabPanel>
          </TabPanels>
        </Tabs>
      </CardBody>
    </Card>
  );
};
