import React from 'react';
import {
  Box,
  Card,
  CardBody,
  HStack,
  Icon,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
  VStack,
  Badge,
  Alert,
  AlertIcon,
  As,
} from '@chakra-ui/react';
import {
  FiCpu,
  FiSettings,
  FiRadio,
  FiLayers,
  FiGrid,
  FiActivity,
  FiTarget,
  FiZap,
  FiWifi,
  FiCheckCircle,
  FiXCircle,
  FiClock,
  FiBarChart2,
  FiTrendingUp,
  FiSliders,
} from 'react-icons/fi';
import { CopyButton, getConfigStatus } from '../../common';

interface AdvancedConfigurationProps {
  data: {
    l1Info?: any;
    ltenrCoTraficPatternType?: any;
    measGap?: any;
    pCCH?: any;
    pfs?: any;
  };
}

const StatBox = ({
  icon,
  label,
  value,
  colorScheme,
  copyable = false,
  unit = '',
  isStatusValue = false,
}: {
  icon: As;
  label: string;
  value: string | number | boolean;
  colorScheme: string;
  copyable?: boolean;
  unit?: string;
  isStatusValue?: boolean;
}) => {
  const { status_color, status_icon, status_text } = getConfigStatus(value.toString());

  return (
    <Stat
      p={3}
      bg={`${colorScheme}.50`}
      borderRadius="md"
      textAlign="center"
      borderWidth="1px"
      borderColor={`${colorScheme}.100`}
    >
      <StatLabel fontSize="sm" color={`${colorScheme}.600`} fontWeight="medium">
        <HStack justify="center" spacing={1.5}>
          <Icon as={icon} boxSize={4} />
          <Text>{label}</Text>
        </HStack>
      </StatLabel>
      <HStack justify="center" spacing={2} mt={1}>
        <StatNumber fontSize="xl" color={`${colorScheme}.800`} fontFamily="mono">
          {isStatusValue ? (
            <Badge colorScheme={status_color} variant="solid" px={3} py={1} borderRadius="full">
              <HStack spacing={1}>
                <Icon as={status_icon} boxSize={4} />
                <Text>{status_text}</Text>
              </HStack>
            </Badge>
          ) : (
            `${value}${unit}`
          )}
        </StatNumber>
        {copyable && <CopyButton text={String(value)} label={label} />}
      </HStack>
    </Stat>
  );
};

const L1InfoConfig = ({ l1Info }: { l1Info: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiCpu} label="nDL Carrier K0" value={l1Info?.nDlCarrierK0 || 0} colorScheme="blue" />
      <StatBox icon={FiRadio} label="nUL Carrier K0" value={l1Info?.nUlCarrierK0 || 0} colorScheme="green" />
      <StatBox icon={FiSettings} label="SS0 Index" value={l1Info?.mibParams?.ss0Idx || 0} colorScheme="purple" />
      <StatBox
        icon={FiCheckCircle}
        label="Intra Freq Reselection"
        value={l1Info?.mibParams?.intraFreqReselection === 'allowed'}
        colorScheme="orange"
        isStatusValue={true}
      />
    </SimpleGrid>

    {/* SIB1 Parameters */}
    {l1Info?.sib1Params && (
      <Box p={4} borderWidth="1px" borderRadius="lg" bg="blue.50" borderColor="blue.200">
        <HStack mb={3}>
          <Icon as={FiSettings} color="blue.500" />
          <Text fontWeight="bold" fontSize="md" color="blue.700">
            SIB1 Parameters
          </Text>
        </HStack>
        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={3}>
          <Box>
            <Text fontSize="xs" color="gray.500" fontWeight="medium">
              eCall Over IMS Support
            </Text>
            <Badge
              colorScheme={l1Info.sib1Params.eCallOverIMSSupport ? 'green' : 'red'}
              variant="solid"
              px={3}
              py={1}
              borderRadius="full"
            >
              <HStack spacing={1}>
                <Icon as={l1Info.sib1Params.eCallOverIMSSupport ? FiCheckCircle : FiXCircle} boxSize={4} />
                <Text>{l1Info.sib1Params.eCallOverIMSSupport ? 'Supported' : 'Not Supported'}</Text>
              </HStack>
            </Badge>
          </Box>
          <Box>
            <Text fontSize="xs" color="gray.500" fontWeight="medium">
              IMS Emergency Support
            </Text>
            <Badge
              colorScheme={l1Info.sib1Params.imsEmergencySupport ? 'green' : 'red'}
              variant="solid"
              px={3}
              py={1}
              borderRadius="full"
            >
              <HStack spacing={1}>
                <Icon as={l1Info.sib1Params.imsEmergencySupport ? FiCheckCircle : FiXCircle} boxSize={4} />
                <Text>{l1Info.sib1Params.imsEmergencySupport ? 'Supported' : 'Not Supported'}</Text>
              </HStack>
            </Badge>
          </Box>
        </SimpleGrid>
      </Box>
    )}
  </VStack>
);

const LTENRCoexistenceConfig = ({ coexistence }: { coexistence: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox
        icon={FiZap}
        label="LTE-NR Co Enable"
        value={coexistence?.isLteNrCoEnable || false}
        colorScheme="purple"
        isStatusValue={true}
      />
      <StatBox
        icon={FiRadio}
        label="Sharing Type"
        value={coexistence?.sharingType?.replace('_', ' ') || 'N/A'}
        colorScheme="blue"
      />
      <StatBox icon={FiActivity} label="Activate SFN" value={coexistence?.activateSfn || 0} colorScheme="green" />
      <StatBox icon={FiGrid} label="Bit Width Size" value={coexistence?.bitWidthSize || 0} colorScheme="orange" />
    </SimpleGrid>

    <Box p={4} borderWidth="1px" borderRadius="lg" bg="purple.50" borderColor="purple.200">
      <HStack mb={3}>
        <Icon as={FiBarChart2} color="purple.500" />
        <Text fontWeight="bold" fontSize="md" color="purple.700">
          Traffic Pattern Configuration
        </Text>
      </HStack>
      <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            DL Traffic Pattern
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {coexistence?.dlTrafficPattern || 'N/A'}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            UL Traffic Pattern
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {coexistence?.ulTrafficPattern || 'N/A'}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            LTE PUCCH RB in Edge
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {coexistence?.ltePucchRbInEdge || 0}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            MB SFN Control Sym
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {coexistence?.mbSfnControlSym || 0}
          </Text>
        </Box>
      </SimpleGrid>
    </Box>
  </VStack>
);

const MeasGapConfig = ({ measGap }: { measGap: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiClock} label="Gap Length" value={measGap?.measGapLength || 'N/A'} colorScheme="cyan" />
      <StatBox icon={FiTarget} label="Pattern ID" value={measGap?.measGapPatternId || 0} colorScheme="teal" />
      <StatBox
        icon={FiActivity}
        label="Rep Periodicity"
        value={measGap?.measGapRepPeriodicity || 'N/A'}
        colorScheme="blue"
      />
      <StatBox icon={FiZap} label="Timing Advance" value={measGap?.measGapTimingAdvance || 'N/A'} colorScheme="green" />
    </SimpleGrid>
  </VStack>
);

const PCCHConfig = ({ pcch }: { pcch: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiClock} label="Default Paging Cycle" value={pcch?.defaultPagCycle || 'N/A'} colorScheme="pink" />
      <StatBox icon={FiSettings} label="Monitor Type" value={pcch?.monitorType || 'N/A'} colorScheme="indigo" />
      <StatBox icon={FiGrid} label="N Parameter" value={pcch?.n || 'N/A'} colorScheme="orange" />
      <StatBox icon={FiTarget} label="Num of PO per PF" value={pcch?.numOfPoPerPf || 0} colorScheme="purple" />
    </SimpleGrid>

    <Box p={4} borderWidth="1px" borderRadius="lg" bg="pink.50" borderColor="pink.200">
      <HStack mb={3}>
        <Icon as={FiSettings} color="pink.500" />
        <Text fontWeight="bold" fontSize="md" color="pink.700">
          Paging Configuration Details
        </Text>
      </HStack>
      <SimpleGrid columns={{ base: 2, md: 3 }} spacing={3}>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            NS Parameter
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {pcch?.ns || 'N/A'}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            Occasion of PO
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {pcch?.occasionOfPo?.join(', ') || 'N/A'}
          </Text>
        </Box>
      </SimpleGrid>
    </Box>
  </VStack>
);

const PFSConfig = ({ pfs }: { pfs: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiTrendingUp} label="Fairness Coefficient" value={pfs?.fairnessCoeff || 0} colorScheme="green" />
      <StatBox icon={FiBarChart2} label="BO Coefficient" value={pfs?.boCoeff || 0} colorScheme="blue" />
      <StatBox icon={FiZap} label="Min DL Bit Rate" value={pfs?.minDlBitRate || 0} colorScheme="purple" />
      <StatBox
        icon={FiActivity}
        label="Max VoNR Packet Size"
        value={pfs?.maxVonrPktSize || 0}
        unit=" bytes"
        colorScheme="orange"
      />
    </SimpleGrid>

    <Box p={4} borderWidth="1px" borderRadius="lg" bg="green.50" borderColor="green.200">
      <HStack mb={3}>
        <Icon as={FiSliders} color="green.500" />
        <Text fontWeight="bold" fontSize="md" color="green.700">
          PFS Coefficients
        </Text>
      </HStack>
      <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            GBR Served Rate Coeff
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {pfs?.gbrServedRateCoeff || 0}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            MBR Served Rate Coeff
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {pfs?.mbrServedRateCoeff || 0}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            NR 5QI Coeff
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {pfs?.nr5qiCoeff || 0}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            PDB Coeff
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {pfs?.pdbCoeff || 0}
          </Text>
        </Box>
      </SimpleGrid>
    </Box>

    <Box p={4} borderWidth="1px" borderRadius="lg" bg="blue.50" borderColor="blue.200">
      <HStack mb={3}>
        <Icon as={FiClock} color="blue.500" />
        <Text fontWeight="bold" fontSize="md" color="blue.700">
          Timer Configuration
        </Text>
      </HStack>
      <SimpleGrid columns={{ base: 2, md: 2 }} spacing={3}>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            DL Proactive Grant Timer
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {pfs?.dlProactiveGrantTmr || 'N/A'}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            UL Proactive Grant Timer
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {pfs?.ulProactiveGrantTmr || 'N/A'}
          </Text>
        </Box>
      </SimpleGrid>
    </Box>
  </VStack>
);

export const AdvancedConfiguration: React.FC<AdvancedConfigurationProps> = ({ data }) => {
  if (!data) {
    return (
      <Alert status="info" borderRadius="lg">
        <AlertIcon />
        <Text>No advanced configuration data available.</Text>
      </Alert>
    );
  }

  const { l1Info, ltenrCoTraficPatternType, measGap, pCCH, pfs } = data;

  return (
    <Card borderWidth="1px" borderRadius="lg" overflow="hidden" variant="outline" border="none">
      <CardBody px={2} py={0}>
        <Tabs variant="soft-rounded" colorScheme="teal" align="start">
          <TabList mt={4} mx={4} flexWrap="wrap" gap={2}>
            {l1Info && (
              <Tab
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'blue.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
              >
                <HStack>
                  <Icon as={FiCpu} boxSize={5} color="blue.500" />
                  <Text fontWeight="medium" fontSize="md">
                    L1 Info
                  </Text>
                </HStack>
              </Tab>
            )}

            {ltenrCoTraficPatternType && (
              <Tab
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'purple.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
              >
                <HStack>
                  <Icon as={FiRadio} boxSize={5} color="purple.500" />
                  <Text fontWeight="medium" fontSize="md">
                    LTE/NR Coexistence
                  </Text>
                </HStack>
              </Tab>
            )}

            {measGap && (
              <Tab
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'cyan.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
              >
                <HStack>
                  <Icon as={FiClock} boxSize={5} color="cyan.500" />
                  <Text fontWeight="medium" fontSize="md">
                    Measurement Gap
                  </Text>
                </HStack>
              </Tab>
            )}

            {pCCH && (
              <Tab
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'pink.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
              >
                <HStack>
                  <Icon as={FiWifi} boxSize={5} color="pink.500" />
                  <Text fontWeight="medium" fontSize="md">
                    PCCH
                  </Text>
                </HStack>
              </Tab>
            )}

            {pfs && (
              <Tab
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'green.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
              >
                <HStack>
                  <Icon as={FiTrendingUp} boxSize={5} color="green.500" />
                  <Text fontWeight="medium" fontSize="md">
                    PFS
                  </Text>
                </HStack>
              </Tab>
            )}
          </TabList>

          <TabPanels mt={4} mx={4}>
            {l1Info && (
              <TabPanel p={4}>
                <L1InfoConfig l1Info={l1Info} />
              </TabPanel>
            )}

            {ltenrCoTraficPatternType && (
              <TabPanel p={4}>
                <LTENRCoexistenceConfig coexistence={ltenrCoTraficPatternType} />
              </TabPanel>
            )}

            {measGap && (
              <TabPanel p={4}>
                <MeasGapConfig measGap={measGap} />
              </TabPanel>
            )}

            {pCCH && (
              <TabPanel p={4}>
                <PCCHConfig pcch={pCCH} />
              </TabPanel>
            )}

            {pfs && (
              <TabPanel p={4}>
                <PFSConfig pfs={pfs} />
              </TabPanel>
            )}
          </TabPanels>
        </Tabs>
      </CardBody>
    </Card>
  );
};
