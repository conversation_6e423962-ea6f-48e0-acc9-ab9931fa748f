import React from 'react';
import {
  Box,
  Card,
  CardBody,
  HStack,
  Icon,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
  VStack,
  Badge,
  Alert,
  AlertIcon,
  As,
} from '@chakra-ui/react';
import {
  FiClock,
  FiSettings,
  FiRadio,
  FiLayers,
  FiGrid,
  FiActivity,
  FiTarget,
  FiZap,
  FiWifi,
  FiCpu,
  FiSliders,
  FiBarChart2,
  FiCheckCircle,
  FiXCircle,
} from 'react-icons/fi';
import { CopyButton } from '../../common';

interface SystemConfigurationProps {
  data: {
    'si-SchedInfo'?: any;
    tddSlot?: any;
    uePositioning?: any;
    xran?: any;
  };
}

const StatBox = ({
  icon,
  label,
  value,
  colorScheme,
  copyable = false,
  unit = '',
}: {
  icon: As;
  label: string;
  value: string | number;
  colorScheme: string;
  copyable?: boolean;
  unit?: string;
}) => (
  <Stat
    p={3}
    bg={`${colorScheme}.50`}
    borderRadius="md"
    textAlign="center"
    borderWidth="1px"
    borderColor={`${colorScheme}.100`}
  >
    <StatLabel fontSize="sm" color={`${colorScheme}.600`} fontWeight="medium">
      <HStack justify="center" spacing={1.5}>
        <Icon as={icon} boxSize={4} />
        <Text>{label}</Text>
      </HStack>
    </StatLabel>
    <HStack justify="center" spacing={2} mt={1}>
      <StatNumber fontSize="xl" color={`${colorScheme}.800`} fontFamily="mono">
        {value}
        {unit}
      </StatNumber>
      {copyable && <CopyButton text={String(value)} label={label} />}
    </HStack>
  </Stat>
);

const SISchedInfoConfig = ({ siSchedInfo }: { siSchedInfo: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox
        icon={FiClock}
        label="SIB2 Periodicity"
        value={siSchedInfo?.sib2Periodcity?.replace('rf', '') || 'N/A'}
        colorScheme="blue"
      />
      <StatBox
        icon={FiClock}
        label="SIB3 Periodicity"
        value={siSchedInfo?.sib3Periodicity?.replace('rf', '') || 'N/A'}
        colorScheme="green"
      />
      <StatBox
        icon={FiClock}
        label="SIB4 Periodicity"
        value={siSchedInfo?.sib4Periodicity?.replace('rf', '') || 'N/A'}
        colorScheme="purple"
      />
      <StatBox
        icon={FiClock}
        label="SIB5 Periodicity"
        value={siSchedInfo?.sib5Periodicity?.replace('rf', '') || 'N/A'}
        colorScheme="orange"
      />
    </SimpleGrid>

    <Box p={4} borderWidth="1px" borderRadius="lg" bg="blue.50" borderColor="blue.200">
      <HStack mb={3}>
        <Icon as={FiSettings} color="blue.500" />
        <Text fontWeight="bold" fontSize="md" color="blue.700">
          Additional SIB Periodicities
        </Text>
      </HStack>
      <SimpleGrid columns={{ base: 2, md: 3 }} spacing={3}>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            SIB6 Periodicity
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {siSchedInfo?.sib6Periodicity?.replace('rf', '') || 'N/A'}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            SIB7 Periodicity
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {siSchedInfo?.sib7Periodicity?.replace('rf', '') || 'N/A'}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            SIB8 Periodicity
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {siSchedInfo?.sib8Periodicity?.replace('rf', '') || 'N/A'}
          </Text>
        </Box>
      </SimpleGrid>
    </Box>
  </VStack>
);

const TDDSlotConfig = ({ tddSlot }: { tddSlot: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiGrid} label="DL Slots" value={tddSlot?.numDlSlot || 0} colorScheme="blue" />
      <StatBox icon={FiGrid} label="UL Slots" value={tddSlot?.numUlSlot || 0} colorScheme="green" />
      <StatBox icon={FiLayers} label="DL Symbols" value={tddSlot?.numDlSymbol || 0} colorScheme="purple" />
      <StatBox icon={FiLayers} label="UL Symbols" value={tddSlot?.numUlSymbol || 0} colorScheme="orange" />
    </SimpleGrid>

    <Box p={4} borderWidth="1px" borderRadius="lg" bg="cyan.50" borderColor="cyan.200">
      <HStack mb={3}>
        <Icon as={FiActivity} color="cyan.500" />
        <Text fontWeight="bold" fontSize="md" color="cyan.700">
          Pattern 2 Configuration
        </Text>
      </HStack>
      <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            P2 Present
          </Text>
          <Badge colorScheme={tddSlot?.p2Pres ? 'green' : 'red'} variant="solid" px={3} py={1} borderRadius="full">
            <HStack spacing={1}>
              <Icon as={tddSlot?.p2Pres ? FiCheckCircle : FiXCircle} boxSize={4} />
              <Text>{tddSlot?.p2Pres ? 'Yes' : 'No'}</Text>
            </HStack>
          </Badge>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            DL Slots P2
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {tddSlot?.numDlSlotP2 || 0}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            UL Slots P2
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {tddSlot?.numUlSlotP2 || 0}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            DL Symbols P2
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {tddSlot?.numDlSymbolP2 || 0}
          </Text>
        </Box>
      </SimpleGrid>
    </Box>
  </VStack>
);

const UEPositioningConfig = ({ uePositioning }: { uePositioning: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox
        icon={FiTarget}
        label="Cell Center Threshold"
        value={uePositioning?.cellCenterThreshold || 0}
        colorScheme="pink"
      />
      <StatBox
        icon={FiRadio}
        label="Path Loss Offset"
        value={uePositioning?.cellPathLossOffset || 0}
        colorScheme="indigo"
      />
      <StatBox icon={FiBarChart2} label="RSRP Offset" value={uePositioning?.cellRsrpOffset || 0} colorScheme="teal" />
      <StatBox icon={FiZap} label="RSRP Threshold" value={uePositioning?.cellRsrpThreshold || 0} colorScheme="orange" />
    </SimpleGrid>
  </VStack>
);

const XRANConfig = ({ xran }: { xran: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiCpu} label="Radio Type Index" value={xran?.RadioTypeIndex || 0} colorScheme="blue" />
      <StatBox icon={FiWifi} label="O-RU Type" value={xran?.oRuType || 0} colorScheme="green" />
      <StatBox icon={FiGrid} label="Max Sections/Slot" value={xran?.MaxSectionsPerSlot || 0} colorScheme="purple" />
      <StatBox
        icon={FiLayers}
        label="Max Sections/Symbol"
        value={xran?.MaxSectionsPerSymbol || 0}
        colorScheme="orange"
      />
    </SimpleGrid>

    {/* Timing Parameters */}
    <Box p={4} borderWidth="1px" borderRadius="lg" bg="blue.50" borderColor="blue.200">
      <HStack mb={3}>
        <Icon as={FiClock} color="blue.500" />
        <Text fontWeight="bold" fontSize="md" color="blue.700">
          Timing Parameters
        </Text>
      </HStack>
      <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            T1a Max CP DL
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {xran?.T1a_max_cp_dl || 0}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            T1a Max CP UL
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {xran?.T1a_max_cp_ul || 0}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            T2a Max CP DL
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {xran?.T2a_max_cp_dl || 0}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            T2a Max CP UL
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {xran?.T2a_max_cp_ul || 0}
          </Text>
        </Box>
      </SimpleGrid>
    </Box>

    {/* Compression Settings */}
    <Box p={4} borderWidth="1px" borderRadius="lg" bg="green.50" borderColor="green.200">
      <HStack mb={3}>
        <Icon as={FiSliders} color="green.500" />
        <Text fontWeight="bold" fontSize="md" color="green.700">
          Compression Configuration
        </Text>
      </HStack>
      <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            Compression Method
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {xran?.xranCompMethod || 0}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            IQ Width
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {xran?.xraniqWidth || 0}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            PRACH Comp Method
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {xran?.xranPrachCompMethod || 0}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            PRACH IQ Width
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {xran?.xranPrachiqWidth || 0}
          </Text>
        </Box>
      </SimpleGrid>
    </Box>

    {/* PRB Elements Summary */}
    <Box p={4} borderWidth="1px" borderRadius="lg" bg="purple.50" borderColor="purple.200">
      <HStack mb={3}>
        <Icon as={FiGrid} color="purple.500" />
        <Text fontWeight="bold" fontSize="md" color="purple.700">
          PRB Elements Configuration
        </Text>
      </HStack>
      <SimpleGrid columns={{ base: 2, md: 3 }} spacing={3}>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            DL PRB Elements
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {xran?.nPrbElemDl || 0}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            UL PRB Elements
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {xran?.nPrbElemUl || 0}
          </Text>
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            SRS PRB Elements
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {xran?.nPrbElemSrs || 0}
          </Text>
        </Box>
      </SimpleGrid>
    </Box>
  </VStack>
);

export const SystemConfiguration: React.FC<SystemConfigurationProps> = ({ data }) => {
  if (!data) {
    return (
      <Alert status="info" borderRadius="lg">
        <AlertIcon />
        <Text>No system configuration data available.</Text>
      </Alert>
    );
  }

  const { 'si-SchedInfo': siSchedInfo, tddSlot, uePositioning, xran } = data;

  return (
    <Card borderWidth="1px" borderRadius="lg" overflow="hidden" variant="outline" border="none">
      <CardBody px={2} py={0}>
        <Tabs variant="soft-rounded" colorScheme="indigo" align="start">
          <TabList mt={4} mx={4} flexWrap="wrap" gap={2}>
            {siSchedInfo && (
              <Tab
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'blue.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
                _selected={{ bg: 'blue.200', transform: 'translateY(-2px)' }}
              >
                <HStack>
                  <Icon as={FiClock} boxSize={5} color="blue.500" />
                  <Text fontWeight="medium" fontSize="md">
                    SI Scheduling
                  </Text>
                </HStack>
              </Tab>
            )}

            {tddSlot && (
              <Tab
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'cyan.200', transform: 'translateY(-2px)' }}
                _selected={{ bg: 'cyan.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
              >
                <HStack>
                  <Icon as={FiGrid} boxSize={5} color="cyan.500" />
                  <Text fontWeight="medium" fontSize="md">
                    TDD Slot
                  </Text>
                </HStack>
              </Tab>
            )}

            {uePositioning && (
              <Tab
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'pink.200', transform: 'translateY(-2px)' }}
                _selected={{ bg: 'pink.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
              >
                <HStack>
                  <Icon as={FiTarget} boxSize={5} color="pink.500" />
                  <Text fontWeight="medium" fontSize="md">
                    UE Positioning
                  </Text>
                </HStack>
              </Tab>
            )}

            {xran && (
              <Tab
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'purple.200', transform: 'translateY(-2px)' }}
                _selected={{ bg: 'purple.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
              >
                <HStack>
                  <Icon as={FiRadio} boxSize={5} color="purple.500" />
                  <Text fontWeight="medium" fontSize="md">
                    XRAN
                  </Text>
                </HStack>
              </Tab>
            )}
          </TabList>

          <TabPanels mt={4} mx={4}>
            {siSchedInfo && (
              <TabPanel p={4}>
                <SISchedInfoConfig siSchedInfo={siSchedInfo} />
              </TabPanel>
            )}

            {tddSlot && (
              <TabPanel p={4}>
                <TDDSlotConfig tddSlot={tddSlot} />
              </TabPanel>
            )}

            {uePositioning && (
              <TabPanel p={4}>
                <UEPositioningConfig uePositioning={uePositioning} />
              </TabPanel>
            )}

            {xran && (
              <TabPanel p={4}>
                <XRANConfig xran={xran} />
              </TabPanel>
            )}
          </TabPanels>
        </Tabs>
      </CardBody>
    </Card>
  );
};
