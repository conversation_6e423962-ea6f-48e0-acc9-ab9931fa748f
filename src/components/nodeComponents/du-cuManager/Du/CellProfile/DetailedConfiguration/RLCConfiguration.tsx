import React from 'react';
import {
  Box,
  Card,
  CardBody,
  HStack,
  Icon,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
  VStack,
  Badge,
  Alert,
  AlertIcon,
  As,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
} from '@chakra-ui/react';
import { FiLayers, FiSettings, FiGrid, FiTarget, FiHash, FiActivity } from 'react-icons/fi';
import { CopyButton } from '../../common';

interface RLCConfigurationProps {
  data: any;
}

const StatBox = ({
  icon,
  label,
  value,
  colorScheme,
  copyable = false,
}: {
  icon: As;
  label: string;
  value: string | number;
  colorScheme: string;
  copyable?: boolean;
}) => (
  <Stat
    p={3}
    bg={`${colorScheme}.50`}
    borderRadius="md"
    textAlign="center"
    borderWidth="1px"
    borderColor={`${colorScheme}.100`}
  >
    <StatLabel fontSize="sm" color={`${colorScheme}.600`} fontWeight="medium">
      <HStack justify="center" spacing={1.5}>
        <Icon as={icon} boxSize={4} />
        <Text>{label}</Text>
      </HStack>
    </StatLabel>
    <HStack justify="center" spacing={2} mt={1}>
      <StatNumber fontSize="xl" color={`${colorScheme}.800`} fontFamily="mono">
        {value}
      </StatNumber>
      {copyable && <CopyButton text={String(value)} label={label} />}
    </HStack>
  </Stat>
);

const EUTRAQoSTable = ({ eutraQosGrp }: { eutraQosGrp: any[] }) => (
  <Box p={4} borderWidth="1px" borderRadius="lg" borderColor="blue.200">
    <HStack mb={3}>
      <Icon as={FiGrid} color="blue.500" />
      <Text fontWeight="bold" fontSize="md" color="blue.700">
        E-UTRA QoS Groups
      </Text>
    </HStack>

    <TableContainer bg="white" borderRadius="lg" shadow="sm">
      <Table size="sm" variant="simple">
        <Thead bg="blue.50">
          <Tr>
            <Th color="blue.700">
              <HStack>
                <Icon as={FiHash} color="blue.500" boxSize={4} />
                <Text>Group ID</Text>
              </HStack>
            </Th>
            <Th color="blue.700">
              <HStack>
                <Icon as={FiTarget} color="blue.500" boxSize={4} />
                <Text>QCI</Text>
              </HStack>
            </Th>
            <Th color="blue.700">
              <HStack>
                <Icon as={FiActivity} color="blue.500" boxSize={4} />
                <Text>UL LC Priority</Text>
              </HStack>
            </Th>
          </Tr>
        </Thead>
        <Tbody>
          {eutraQosGrp?.map((group, index) => (
            <Tr
              key={index}
              _hover={{
                bg: 'blue.100',
                transform: 'translateY(-5px)',
              }}
              transition="all 0.3s ease"
            >
              <Td>
                <Badge colorScheme="blue" variant="solid" px={3} py={1} borderRadius="full">
                  <HStack spacing={1}>
                    <Icon as={FiHash} boxSize={4} />
                    <Text>{group.id}</Text>
                  </HStack>
                </Badge>
              </Td>
              <Td fontWeight="bold" color="blue.700" fontFamily="mono">
                {group.qci}
              </Td>
              <Td fontWeight="bold" fontFamily="mono">
                {group.ulLcPriority}
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  </Box>
);

const NRQFITable = ({ nrQfiGrp }: { nrQfiGrp: any[] }) => (
  <Box p={4} borderWidth="1px" borderRadius="lg" borderColor="green.200">
    <HStack mb={3}>
      <Icon as={FiTarget} color="green.500" />
      <Text fontWeight="bold" fontSize="md" color="green.700">
        NR QFI Groups
      </Text>
    </HStack>

    <TableContainer bg="white" borderRadius="lg" shadow="sm">
      <Table size="sm" variant="simple">
        <Thead bg="green.50">
          <Tr>
            <Th color="green.700">
              <HStack>
                <Icon as={FiHash} color="green.500" boxSize={4} />
                <Text>Group ID</Text>
              </HStack>
            </Th>
            <Th color="green.700">
              <HStack>
                <Icon as={FiTarget} color="green.500" boxSize={4} />
                <Text>5QI</Text>
              </HStack>
            </Th>
            <Th color="green.700">
              <HStack>
                <Icon as={FiActivity} color="green.500" boxSize={4} />
                <Text>UL LC Priority</Text>
              </HStack>
            </Th>
          </Tr>
        </Thead>
        <Tbody>
          {nrQfiGrp?.map((group, index) => (
            <Tr
              key={index}
              _hover={{
                bg: 'green.100',
                transform: 'translateY(-5px)',
              }}
              transition="all 0.3s ease"
            >
              <Td>
                <Badge colorScheme="green" variant="solid" px={3} py={1} borderRadius="full">
                  <HStack spacing={1}>
                    <Icon as={FiHash} boxSize={4} />
                    <Text>{group.id}</Text>
                  </HStack>
                </Badge>
              </Td>
              <Td fontWeight="bold" color="green.700" fontFamily="mono">
                {group.fiveqi}
              </Td>
              <Td fontWeight="bold" fontFamily="mono">
                {group.ulLcPriority}
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  </Box>
);

const RLCOverview = ({ data }: { data: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiGrid} label="E-UTRA QoS Groups" value={data.eutraQosGrp?.length || 0} colorScheme="blue" />
      <StatBox icon={FiTarget} label="NR QFI Groups" value={data.nrQfiGrp?.length || 0} colorScheme="green" />
      <StatBox
        icon={FiActivity}
        label="Total QoS Configs"
        value={(data.eutraQosGrp?.length || 0) + (data.nrQfiGrp?.length || 0)}
        colorScheme="purple"
      />
      <StatBox
        icon={FiHash}
        label="Max 5QI Value"
        value={Math.max(...(data.nrQfiGrp?.map((g: any) => g.fiveqi) || [0]))}
        colorScheme="orange"
      />
    </SimpleGrid>

    {/* QoS Priority Distribution */}
    <Box p={4} borderWidth="1px" borderRadius="lg" bg="gray.50" borderColor="gray.200">
      <HStack mb={3}>
        <Icon as={FiSettings} color="gray.500" />
        <Text fontWeight="bold" fontSize="md" color="gray.700">
          QoS Configuration Summary
        </Text>
      </HStack>
      <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
        <Box textAlign="center">
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            E-UTRA QCI Range
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {data.eutraQosGrp?.length > 0
              ? `${Math.min(...data.eutraQosGrp.map((g: any) => g.qci))} - ${Math.max(
                  ...data.eutraQosGrp.map((g: any) => g.qci)
                )}`
              : 'N/A'}
          </Text>
        </Box>
        <Box textAlign="center">
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            NR 5QI Range
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {data.nrQfiGrp?.length > 0
              ? `${Math.min(...data.nrQfiGrp.map((g: any) => g.fiveqi))} - ${Math.max(
                  ...data.nrQfiGrp.map((g: any) => g.fiveqi)
                )}`
              : 'N/A'}
          </Text>
        </Box>
        <Box textAlign="center">
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            Common UL Priority
          </Text>
          <Text fontSize="sm" fontWeight="bold">
            {data.eutraQosGrp?.[0]?.ulLcPriority || data.nrQfiGrp?.[0]?.ulLcPriority || 'N/A'}
          </Text>
        </Box>
        <Box textAlign="center">
          <Text fontSize="xs" color="gray.500" fontWeight="medium">
            Total Groups
          </Text>
          <Badge colorScheme="purple" variant="solid" px={3} py={1} borderRadius="full">
            <HStack spacing={1}>
              <Icon as={FiActivity} boxSize={4} />
              <Text>{(data.eutraQosGrp?.length || 0) + (data.nrQfiGrp?.length || 0)}</Text>
            </HStack>
          </Badge>
        </Box>
      </SimpleGrid>
    </Box>
  </VStack>
);

export const RLCConfiguration: React.FC<RLCConfigurationProps> = ({ data }) => {
  if (!data) {
    return (
      <Alert status="info" borderRadius="lg">
        <AlertIcon />
        <Text>No RLC configuration data available.</Text>
      </Alert>
    );
  }

  const { eutraQosGrp, nrQfiGrp } = data;

  return (
    <Card borderWidth="1px" borderRadius="lg" overflow="hidden" variant="outline" border="none">
      <CardBody px={2} py={0}>
        <Tabs variant="soft-rounded" colorScheme="purple" align="start">
          <TabList mt={4} mx={4} flexWrap="wrap" gap={2}>
            <Tab
              shadow="md"
              _focus={{ boxShadow: 'none' }}
              _hover={{ bg: 'purple.200', transform: 'translateY(-2px)' }}
              transition="all 0.2s ease"
            >
              <HStack>
                <Icon as={FiLayers} boxSize={5} color="purple.500" />
                <Text fontWeight="medium" fontSize="md">
                  Overview
                </Text>
              </HStack>
            </Tab>

            {eutraQosGrp && eutraQosGrp.length > 0 && (
              <Tab
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'blue.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
              >
                <HStack>
                  <Icon as={FiGrid} boxSize={5} color="blue.500" />
                  <Text fontWeight="medium" fontSize="md">
                    E-UTRA QoS
                  </Text>
                  <Badge colorScheme="blue" variant="solid" px={3} py={1} borderRadius="full">
                    <HStack spacing={1}>
                      <Icon as={FiGrid} boxSize={4} />
                      <Text>{eutraQosGrp.length}</Text>
                    </HStack>
                  </Badge>
                </HStack>
              </Tab>
            )}

            {nrQfiGrp && nrQfiGrp.length > 0 && (
              <Tab
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'green.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
              >
                <HStack>
                  <Icon as={FiTarget} boxSize={5} color="green.500" />
                  <Text fontWeight="medium" fontSize="md">
                    NR QFI
                  </Text>
                  <Badge colorScheme="green" variant="solid" px={3} py={1} borderRadius="full">
                    <HStack spacing={1}>
                      <Icon as={FiTarget} boxSize={4} />
                      <Text>{nrQfiGrp.length}</Text>
                    </HStack>
                  </Badge>
                </HStack>
              </Tab>
            )}
          </TabList>

          <TabPanels mt={4} mx={4}>
            <TabPanel p={4}>
              <RLCOverview data={data} />
            </TabPanel>

            {eutraQosGrp && eutraQosGrp.length > 0 && (
              <TabPanel p={4}>
                <EUTRAQoSTable eutraQosGrp={eutraQosGrp} />
              </TabPanel>
            )}

            {nrQfiGrp && nrQfiGrp.length > 0 && (
              <TabPanel p={4}>
                <NRQFITable nrQfiGrp={nrQfiGrp} />
              </TabPanel>
            )}
          </TabPanels>
        </Tabs>
      </CardBody>
    </Card>
  );
};
