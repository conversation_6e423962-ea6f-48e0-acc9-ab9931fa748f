import React from 'react';
import {
  Box,
  Card,
  CardBody,
  HStack,
  Icon,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
  VStack,
  Badge,
  Alert,
  AlertIcon,
  As,
} from '@chakra-ui/react';
import {
  FiArrowDown,
  FiArrowUp,
  FiSettings,
  FiRadio,
  FiLayers,
  FiGrid,
  FiCpu,
  FiActivity,
  FiTarget,
  FiZap,
  FiWifi,
  FiPower,
  FiCheckCircle,
  FiXCircle,
  FiBarChart2,
  FiDownload,
  FiUpload,
} from 'react-icons/fi';
import { CopyButton, getConfigStatus } from '../../common';

interface LinkConfigurationProps {
  data: {
    dlConfiguration?: any;
    ulConfiguration?: any;
  };
}

{
  /* <Badge
colorScheme={ulDmrs?.transPrecodingDisabledUl?.istransPrecodingDisabled ? 'green' : 'red'}
variant="solid"
px={3}
py={1}
borderRadius="full"
>
<HStack spacing={1}>
  <Icon
    as={ulDmrs?.transPrecodingDisabledUl?.istransPrecodingDisabled ? FiCheckCircle : FiXCircle}
    boxSize={4}
  />
  <Text>{ulDmrs?.transPrecodingDisabledUl?.istransPrecodingDisabled ? 'Disabled' : 'Not Disabled'}</Text>
</HStack>
</Badge> */
}

const StatBox = ({
  icon,
  label,
  value,
  colorScheme,
  copyable = false,
  unit = '',
  isValueStatus = false,
}: {
  icon: As;
  label: string;
  value: string | number | boolean;
  colorScheme: string;
  copyable?: boolean;
  unit?: string;
  isValueStatus?: boolean;
}) => {
  const { status_color, status_icon, status_text } = getConfigStatus(value.toString());
  return (
    <Stat
      p={3}
      bg={`${colorScheme}.50`}
      borderRadius="md"
      textAlign="center"
      borderWidth="1px"
      borderColor={`${colorScheme}.100`}
    >
      <StatLabel fontSize="sm" color={`${colorScheme}.600`} fontWeight="medium">
        <HStack justify="center" spacing={1.5}>
          <Icon as={icon} boxSize={4} />
          <Text>{label}</Text>
        </HStack>
      </StatLabel>
      <HStack justify="center" spacing={2} mt={1}>
        <StatNumber fontSize="xl" color={`${colorScheme}.800`} fontFamily="mono">
          {isValueStatus ? (
            <Badge colorScheme={status_color} variant="solid" px={3} py={1} borderRadius="full">
              <HStack spacing={1}>
                <Icon as={status_icon} boxSize={4} />
                <Text>{status_text}</Text>
              </HStack>
            </Badge>
          ) : (
            `${value}${unit}`
          )}
        </StatNumber>
        {copyable && <CopyButton text={String(value)} label={label} />}
      </HStack>
    </Stat>
  );
};

const DLCommonConfig = ({ dlCmn }: { dlCmn: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 3 }} spacing={4}>
      <StatBox
        icon={FiCpu}
        label="BWP Inactivity Timer"
        value={dlCmn?.bwpInactivityTimer || 'N/A'}
        colorScheme="blue"
      />
      <StatBox icon={FiGrid} label="Reserved CCE" value={dlCmn?.numCceRsvdForCmnPdcch || 0} colorScheme="green" />
      <StatBox
        icon={FiLayers}
        label="Sub-Carrier Configs"
        value={dlCmn?.subCarrierCfg?.length || 0}
        colorScheme="purple"
      />
    </SimpleGrid>

    {/* BWP Configuration */}
    {dlCmn?.dlBwp && dlCmn.dlBwp.length > 0 && (
      <Box p={4} borderWidth="1px" borderRadius="lg" bg="blue.50" borderColor="blue.200">
        <HStack mb={3}>
          <Icon as={FiSettings} color="blue.500" />
          <Text fontWeight="bold" fontSize="md" color="blue.700">
            Bandwidth Part (BWP) Configuration
          </Text>
        </HStack>
        {dlCmn.dlBwp.map((bwp: any, index: number) => (
          <Box key={index} p={3} bg="white" borderRadius="md" mb={2}>
            <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3} mb={3}>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  BWP ID
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {bwp.id}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  MU
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {bwp.mu}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Number of RBs
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {bwp.numRb}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Cyclic Prefix
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {bwp.cyclicPrefix}
                </Text>
              </Box>
            </SimpleGrid>

            {/* Control Resource Set Search Space Configuration */}
            {bwp.cntrlResourceSetSearchSpaceCfg && (
              <Box p={2} bg="gray.50" borderRadius="md">
                <Text fontSize="xs" fontWeight="bold" color="gray.600" mb={2}>
                  Control Resource Set Search Space Config
                </Text>
                <SimpleGrid columns={{ base: 2, md: 5 }} spacing={2}>
                  <Box>
                    <Text fontSize="xs" color="gray.500">
                      Aggr Lvl 1
                    </Text>
                    <Text fontSize="xs" fontWeight="bold">
                      {bwp.cntrlResourceSetSearchSpaceCfg.aggrLvl1Candidates}
                    </Text>
                  </Box>
                  <Box>
                    <Text fontSize="xs" color="gray.500">
                      Aggr Lvl 2
                    </Text>
                    <Text fontSize="xs" fontWeight="bold">
                      {bwp.cntrlResourceSetSearchSpaceCfg.aggrLvl2Candidates}
                    </Text>
                  </Box>
                  <Box>
                    <Text fontSize="xs" color="gray.500">
                      Aggr Lvl 4
                    </Text>
                    <Text fontSize="xs" fontWeight="bold">
                      {bwp.cntrlResourceSetSearchSpaceCfg.aggrLvl4Candidates}
                    </Text>
                  </Box>
                  <Box>
                    <Text fontSize="xs" color="gray.500">
                      Aggr Lvl 8
                    </Text>
                    <Text fontSize="xs" fontWeight="bold">
                      {bwp.cntrlResourceSetSearchSpaceCfg.aggrLvl8Candidates}
                    </Text>
                  </Box>
                  <Box>
                    <Text fontSize="xs" color="gray.500">
                      Aggr Lvl 16
                    </Text>
                    <Text fontSize="xs" fontWeight="bold">
                      {bwp.cntrlResourceSetSearchSpaceCfg.aggrLvl16Candidates}
                    </Text>
                  </Box>
                </SimpleGrid>

                {bwp.cntrlResourceSetSearchSpaceCfg.type3SearchSpaceCfg && (
                  <Box mt={2} p={2} bg="white" borderRadius="md">
                    <Text fontSize="xs" fontWeight="bold" color="gray.600" mb={1}>
                      Type 3 Search Space Config
                    </Text>
                    <SimpleGrid columns={{ base: 2, md: 3 }} spacing={2}>
                      <Box>
                        <Text fontSize="xs" color="gray.500">
                          Enabled
                        </Text>
                        <Badge
                          colorScheme={
                            bwp.cntrlResourceSetSearchSpaceCfg.type3SearchSpaceCfg.enableType3SearchSpace
                              ? 'green'
                              : 'red'
                          }
                          variant="solid"
                          size="sm"
                        >
                          {bwp.cntrlResourceSetSearchSpaceCfg.type3SearchSpaceCfg.enableType3SearchSpace ? 'Yes' : 'No'}
                        </Badge>
                      </Box>
                      <Box>
                        <Text fontSize="xs" color="gray.500">
                          Avg Aggr Level
                        </Text>
                        <Text fontSize="xs" fontWeight="bold">
                          {bwp.cntrlResourceSetSearchSpaceCfg.type3SearchSpaceCfg.avgAggregationLvl}
                        </Text>
                      </Box>
                      <Box>
                        <Text fontSize="xs" color="gray.500">
                          Configurable
                        </Text>
                        <Badge
                          colorScheme={
                            bwp.cntrlResourceSetSearchSpaceCfg.type3SearchSpaceCfg.isCandidatesPerAggrLvlConfigurable
                              ? 'green'
                              : 'red'
                          }
                          variant="solid"
                          size="sm"
                        >
                          {bwp.cntrlResourceSetSearchSpaceCfg.type3SearchSpaceCfg.isCandidatesPerAggrLvlConfigurable
                            ? 'Yes'
                            : 'No'}
                        </Badge>
                      </Box>
                    </SimpleGrid>
                  </Box>
                )}
              </Box>
            )}
          </Box>
        ))}
      </Box>
    )}

    {/* Sub-Carrier Configuration */}
    {dlCmn?.subCarrierCfg && dlCmn.subCarrierCfg.length > 0 && (
      <Box p={4} borderWidth="1px" borderRadius="lg" bg="purple.50" borderColor="purple.200">
        <HStack mb={3}>
          <Icon as={FiRadio} color="purple.500" />
          <Text fontWeight="bold" fontSize="md" color="purple.700">
            Sub-Carrier Configuration
          </Text>
        </HStack>
        {dlCmn.subCarrierCfg.map((config: any, index: number) => (
          <Box key={index} p={3} bg="white" borderRadius="md" mb={2}>
            <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Config ID
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {config.id}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Carrier BW
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {config.carrierBw}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  SCS
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {config.subCarrierSpacing}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Offset
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {config.offsetToCarrier}
                </Text>
              </Box>
            </SimpleGrid>
          </Box>
        ))}
      </Box>
    )}
  </VStack>
);

const DLCAConfig = ({ dlCA }: { dlCA: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiTarget} label="CA Distribution Type" value={dlCA?.CaBoDistType || 'N/A'} colorScheme="orange" />
      <StatBox
        icon={FiActivity}
        label="SCell Active Threshold"
        value={dlCA?.ScellActiveThr || 'N/A'}
        colorScheme="green"
      />
      <StatBox
        icon={FiZap}
        label="SCell Active BO Threshold"
        value={dlCA?.ScellActiveBoThr || 'N/A'}
        colorScheme="blue"
      />
      <StatBox icon={FiSettings} label="CA Activation Type" value={dlCA?.caActType || 'N/A'} colorScheme="purple" />
    </SimpleGrid>

    <Box p={4} borderWidth="1px" borderRadius="lg" bg="orange.50" borderColor="orange.200">
      <Text fontSize="sm" color="gray.600" mb={2}>
        <strong>SCell Deactivation Threshold:</strong> {dlCA?.ScellDeActiveThr || 'N/A'}
      </Text>
    </Box>
  </VStack>
);

const DLDMRSConfig = ({ dlDmrs }: { dlDmrs: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiRadio} label="DMRS Type" value={dlDmrs?.dmrsTypeDl || 'N/A'} colorScheme="cyan" />
      <StatBox
        icon={FiLayers}
        label="Additional Positions"
        value={dlDmrs?.dmrsAdditionalPosDl || 'N/A'}
        colorScheme="teal"
      />
      <StatBox icon={FiGrid} label="Max Length" value={dlDmrs?.maxLenDl || 'N/A'} colorScheme="blue" />
      <StatBox icon={FiCpu} label="CDM Groups" value={dlDmrs?.numCdmGrpWithoutData || 'N/A'} colorScheme="green" />
    </SimpleGrid>
  </VStack>
);

const ULCommonConfig = ({ ulCmn }: { ulCmn: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 3 }} spacing={4}>
      <StatBox
        icon={FiSettings}
        label="Additional UL Coreset"
        value={ulCmn?.addnlUlCoresetEnable ? 'Enabled' : 'Disabled'}
        colorScheme={ulCmn?.addnlUlCoresetEnable ? 'green' : 'red'}
        isValueStatus={true}
      />
      <StatBox
        icon={FiLayers}
        label="Sub-Carrier Configs"
        value={ulCmn?.subCarrierCfg?.length || 0}
        colorScheme="purple"
      />
      <StatBox icon={FiGrid} label="UL BWP Configs" value={ulCmn?.ulBwpCfg?.length || 0} colorScheme="blue" />
    </SimpleGrid>

    {/* Sub-Carrier Configuration */}
    {ulCmn?.subCarrierCfg && ulCmn.subCarrierCfg.length > 0 && (
      <Box p={4} borderWidth="1px" borderRadius="lg" bg="purple.50" borderColor="purple.200">
        <HStack mb={3}>
          <Icon as={FiRadio} color="purple.500" />
          <Text fontWeight="bold" fontSize="md" color="purple.700">
            Sub-Carrier Configuration
          </Text>
        </HStack>
        {ulCmn.subCarrierCfg.map((config: any, index: number) => (
          <Box key={index} p={3} bg="white" borderRadius="md" mb={2}>
            <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Config ID
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {config.id}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Carrier BW
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {config.carrierBw}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  SCS
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {config.subCarrierSpacing}
                </Text>
              </Box>
              <Box>
                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                  Offset
                </Text>
                <Text fontSize="sm" fontWeight="bold">
                  {config.offsetToCarrier}
                </Text>
              </Box>
            </SimpleGrid>
          </Box>
        ))}
      </Box>
    )}
  </VStack>
);

const ULBWPConfig = ({ ulBwpCfg }: { ulBwpCfg: any[] }) => {
  if (!ulBwpCfg || ulBwpCfg.length === 0) {
    return (
      <Alert status="info" borderRadius="lg">
        <AlertIcon />
        <Text>No UL BWP configurations available.</Text>
      </Alert>
    );
  }

  return (
    <VStack spacing={4} align="stretch">
      {/* BWP Overview */}
      <SimpleGrid columns={{ base: 1, md: ulBwpCfg.length }} spacing={4} mb={4}>
        {ulBwpCfg?.map((bwp: any, index: number) => (
          <StatBox
            key={index}
            icon={FiGrid}
            label={`BWP ${bwp.id}`}
            value={`${bwp.numRb || 'N/A'} RBs`}
            colorScheme="blue"
          />
        ))}
      </SimpleGrid>

      {/* Individual BWP Configurations as Tabs */}
      <Tabs variant="soft-rounded" colorScheme="purple" align="start">
        <TabList mt={2} mx={2} flexWrap="wrap" gap={2}>
          {ulBwpCfg?.map((bwp: any, index: number) => (
            <Tab
              key={index}
              shadow="md"
              _focus={{ boxShadow: 'none' }}
              _hover={{ bg: 'purple.200', transform: 'translateY(-2px)' }}
              transition="all 0.2s ease"
            >
              <HStack>
                <Icon as={FiSettings} boxSize={4} color="purple.500" />
                <Text fontWeight="medium" fontSize="sm">
                  BWP {bwp.id}
                </Text>
                <Badge size="sm" colorScheme="purple">
                  {bwp.mu || 'N/A'}μ
                </Badge>
              </HStack>
            </Tab>
          ))}
        </TabList>

        <TabPanels mt={4} mx={2}>
          {ulBwpCfg?.map((bwp: any, index: number) => (
            <TabPanel key={index} p={4}>
              <VStack spacing={4} align="stretch">
                {/* Basic BWP Info */}
                <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4} mb={4}>
                  <StatBox icon={FiGrid} label="BWP ID" value={bwp.id} colorScheme="blue" />
                  <StatBox icon={FiRadio} label="MU" value={bwp.mu || 'N/A'} colorScheme="green" />
                  <StatBox icon={FiLayers} label="Number of RBs" value={bwp.numRb || 'N/A'} colorScheme="purple" />
                  <StatBox icon={FiCpu} label="Cyclic Prefix" value={bwp.cyclicPrefix || 'N/A'} colorScheme="orange" />
                </SimpleGrid>

                {/* Nested Tabs for BWP Configuration Details */}
                <Tabs variant="soft-rounded" colorScheme="gray" align="start" size="sm">
                  <TabList mt={2} mx={2} flexWrap="wrap" gap={2}>
                    <Tab
                      shadow="md"
                      _focus={{ boxShadow: 'none' }}
                      _hover={{ bg: 'blue.100', transform: 'translateY(-1px)' }}
                      transition="all 0.2s ease"
                      _selected={{ bg: 'blue.100', shadow: 'md', transform: 'translateY(-1px)' }}
                    >
                      <HStack>
                        <Icon as={FiWifi} boxSize={4} color="blue.500" />
                        <Text fontWeight="medium" fontSize="sm">
                          PUCCH & PUSCH
                        </Text>
                      </HStack>
                    </Tab>

                    {bwp.puschPwrCfg && (
                      <Tab
                        shadow="md"
                        _focus={{ boxShadow: 'none' }}
                        _hover={{ bg: 'orange.100', transform: 'translateY(-1px)' }}
                        transition="all 0.2s ease"
                        _selected={{ bg: 'orange.100', shadow: 'md', transform: 'translateY(-1px)' }}
                      >
                        <HStack>
                          <Icon as={FiPower} boxSize={4} color="orange.500" />
                          <Text fontWeight="medium" fontSize="sm">
                            Power Control
                          </Text>
                        </HStack>
                      </Tab>
                    )}

                    {bwp.rachCfgInfo && (
                      <Tab
                        shadow="md"
                        _focus={{ boxShadow: 'none' }}
                        _hover={{ bg: 'green.100', transform: 'translateY(-1px)' }}
                        transition="all 0.2s ease"
                        _selected={{ bg: 'green.100', shadow: 'md', transform: 'translateY(-1px)' }}
                      >
                        <HStack>
                          <Icon as={FiTarget} boxSize={4} color="green.500" />
                          <Text fontWeight="medium" fontSize="sm">
                            RACH Config
                          </Text>
                        </HStack>
                      </Tab>
                    )}

                    {bwp.srsCfg && (
                      <Tab
                        shadow="md"
                        _focus={{ boxShadow: 'none' }}
                        _hover={{ bg: 'purple.100', transform: 'translateY(-1px)' }}
                        transition="all 0.2s ease"
                        _selected={{ bg: 'purple.100', shadow: 'md', transform: 'translateY(-1px)' }}
                      >
                        <HStack>
                          <Icon as={FiBarChart2} boxSize={4} color="purple.500" />
                          <Text fontWeight="medium" fontSize="sm">
                            SRS Config
                          </Text>
                        </HStack>
                      </Tab>
                    )}
                  </TabList>

                  <TabPanels mt={3} mx={2}>
                    {/* PUCCH & PUSCH Tab */}
                    <TabPanel p={3}>
                      <VStack spacing={4} align="stretch">
                        {/* PUCCH Configuration */}
                        <Box p={3} bg="blue.50" borderRadius="md" borderWidth="1px" borderColor="blue.200">
                          <Text fontWeight="bold" fontSize="sm" color="blue.700" mb={3}>
                            PUCCH Configuration
                          </Text>
                          <SimpleGrid columns={{ base: 1, md: 3 }} spacing={3}>
                            <Box>
                              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                                Group Hopping
                              </Text>
                              <Text fontSize="sm" fontWeight="bold">
                                {bwp.pucchGrpHopping}
                              </Text>
                            </Box>
                            <Box>
                              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                                Resource Common
                              </Text>
                              <Text fontSize="sm" fontWeight="bold">
                                {bwp.pucchResourceCmn}
                              </Text>
                            </Box>
                            <Box>
                              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                                Common Config Present
                              </Text>
                              <Badge colorScheme={bwp.pucchCmnCfgPres ? 'green' : 'red'} variant="solid">
                                <Icon as={bwp.pucchCmnCfgPres ? FiCheckCircle : FiXCircle} mr={1} />
                                {bwp.pucchCmnCfgPres ? 'Yes' : 'No'}
                              </Badge>
                            </Box>
                          </SimpleGrid>
                        </Box>

                        {/* PUSCH Configuration */}
                        <Box p={3} bg="cyan.50" borderRadius="md" borderWidth="1px" borderColor="cyan.200">
                          <Text fontWeight="bold" fontSize="sm" color="cyan.700" mb={3}>
                            PUSCH Configuration
                          </Text>
                          <SimpleGrid columns={{ base: 1, md: 3 }} spacing={3}>
                            <Box>
                              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                                Transform Precoder
                              </Text>
                              <Badge colorScheme={bwp.puschTransformPrecoder ? 'green' : 'red'} variant="solid">
                                <Icon as={bwp.puschTransformPrecoder ? FiCheckCircle : FiXCircle} mr={1} />
                                {bwp.puschTransformPrecoder ? 'Enabled' : 'Disabled'}
                              </Badge>
                            </Box>
                            <Box>
                              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                                Codebook Subset
                              </Text>
                              <Text fontSize="sm" fontWeight="bold">
                                {bwp.puschCodebookSubset}
                              </Text>
                            </Box>
                            <Box>
                              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                                TX Config
                              </Text>
                              <Text fontSize="sm" fontWeight="bold">
                                {bwp.puschTxCfg}
                              </Text>
                            </Box>
                          </SimpleGrid>
                        </Box>
                      </VStack>
                    </TabPanel>

                    {/* Power Control Tab */}
                    {bwp.puschPwrCfg && (
                      <TabPanel p={3}>
                        <Box p={3} bg="orange.50" borderRadius="md" borderWidth="1px" borderColor="orange.200">
                          <Text fontWeight="bold" fontSize="sm" color="orange.700" mb={3}>
                            Power Configuration
                          </Text>
                          <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
                            <Box>
                              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                                P0 Nominal
                              </Text>
                              <Text fontSize="sm" fontWeight="bold">
                                {bwp.puschPwrCfg.p0nominal} dBm
                              </Text>
                            </Box>
                            <Box>
                              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                                Alpha
                              </Text>
                              <Text fontSize="sm" fontWeight="bold">
                                {bwp.puschPwrCfg.alpha}
                              </Text>
                            </Box>
                            <Box>
                              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                                MSG3 Alpha
                              </Text>
                              <Text fontSize="sm" fontWeight="bold">
                                {bwp.puschPwrCfg.msg3alpha}
                              </Text>
                            </Box>
                            <Box>
                              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                                Accumulated
                              </Text>
                              <Badge colorScheme={bwp.puschPwrCfg.isAccumulated ? 'green' : 'red'} variant="solid">
                                <Icon as={bwp.puschPwrCfg.isAccumulated ? FiCheckCircle : FiXCircle} mr={1} />
                                {bwp.puschPwrCfg.isAccumulated ? 'Yes' : 'No'}
                              </Badge>
                            </Box>
                          </SimpleGrid>
                        </Box>
                      </TabPanel>
                    )}

                    {/* RACH Configuration Tab */}
                    {bwp.rachCfgInfo && (
                      <TabPanel p={3}>
                        <VStack spacing={4} align="stretch">
                          <Box p={3} bg="green.50" borderRadius="md" borderWidth="1px" borderColor="green.200">
                            <Text fontWeight="bold" fontSize="sm" color="green.700" mb={3}>
                              RACH Configuration
                            </Text>
                            <SimpleGrid columns={{ base: 1, md: 3 }} spacing={3} mb={3}>
                              <Box>
                                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                                  CB Preambles per SSB
                                </Text>
                                <Text fontSize="sm" fontWeight="bold">
                                  {bwp.rachCfgInfo.cbPreamblePerSsb}
                                </Text>
                              </Box>
                              <Box>
                                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                                  Content Resolution Timer
                                </Text>
                                <Text fontSize="sm" fontWeight="bold">
                                  {bwp.rachCfgInfo.contentResolutionTmr}
                                </Text>
                              </Box>
                              <Box>
                                <Text fontSize="xs" color="gray.500" fontWeight="medium">
                                  Max MSG3 TX
                                </Text>
                                <Text fontSize="sm" fontWeight="bold">
                                  {bwp.rachCfgInfo.maxMsg3Tx}
                                </Text>
                              </Box>
                            </SimpleGrid>

                            {bwp.rachCfgInfo.rachGenCfg && (
                              <Box p={3} bg="white" borderRadius="md" borderWidth="1px" borderColor="green.300">
                                <Text fontSize="xs" fontWeight="bold" color="green.700" mb={2}>
                                  General RACH Config
                                </Text>
                                <SimpleGrid columns={{ base: 2, md: 4 }} spacing={2}>
                                  <Box>
                                    <Text fontSize="xs" color="gray.500">
                                      PRACH Config Index
                                    </Text>
                                    <Text fontSize="xs" fontWeight="bold">
                                      {bwp.rachCfgInfo.rachGenCfg.prachCfgIdx}
                                    </Text>
                                  </Box>
                                  <Box>
                                    <Text fontSize="xs" color="gray.500">
                                      MSG1 FDM
                                    </Text>
                                    <Text fontSize="xs" fontWeight="bold">
                                      {bwp.rachCfgInfo.rachGenCfg.msg1Fdm}
                                    </Text>
                                  </Box>
                                  <Box>
                                    <Text fontSize="xs" color="gray.500">
                                      Preamble TX Max
                                    </Text>
                                    <Text fontSize="xs" fontWeight="bold">
                                      {bwp.rachCfgInfo.rachGenCfg.preambleTransMax}
                                    </Text>
                                  </Box>
                                  <Box>
                                    <Text fontSize="xs" color="gray.500">
                                      Power Ramping Step
                                    </Text>
                                    <Text fontSize="xs" fontWeight="bold">
                                      {bwp.rachCfgInfo.rachGenCfg.pwrRampingStep}
                                    </Text>
                                  </Box>
                                </SimpleGrid>
                              </Box>
                            )}
                          </Box>
                        </VStack>
                      </TabPanel>
                    )}

                    {/* SRS Configuration Tab */}
                    {bwp.srsCfg && (
                      <TabPanel p={3}>
                        <Box p={3} bg="purple.50" borderRadius="md" borderWidth="1px" borderColor="purple.200">
                          <Text fontWeight="bold" fontSize="sm" color="purple.700" mb={3}>
                            SRS Configuration
                          </Text>
                          <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
                            <Box>
                              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                                Alpha
                              </Text>
                              <Text fontSize="sm" fontWeight="bold">
                                {bwp.srsCfg.alpha}
                              </Text>
                            </Box>
                            <Box>
                              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                                Max SRS UE per Slot
                              </Text>
                              <Text fontSize="sm" fontWeight="bold">
                                {bwp.srsCfg.maxSrsUePerSlot}
                              </Text>
                            </Box>
                            <Box>
                              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                                Number of RBs
                              </Text>
                              <Text fontSize="sm" fontWeight="bold">
                                {bwp.srsCfg.numRBs}
                              </Text>
                            </Box>
                            <Box>
                              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                                Resource Type
                              </Text>
                              <Text fontSize="sm" fontWeight="bold">
                                {bwp.srsCfg.srsResourceType}
                              </Text>
                            </Box>
                          </SimpleGrid>
                        </Box>
                      </TabPanel>
                    )}
                  </TabPanels>
                </Tabs>
              </VStack>
            </TabPanel>
          ))}
        </TabPanels>
      </Tabs>
    </VStack>
  );
};

const ULDMRSConfig = ({ ulDmrs }: { ulDmrs: any }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiRadio} label="DMRS Type" value={ulDmrs?.dmrsTypeUl || 'N/A'} colorScheme="cyan" />
      <StatBox
        icon={FiLayers}
        label="Additional Positions"
        value={ulDmrs?.dmrsAdditionalPosUl || 'N/A'}
        colorScheme="teal"
      />
      <StatBox icon={FiGrid} label="Max Length" value={ulDmrs?.maxLenUl || 'N/A'} colorScheme="blue" />
      <StatBox icon={FiCpu} label="CDM Groups" value={ulDmrs?.numCdmGrpWithoutData || 'N/A'} colorScheme="green" />
    </SimpleGrid>

    {/* Transform Precoding Configuration */}
    <Box p={4} borderWidth="1px" borderRadius="lg" bg="cyan.50" borderColor="cyan.200">
      <HStack mb={3}>
        <Icon as={FiZap} color="cyan.500" />
        <Text fontWeight="bold" fontSize="md" color="cyan.700">
          Transform Precoding Configuration
        </Text>
      </HStack>

      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
        <Box p={3} bg="white" borderRadius="md" borderWidth="1px" borderColor="gray.200">
          <HStack>
            <Text fontWeight="bold" fontSize="sm" color="gray.700" mb={2}>
              Disabled Configuration
            </Text>

            <Badge
              colorScheme={ulDmrs?.transPrecodingDisabledUl?.istransPrecodingDisabled ? 'green' : 'red'}
              variant="solid"
              px={3}
              py={1}
              borderRadius="full"
            >
              <HStack spacing={1}>
                <Icon
                  as={ulDmrs?.transPrecodingDisabledUl?.istransPrecodingDisabled ? FiCheckCircle : FiXCircle}
                  boxSize={4}
                />
                <Text>{ulDmrs?.transPrecodingDisabledUl?.istransPrecodingDisabled ? 'Disabled' : 'Not Disabled'}</Text>
              </HStack>
            </Badge>
          </HStack>
        </Box>

        <Box p={3} bg="white" borderRadius="md">
          <HStack>
            <Text fontWeight="bold" fontSize="sm" color="gray.700" mb={2}>
              Enabled Configuration
            </Text>
            <Badge
              colorScheme={ulDmrs?.transPrecodingEnabledUl?.istransPrecodingEnabled ? 'green' : 'red'}
              variant="solid"
              px={3}
              py={1}
              borderRadius="full"
            >
              <HStack spacing={1}>
                <Icon
                  as={ulDmrs?.transPrecodingEnabledUl?.istransPrecodingEnabled ? FiCheckCircle : FiXCircle}
                  boxSize={4}
                />
                <Text>{ulDmrs?.transPrecodingEnabledUl?.istransPrecodingEnabled ? 'Enabled' : 'Not Enabled'}</Text>
              </HStack>
            </Badge>
          </HStack>

          <VStack align="start" spacing={2}>
            <Text fontSize="xs" color="gray.600">
              nPUSCH ID: {ulDmrs?.transPrecodingEnabledUl?.nPUSCHId || 'N/A'}
            </Text>
            <HStack>
              <Text fontSize="xs" color="gray.600">
                Seq Group Hopping:
              </Text>
              <Badge
                colorScheme={ulDmrs?.transPrecodingEnabledUl?.seqGrpHopping === 'enabled' ? 'green' : 'red'}
                variant="solid"
                px={3}
                py={1}
                borderRadius="full"
              >
                <HStack spacing={1}>
                  <Icon
                    as={ulDmrs?.transPrecodingEnabledUl?.seqGrpHopping === 'enabled' ? FiCheckCircle : FiXCircle}
                    boxSize={4}
                  />
                  <Text>{ulDmrs?.transPrecodingEnabledUl?.seqGrpHopping === 'enabled' ? 'Enabled' : 'Disabled'}</Text>
                </HStack>
              </Badge>
            </HStack>

            <HStack>
              <Text fontSize="xs" color="gray.600">
                Seq Hopping:
              </Text>
              <Badge
                colorScheme={ulDmrs?.transPrecodingEnabledUl?.seqHopping === 'enabled' ? 'green' : 'red'}
                variant="solid"
                px={3}
                py={1}
                borderRadius="full"
              >
                <HStack spacing={1}>
                  <Icon
                    as={ulDmrs?.transPrecodingEnabledUl?.seqHopping === 'enabled' ? FiCheckCircle : FiXCircle}
                    boxSize={4}
                  />
                  <Text>{ulDmrs?.transPrecodingEnabledUl?.seqHopping === 'enabled' ? 'Enabled' : 'Disabled'}</Text>
                </HStack>
              </Badge>
            </HStack>
          </VStack>
        </Box>
      </SimpleGrid>
    </Box>
  </VStack>
);

const DLConfigurationTabs = ({ dlConfiguration }: { dlConfiguration: any }) => (
  <Tabs variant="soft-rounded" colorScheme="blue" align="start">
    <TabList mt={2} mx={2} flexWrap="wrap" gap={2}>
      {dlConfiguration?.dlCmn && (
        <Tab
          shadow="md"
          _focus={{ boxShadow: 'none' }}
          _hover={{ bg: 'blue.200', transform: 'translateY(-2px)' }}
          transition="all 0.2s ease"
        >
          <HStack>
            <Icon as={FiSettings} boxSize={4} color="blue.500" />
            <Text fontWeight="medium" fontSize="sm">
              DL Common
            </Text>
          </HStack>
        </Tab>
      )}

      {dlConfiguration?.dlCA && (
        <Tab
          shadow="md"
          _focus={{ boxShadow: 'none' }}
          _hover={{ bg: 'orange.200', transform: 'translateY(-2px)' }}
          transition="all 0.2s ease"
        >
          <HStack>
            <Icon as={FiLayers} boxSize={4} color="orange.500" />
            <Text fontWeight="medium" fontSize="sm">
              Carrier Aggregation
            </Text>
          </HStack>
        </Tab>
      )}

      {dlConfiguration?.dlDmrs && (
        <Tab
          shadow="md"
          _focus={{ boxShadow: 'none' }}
          _hover={{ bg: 'cyan.200', transform: 'translateY(-2px)' }}
          transition="all 0.2s ease"
        >
          <HStack>
            <Icon as={FiRadio} boxSize={4} color="cyan.500" />
            <Text fontWeight="medium" fontSize="sm">
              DMRS
            </Text>
          </HStack>
        </Tab>
      )}
    </TabList>

    <TabPanels mt={4} mx={2}>
      {dlConfiguration?.dlCmn && (
        <TabPanel p={4}>
          <DLCommonConfig dlCmn={dlConfiguration.dlCmn} />
        </TabPanel>
      )}

      {dlConfiguration?.dlCA && (
        <TabPanel p={4}>
          <DLCAConfig dlCA={dlConfiguration.dlCA} />
        </TabPanel>
      )}

      {dlConfiguration?.dlDmrs && (
        <TabPanel p={4}>
          <DLDMRSConfig dlDmrs={dlConfiguration.dlDmrs} />
        </TabPanel>
      )}
    </TabPanels>
  </Tabs>
);

const ULConfigurationTabs = ({ ulConfiguration }: { ulConfiguration: any }) => (
  <Tabs variant="soft-rounded" colorScheme="green" align="start">
    <TabList mt={2} mx={2} flexWrap="wrap" gap={2}>
      {ulConfiguration?.ulCmn && (
        <Tab
          shadow="md"
          _focus={{ boxShadow: 'none' }}
          _hover={{ bg: 'green.200', transform: 'translateY(-2px)' }}
          transition="all 0.2s ease"
        >
          <HStack>
            <Icon as={FiSettings} boxSize={4} color="green.500" />
            <Text fontWeight="medium" fontSize="sm">
              UL Common
            </Text>
          </HStack>
        </Tab>
      )}

      {ulConfiguration?.ulCmn?.ulBwpCfg && ulConfiguration.ulCmn.ulBwpCfg.length > 0 && (
        <Tab
          shadow="md"
          _focus={{ boxShadow: 'none' }}
          _hover={{ bg: 'purple.200', transform: 'translateY(-2px)' }}
          transition="all 0.2s ease"
        >
          <HStack>
            <Icon as={FiGrid} boxSize={4} color="purple.500" />
            <Text fontWeight="medium" fontSize="sm">
              BWP Configuration
            </Text>
          </HStack>
        </Tab>
      )}

      {ulConfiguration?.ulDmrs && (
        <Tab
          shadow="md"
          _focus={{ boxShadow: 'none' }}
          _hover={{ bg: 'teal.200', transform: 'translateY(-2px)' }}
          transition="all 0.2s ease"
        >
          <HStack>
            <Icon as={FiRadio} boxSize={4} color="teal.500" />
            <Text fontWeight="medium" fontSize="sm">
              DMRS
            </Text>
          </HStack>
        </Tab>
      )}
    </TabList>

    <TabPanels mt={4} mx={2}>
      {ulConfiguration?.ulCmn && (
        <TabPanel p={4}>
          <ULCommonConfig ulCmn={ulConfiguration.ulCmn} />
        </TabPanel>
      )}

      {ulConfiguration?.ulCmn?.ulBwpCfg && ulConfiguration.ulCmn.ulBwpCfg.length > 0 && (
        <TabPanel p={4}>
          <ULBWPConfig ulBwpCfg={ulConfiguration.ulCmn.ulBwpCfg} />
        </TabPanel>
      )}

      {ulConfiguration?.ulDmrs && (
        <TabPanel p={4}>
          <ULDMRSConfig ulDmrs={ulConfiguration.ulDmrs} />
        </TabPanel>
      )}
    </TabPanels>
  </Tabs>
);

export const LinkConfiguration: React.FC<LinkConfigurationProps> = ({ data }) => {
  if (!data?.dlConfiguration && !data?.ulConfiguration) {
    return (
      <Alert status="info" borderRadius="lg">
        <AlertIcon />
        <Text>No link configuration data available.</Text>
      </Alert>
    );
  }

  const { dlConfiguration, ulConfiguration } = data;

  return (
    <Card borderWidth="1px" borderRadius="lg" overflow="hidden" variant="outline" border="none">
      <CardBody px={2} py={0}>
        <Tabs variant="soft-rounded" colorScheme="blue" align="start">
          <TabList mt={4} mx={4} flexWrap="wrap" gap={2}>
            {/* Downlink Top-Level Tab */}
            {dlConfiguration && (
              <Tab
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'blue.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
              >
                <HStack>
                  <Icon as={FiDownload} boxSize={5} color="blue.500" />
                  <Text fontWeight="medium" fontSize="md">
                    Downlink Configuration
                  </Text>
                </HStack>
              </Tab>
            )}

            {/* Uplink Top-Level Tab */}
            {ulConfiguration && (
              <Tab
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'green.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
              >
                <HStack>
                  <Icon as={FiUpload} boxSize={5} color="green.500" />
                  <Text fontWeight="medium" fontSize="md">
                    Uplink Configuration
                  </Text>
                </HStack>
              </Tab>
            )}
          </TabList>

          <TabPanels mt={4} mx={4}>
            {/* Downlink Tab Panel with nested tabs */}
            {dlConfiguration && (
              <TabPanel p={4}>
                <DLConfigurationTabs dlConfiguration={dlConfiguration} />
              </TabPanel>
            )}

            {/* Uplink Tab Panel with nested tabs */}
            {ulConfiguration && (
              <TabPanel p={4}>
                <ULConfigurationTabs ulConfiguration={ulConfiguration} />
              </TabPanel>
            )}
          </TabPanels>
        </Tabs>
      </CardBody>
    </Card>
  );
};
