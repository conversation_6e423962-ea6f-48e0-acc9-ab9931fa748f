import React from 'react';
import {
  Box,
  Card,
  CardBody,
  HStack,
  Icon,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Text,
  VStack,
  Badge,
  Alert,
  AlertIcon,
  As,
} from '@chakra-ui/react';
import {
  FiDatabase,
  FiClock,
  FiActivity,
  FiZap,
  FiCheckCircle,
  FiXCircle,
  FiHash,
  FiSettings,
  FiBarChart2,
} from 'react-icons/fi';
import { CopyButton } from '../../common';

interface DRXConfigurationProps {
  data: any[];
}

const StatBox = ({
  icon,
  label,
  value,
  colorScheme,
  copyable = false,
}: {
  icon: As;
  label: string;
  value: string | number | boolean;
  colorScheme: string;
  copyable?: boolean;
}) => (
  <Stat
    p={3}
    bg={`${colorScheme}.50`}
    borderRadius="md"
    textAlign="center"
    borderWidth="1px"
    borderColor={`${colorScheme}.100`}
  >
    <StatLabel fontSize="sm" color={`${colorScheme}.600`} fontWeight="medium">
      <HStack justify="center" spacing={1.5}>
        <Icon as={icon} boxSize={4} />
        <Text>{label}</Text>
      </HStack>
    </StatLabel>
    <HStack justify="center" spacing={2} mt={1}>
      <StatNumber fontSize="xl" color={`${colorScheme}.800`} fontFamily="mono">
        {typeof value === 'boolean' ? (
          <Badge colorScheme={value ? 'green' : 'red'} variant="solid" px={3} py={1} borderRadius="full">
            <HStack spacing={1}>
              <Icon as={value ? FiCheckCircle : FiXCircle} boxSize={4} />
              <Text>{value ? 'Enabled' : 'Disabled'}</Text>
            </HStack>
          </Badge>
        ) : (
          value
        )}
      </StatNumber>
      {copyable && <CopyButton text={String(value)} label={label} />}
    </HStack>
  </Stat>
);

const DRXProfileCard = ({ profile }: { profile: any }) => {
  const getProfileColor = (profileType: string) => {
    switch (profileType) {
      case 'VONR':
        return 'purple';
      case 'GBR':
        return 'green';
      case 'NGBR':
        return 'blue';
      case 'ANR':
        return 'orange';
      default:
        return 'gray';
    }
  };

  const colorScheme = getProfileColor(profile.drxProfile);

  return (
    <Box p={4} borderWidth="1px" borderRadius="lg" bg={`${colorScheme}.50`} borderColor={`${colorScheme}.200`} mb={4}>
      <HStack mb={3} justify="space-between">
        <HStack>
          <Icon as={FiDatabase} color={`${colorScheme}.500`} />
          <Text fontWeight="bold" fontSize="md" color={`${colorScheme}.700`}>
            {profile.drxProfile} Profile
          </Text>
        </HStack>
        <HStack spacing={2}>
          <Badge colorScheme={colorScheme} variant="solid" px={3} py={1} borderRadius="full">
            <HStack spacing={1}>
              <Icon as={FiHash} boxSize={4} />
              <Text>ID: {profile.id}</Text>
            </HStack>
          </Badge>
          {profile.isDrxEnabled !== undefined && (
            <Badge
              colorScheme={profile.isDrxEnabled ? 'green' : 'red'}
              variant="solid"
              px={3}
              py={1}
              borderRadius="full"
            >
              <HStack spacing={1}>
                <Icon as={profile.isDrxEnabled ? FiCheckCircle : FiXCircle} boxSize={4} />
                <Text>{profile.isDrxEnabled ? 'Enabled' : 'Disabled'}</Text>
              </HStack>
            </Badge>
          )}
        </HStack>
      </HStack>

      {/* Timer Configuration */}
      <Box p={3} bg="white" borderRadius="md" mb={3}>
        <Text fontWeight="bold" fontSize="sm" color="gray.700" mb={2}>
          Timer Configuration
        </Text>
        <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
          <Box>
            <Text fontSize="xs" color="gray.500" fontWeight="medium">
              Inactivity Timer
            </Text>
            <Text fontSize="sm" fontWeight="bold">
              {profile.drxInactivityTmr}
            </Text>
          </Box>
          <Box>
            <Text fontSize="xs" color="gray.500" fontWeight="medium">
              On Duration Timer
            </Text>
            <Text fontSize="sm" fontWeight="bold">
              {profile.drxOnDurationTmrInMS}
            </Text>
          </Box>
          <Box>
            <Text fontSize="xs" color="gray.500" fontWeight="medium">
              DL Retx Timer
            </Text>
            <Text fontSize="sm" fontWeight="bold">
              {profile.drxRetxTmrDl}
            </Text>
          </Box>
          <Box>
            <Text fontSize="xs" color="gray.500" fontWeight="medium">
              UL Retx Timer
            </Text>
            <Text fontSize="sm" fontWeight="bold">
              {profile.drxRetxTmrUl}
            </Text>
          </Box>
        </SimpleGrid>
      </Box>

      {/* RTT Configuration */}
      <Box p={3} bg="white" borderRadius="md" mb={3}>
        <Text fontWeight="bold" fontSize="sm" color="gray.700" mb="2">
          RTT Configuration
        </Text>
        <SimpleGrid columns={{ base: 2, md: 3 }} spacing={3}>
          <Box>
            <Text fontSize="xs" color="gray.500" fontWeight="medium">
              DL RTT Timer
            </Text>
            <Text fontSize="sm" fontWeight="bold">
              {profile.drxRttTmrDl} slots
            </Text>
          </Box>
          <Box>
            <Text fontSize="xs" color="gray.500" fontWeight="medium">
              UL RTT Timer
            </Text>
            <Text fontSize="sm" fontWeight="bold">
              {profile.drxRttTmrUl} slots
            </Text>
          </Box>
          <Box>
            <Text fontSize="xs" color="gray.500" fontWeight="medium">
              On Duration Timer Type
            </Text>
            <Text fontSize="sm" fontWeight="bold">
              {profile.isDrxOnDurationTmrType?.replace('drx-', '') || 'N/A'}
            </Text>
          </Box>
        </SimpleGrid>
      </Box>
    </Box>
  );
};

const DRXOverview = ({ profiles }: { profiles: any[] }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiDatabase} label="Total Profiles" value={profiles?.length || 0} colorScheme="blue" />
      <StatBox
        icon={FiCheckCircle}
        label="Enabled Profiles"
        value={profiles?.filter((p) => p.isDrxEnabled).length || 0}
        colorScheme="green"
      />
      <StatBox
        icon={FiZap}
        label="VoNR Profiles"
        value={profiles?.filter((p) => p.drxProfile === 'VONR').length || 0}
        colorScheme="purple"
      />
      <StatBox
        icon={FiActivity}
        label="GBR Profiles"
        value={profiles?.filter((p) => p.drxProfile === 'GBR').length || 0}
        colorScheme="green"
      />
    </SimpleGrid>

    {/* Individual Profile Cards */}
    {profiles?.map((profile) => (
      <DRXProfileCard key={profile.id} profile={profile} />
    ))}
  </VStack>
);

const VoNRProfileConfig = ({ profiles }: { profiles: any[] }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiZap} label="VoNR Profiles" value={profiles.length} colorScheme="purple" />
      <StatBox
        icon={FiCheckCircle}
        label="Enabled"
        value={profiles.filter((p) => p.isDrxEnabled).length}
        colorScheme="green"
      />
      <StatBox
        icon={FiActivity}
        label="Avg Inactivity Timer"
        value={profiles[0]?.drxInactivityTmr || 'N/A'}
        colorScheme="blue"
      />
      <StatBox
        icon={FiDatabase}
        label="On Duration Timer"
        value={profiles[0]?.drxOnDurationTmrInMS || 'N/A'}
        colorScheme="orange"
      />
    </SimpleGrid>

    {profiles.map((profile) => (
      <Box key={profile.id} p={4} borderWidth="1px" borderRadius="lg" bg="purple.50" borderColor="purple.200">
        <HStack mb={3} justify="space-between">
          <HStack>
            <Icon as={FiZap} color="purple.500" />
            <Text fontWeight="bold" fontSize="md" color="purple.700">
              VoNR Profile ID: {profile.id}
            </Text>
          </HStack>
          <HStack spacing={2}>
            {profile.isDrxEnabled !== undefined && (
              <Badge
                colorScheme={profile.isDrxEnabled ? 'green' : 'red'}
                variant="solid"
                px={3}
                py={1}
                borderRadius="full"
              >
                <HStack spacing={1}>
                  <Icon as={profile.isDrxEnabled ? FiCheckCircle : FiXCircle} boxSize={4} />
                  <Text>{profile.isDrxEnabled ? 'Enabled' : 'Disabled'}</Text>
                </HStack>
              </Badge>
            )}
            <CopyButton text={`VoNR-${profile.id}`} label="Profile ID" />
          </HStack>
        </HStack>

        {/* Timer Configuration */}
        <Box p={3} bg="white" borderRadius="md" mb={3}>
          <Text fontWeight="bold" fontSize="sm" color="gray.700" mb={2}>
            Timer Configuration
          </Text>
          <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                Inactivity Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxInactivityTmr}
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                On Duration Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxOnDurationTmrInMS}
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                DL Retx Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxRetxTmrDl}
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                UL Retx Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxRetxTmrUl}
              </Text>
            </Box>
          </SimpleGrid>
        </Box>

        {/* RTT Configuration */}
        <Box p={3} bg="white" borderRadius="md">
          <Text fontWeight="bold" fontSize="sm" color="gray.700" mb={2}>
            RTT Configuration
          </Text>
          <SimpleGrid columns={{ base: 2, md: 3 }} spacing={3}>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                DL RTT Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxRttTmrDl} slots
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                UL RTT Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxRttTmrUl} slots
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                On Duration Timer Type
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.isDrxOnDurationTmrType?.replace('drx-', '') || 'N/A'}
              </Text>
            </Box>
          </SimpleGrid>
        </Box>
      </Box>
    ))}
  </VStack>
);

const GBRProfileConfig = ({ profiles }: { profiles: any[] }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiActivity} label="GBR Profiles" value={profiles.length} colorScheme="green" />
      <StatBox
        icon={FiCheckCircle}
        label="Enabled"
        value={profiles.filter((p) => p.isDrxEnabled).length}
        colorScheme="blue"
      />
      <StatBox
        icon={FiClock}
        label="Avg Inactivity Timer"
        value={profiles[0]?.drxInactivityTmr || 'N/A'}
        colorScheme="purple"
      />
      <StatBox
        icon={FiDatabase}
        label="On Duration Timer"
        value={profiles[0]?.drxOnDurationTmrInMS || 'N/A'}
        colorScheme="orange"
      />
    </SimpleGrid>

    {profiles.map((profile) => (
      <Box key={profile.id} p={4} borderWidth="1px" borderRadius="lg" bg="green.50" borderColor="green.200">
        <HStack mb={3} justify="space-between">
          <HStack>
            <Icon as={FiActivity} color="green.500" />
            <Text fontWeight="bold" fontSize="md" color="green.700">
              GBR Profile ID: {profile.id}
            </Text>
          </HStack>
          <HStack spacing={2}>
            {profile.isDrxEnabled !== undefined && (
              <Badge
                colorScheme={profile.isDrxEnabled ? 'green' : 'red'}
                variant="solid"
                px={3}
                py={1}
                borderRadius="full"
              >
                <HStack spacing={1}>
                  <Icon as={profile.isDrxEnabled ? FiCheckCircle : FiXCircle} boxSize={4} />
                  <Text>{profile.isDrxEnabled ? 'Enabled' : 'Disabled'}</Text>
                </HStack>
              </Badge>
            )}
            <CopyButton text={`GBR-${profile.id}`} label="Profile ID" />
          </HStack>
        </HStack>

        {/* Timer Configuration */}
        <Box p={3} bg="white" borderRadius="md" mb={3}>
          <Text fontWeight="bold" fontSize="sm" color="gray.700" mb={2}>
            Timer Configuration
          </Text>
          <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                Inactivity Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxInactivityTmr}
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                On Duration Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxOnDurationTmrInMS}
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                DL Retx Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxRetxTmrDl}
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                UL Retx Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxRetxTmrUl}
              </Text>
            </Box>
          </SimpleGrid>
        </Box>

        {/* RTT Configuration */}
        <Box p={3} bg="white" borderRadius="md">
          <Text fontWeight="bold" fontSize="sm" color="gray.700" mb={2}>
            RTT Configuration
          </Text>
          <SimpleGrid columns={{ base: 2, md: 3 }} spacing={3}>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                DL RTT Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxRttTmrDl} slots
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                UL RTT Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxRttTmrUl} slots
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                On Duration Timer Type
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.isDrxOnDurationTmrType?.replace('drx-', '') || 'N/A'}
              </Text>
            </Box>
          </SimpleGrid>
        </Box>
      </Box>
    ))}
  </VStack>
);

const NGBRProfileConfig = ({ profiles }: { profiles: any[] }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiDatabase} label="NGBR Profiles" value={profiles.length} colorScheme="blue" />
      <StatBox
        icon={FiCheckCircle}
        label="Enabled"
        value={profiles.filter((p) => p.isDrxEnabled).length}
        colorScheme="green"
      />
      <StatBox
        icon={FiClock}
        label="Avg Inactivity Timer"
        value={profiles[0]?.drxInactivityTmr || 'N/A'}
        colorScheme="purple"
      />
      <StatBox
        icon={FiActivity}
        label="On Duration Timer"
        value={profiles[0]?.drxOnDurationTmrInMS || 'N/A'}
        colorScheme="orange"
      />
    </SimpleGrid>

    {profiles.map((profile) => (
      <Box key={profile.id} p={4} borderWidth="1px" borderRadius="lg" bg="blue.50" borderColor="blue.200">
        <HStack mb={3} justify="space-between">
          <HStack>
            <Icon as={FiDatabase} color="blue.500" />
            <Text fontWeight="bold" fontSize="md" color="blue.700">
              NGBR Profile ID: {profile.id}
            </Text>
          </HStack>
          <HStack spacing={2}>
            {profile.isDrxEnabled !== undefined && (
              <Badge
                colorScheme={profile.isDrxEnabled ? 'green' : 'red'}
                variant="solid"
                px={3}
                py={1}
                borderRadius="full"
              >
                <HStack spacing={1}>
                  <Icon as={profile.isDrxEnabled ? FiCheckCircle : FiXCircle} boxSize={4} />
                  <Text>{profile.isDrxEnabled ? 'Enabled' : 'Disabled'}</Text>
                </HStack>
              </Badge>
            )}
            <CopyButton text={`NGBR-${profile.id}`} label="Profile ID" />
          </HStack>
        </HStack>

        {/* Timer Configuration */}
        <Box p={3} bg="white" borderRadius="md" mb={3}>
          <Text fontWeight="bold" fontSize="sm" color="gray.700" mb={2}>
            Timer Configuration
          </Text>
          <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                Inactivity Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxInactivityTmr}
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                On Duration Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxOnDurationTmrInMS}
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                DL Retx Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxRetxTmrDl}
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                UL Retx Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxRetxTmrUl}
              </Text>
            </Box>
          </SimpleGrid>
        </Box>

        {/* RTT Configuration */}
        <Box p={3} bg="white" borderRadius="md">
          <Text fontWeight="bold" fontSize="sm" color="gray.700" mb={2}>
            RTT Configuration
          </Text>
          <SimpleGrid columns={{ base: 2, md: 3 }} spacing={3}>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                DL RTT Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxRttTmrDl} slots
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                UL RTT Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxRttTmrUl} slots
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                On Duration Timer Type
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.isDrxOnDurationTmrType?.replace('drx-', '') || 'N/A'}
              </Text>
            </Box>
          </SimpleGrid>
        </Box>
      </Box>
    ))}
  </VStack>
);

const ANRProfileConfig = ({ profiles }: { profiles: any[] }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiSettings} label="ANR Profiles" value={profiles.length} colorScheme="orange" />
      <StatBox
        icon={FiCheckCircle}
        label="Enabled"
        value={profiles.filter((p) => p.isDrxEnabled).length}
        colorScheme="green"
      />
      <StatBox
        icon={FiClock}
        label="Avg Inactivity Timer"
        value={profiles[0]?.drxInactivityTmr || 'N/A'}
        colorScheme="blue"
      />
      <StatBox
        icon={FiActivity}
        label="On Duration Timer"
        value={profiles[0]?.drxOnDurationTmrInMS || 'N/A'}
        colorScheme="purple"
      />
    </SimpleGrid>

    {profiles.map((profile) => (
      <Box key={profile.id} p={4} borderWidth="1px" borderRadius="lg" bg="orange.50" borderColor="orange.200">
        <HStack mb={3} justify="space-between">
          <HStack>
            <Icon as={FiSettings} color="orange.500" />
            <Text fontWeight="bold" fontSize="md" color="orange.700">
              ANR Profile ID: {profile.id}
            </Text>
          </HStack>
          <HStack spacing={2}>
            {profile.isDrxEnabled !== undefined && (
              <Badge
                colorScheme={profile.isDrxEnabled ? 'green' : 'red'}
                variant="solid"
                px={3}
                py={1}
                borderRadius="full"
              >
                <HStack spacing={1}>
                  <Icon as={profile.isDrxEnabled ? FiCheckCircle : FiXCircle} boxSize={4} />
                  <Text>{profile.isDrxEnabled ? 'Enabled' : 'Disabled'}</Text>
                </HStack>
              </Badge>
            )}
            <CopyButton text={`ANR-${profile.id}`} label="Profile ID" />
          </HStack>
        </HStack>

        {/* Timer Configuration */}
        <Box p={3} bg="white" borderRadius="md" mb={3}>
          <Text fontWeight="bold" fontSize="sm" color="gray.700" mb={2}>
            Timer Configuration
          </Text>
          <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                Inactivity Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxInactivityTmr}
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                On Duration Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxOnDurationTmrInMS}
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                DL Retx Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxRetxTmrDl}
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                UL Retx Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxRetxTmrUl}
              </Text>
            </Box>
          </SimpleGrid>
        </Box>

        {/* RTT Configuration */}
        <Box p={3} bg="white" borderRadius="md">
          <Text fontWeight="bold" fontSize="sm" color="gray.700" mb={2}>
            RTT Configuration
          </Text>
          <SimpleGrid columns={{ base: 2, md: 3 }} spacing={3}>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                DL RTT Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxRttTmrDl} slots
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                UL RTT Timer
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.drxRttTmrUl} slots
              </Text>
            </Box>
            <Box>
              <Text fontSize="xs" color="gray.500" fontWeight="medium">
                On Duration Timer Type
              </Text>
              <Text fontSize="sm" fontWeight="bold">
                {profile.isDrxOnDurationTmrType?.replace('drx-', '') || 'N/A'}
              </Text>
            </Box>
          </SimpleGrid>
        </Box>
      </Box>
    ))}
  </VStack>
);

export const DRXConfiguration: React.FC<DRXConfigurationProps> = ({ data }) => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <Alert status="info" borderRadius="lg">
        <AlertIcon />
        <Text>No DRX configuration data available.</Text>
      </Alert>
    );
  }

  // Group profiles by type
  const vonrProfiles = data.filter((p) => p.drxProfile === 'VONR');
  const gbrProfiles = data.filter((p) => p.drxProfile === 'GBR');
  const ngbrProfiles = data.filter((p) => p.drxProfile === 'NGBR');
  const anrProfiles = data.filter((p) => p.drxProfile === 'ANR');

  return (
    <Card borderWidth="1px" borderRadius="lg" overflow="hidden" variant="outline" border="none">
      <CardBody px={2} py={0}>
        <Tabs variant="soft-rounded" colorScheme="purple" align="start">
          <TabList mt={4} mx={4} flexWrap="wrap" gap={2}>
            {vonrProfiles.length > 0 && (
              <Tab
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'purple.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
              >
                <HStack>
                  <Icon as={FiZap} boxSize={5} color="purple.500" />
                  <Text fontWeight="medium" fontSize="md">
                    VoNR
                  </Text>
                  <Badge colorScheme="purple" variant="solid" px={3} py={1} borderRadius="full">
                    <HStack spacing={1}>
                      <Icon as={FiZap} boxSize={4} />
                      <Text>{vonrProfiles.length}</Text>
                    </HStack>
                  </Badge>
                </HStack>
              </Tab>
            )}

            {gbrProfiles.length > 0 && (
              <Tab
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'green.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
              >
                <HStack>
                  <Icon as={FiActivity} boxSize={5} color="green.500" />
                  <Text fontWeight="medium" fontSize="md">
                    GBR
                  </Text>
                  <Badge colorScheme="green" variant="solid" px={3} py={1} borderRadius="full">
                    <HStack spacing={1}>
                      <Icon as={FiActivity} boxSize={4} />
                      <Text>{gbrProfiles.length}</Text>
                    </HStack>
                  </Badge>
                </HStack>
              </Tab>
            )}

            {ngbrProfiles.length > 0 && (
              <Tab
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'blue.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
              >
                <HStack>
                  <Icon as={FiDatabase} boxSize={5} color="blue.500" />
                  <Text fontWeight="medium" fontSize="md">
                    NGBR
                  </Text>
                  <Badge colorScheme="blue" variant="solid" px={3} py={1} borderRadius="full">
                    <HStack spacing={1}>
                      <Icon as={FiDatabase} boxSize={4} />
                      <Text>{ngbrProfiles.length}</Text>
                    </HStack>
                  </Badge>
                </HStack>
              </Tab>
            )}

            {anrProfiles.length > 0 && (
              <Tab
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'orange.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
              >
                <HStack>
                  <Icon as={FiSettings} boxSize={5} color="orange.500" />
                  <Text fontWeight="medium" fontSize="md">
                    ANR
                  </Text>
                  <Badge colorScheme="orange" variant="solid" px={3} py={1} borderRadius="full">
                    <HStack spacing={1}>
                      <Icon as={FiSettings} boxSize={4} />
                      <Text>{anrProfiles.length}</Text>
                    </HStack>
                  </Badge>
                </HStack>
              </Tab>
            )}
          </TabList>

          <TabPanels mt={4} mx={4}>
            {vonrProfiles.length > 0 && (
              <TabPanel p={4}>
                <VoNRProfileConfig profiles={vonrProfiles} />
              </TabPanel>
            )}

            {gbrProfiles.length > 0 && (
              <TabPanel p={4}>
                <GBRProfileConfig profiles={gbrProfiles} />
              </TabPanel>
            )}

            {ngbrProfiles.length > 0 && (
              <TabPanel p={4}>
                <NGBRProfileConfig profiles={ngbrProfiles} />
              </TabPanel>
            )}

            {anrProfiles.length > 0 && (
              <TabPanel p={4}>
                <ANRProfileConfig profiles={anrProfiles} />
              </TabPanel>
            )}
          </TabPanels>
        </Tabs>
      </CardBody>
    </Card>
  );
};
