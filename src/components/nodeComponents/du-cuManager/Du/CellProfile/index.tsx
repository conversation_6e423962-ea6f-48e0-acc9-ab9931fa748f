import React from 'react';
import {
  <PERSON><PERSON>,
  HStack,
  TabList,
  TabPanels,
  TabPanel,
  Flex,
  VStack,
  useColorModeValue,
  Card,
  CardBody,
  Tabs,
  Box,
  Icon,
  Heading,
  Tab,
  useDisclosure,
} from '@chakra-ui/react';
import { FiActivity, FiSettings, FiUser, FiEye } from 'react-icons/fi';

import MetadataComponent from '../common';
import CellProfileDetailedConfiguration from './DetailedConfiguration';
import CellProfileVitals from './Vitals';
import JsonViewerDialog from '../JsonViewerDialog';

const CellProfile = ({ data, colorScheme }: { data: any; colorScheme: string }) => {
  const { status, content } = data;
  const { spec, metadata } = content;
  const cardBg = useColorModeValue('white', 'gray.700');
  const profiles = spec.profiles || [];
  const { isOpen: isJsonViewerOpen, onOpen: onJsonViewerOpen, onClose: onJsonViewerClose } = useDisclosure();

  const tabsConfig = [
    {
      id: 'vitals',
      label: 'Cell Vitals',
      icon: FiActivity,
      component: <CellProfileVitals profile={spec.profiles?.[0]} colorScheme={colorScheme} />,
      badge: 'Live',
    },
    {
      id: 'detailed',
      label: 'Detailed Configuration',
      icon: FiSettings,
      component: <CellProfileDetailedConfiguration spec={spec.profiles?.[0]} colorScheme={colorScheme} />,
      badge: 'Advanced',
    },
  ];

  return (
    <VStack spacing={6} align="stretch">
      {/* HEADER WITH JSON VIEWER BUTTON */}
      <Flex justify="space-between" align="center" mb={1}>
        <Heading size="xl" color={`${colorScheme}.700`}>
          DU Cell Profile Configuration
        </Heading>
        <Button
          leftIcon={<Icon as={FiEye} />}
          colorScheme={colorScheme}
          variant="outline"
          size="md"
          onClick={onJsonViewerOpen}
        >
          View Raw JSON
        </Button>
      </Flex>

      {/* METADATA SECTION */}
      <MetadataComponent metadata={metadata} colorScheme={colorScheme} />

      <Card bg={cardBg} variant="outline" shadow="lg" borderColor={`${colorScheme}.200`}>
        <CardBody pt={0}>
          <Tabs variant="soft-rounded" colorScheme={colorScheme} size="lg">
            <TabList mt={3} bg="white" p={2} gap={4}>
              {profiles.map((profile: any, index: number) => (
                <Tab
                  key={profile.NRDUCellID}
                  fontWeight="bold"
                  fontSize="lg"
                  shadow="lg"
                  _hover={{
                    bg: `${colorScheme}.100`,
                    transform: 'translateY(-10px)',
                    shadow: 'lg',
                  }}
                  _selected={{
                    borderColor: `${colorScheme}.600`,
                    shadow: 'lg',
                    bg: `${colorScheme}.100`,
                  }}
                  transition="all 0.3s ease"
                >
                  <HStack spacing={2} py="2">
                    <Box
                      bg={`${colorScheme}.500`}
                      bgGradient={`linear(135deg, ${colorScheme}.300, ${colorScheme}.700)`}
                      borderRadius="full"
                      p={2}
                      shadow="md"
                    >
                      <Icon as={FiUser} boxSize={8} color="white" />
                    </Box>

                    <Heading size="xl" color={`${colorScheme}.700`}>
                      Profile {profile.id}
                    </Heading>
                  </HStack>
                </Tab>
              ))}
            </TabList>
            <TabPanels>
              {profiles.map((profile: any, index: number) => (
                <TabPanel key={profile.id} mx={5}>
                  <Flex direction="column" gap={5}>
                    <CellProfileVitals profile={profile} colorScheme={colorScheme} />

                    {/* DETAILED CONFIGURATION SECTION */}
                    <CellProfileDetailedConfiguration spec={profile} colorScheme={colorScheme} />
                  </Flex>
                </TabPanel>
              ))}
            </TabPanels>
          </Tabs>
        </CardBody>
      </Card>

      {/* JSON VIEWER DIALOG */}
      <JsonViewerDialog
        isOpen={isJsonViewerOpen}
        onClose={onJsonViewerClose}
        title="Cell Profile Configuration"
        data={data}
        colorScheme={colorScheme}
      />
    </VStack>
  );
};

export default CellProfile;
