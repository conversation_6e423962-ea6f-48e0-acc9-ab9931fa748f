import React from 'react';
import { Box, SimpleGrid, Text, Icon, Flex, HStack, Badge } from '@chakra-ui/react';
import { FiSettings } from 'react-icons/fi';
import { getConfigStatus } from './common';

interface ConfigurationStatusItem {
  label: string;
  value: boolean | string;
  icon?: any;
}

interface CommonConfigurationStatusProps {
  title?: string;
  items: ConfigurationStatusItem[];
  colorScheme: string;
  columns?: { base: number; md: number; lg?: number };
}

const StatusComponent = ({
  label,
  value,
  icon,
  colorScheme,
}: {
  label: string;
  value: boolean | string;
  icon?: any;
  colorScheme: string;
}) => {
  const isEnabled = typeof value === 'boolean' ? value : value === 'enabled';
  const statusColorScheme = isEnabled ? 'green' : 'red';

  const { status_color, status_icon, status_text } = getConfigStatus(value.toString());

  return (
    <Flex
      justify="space-between"
      align="center"
      p={3}
      borderRadius="md"
      borderWidth={1}
      borderColor={`${colorScheme}.100`}
      _hover={{
        bg: `${statusColorScheme}.100`,
        transform: 'translateY(-2px)',
        shadow: 'md',
      }}
      transition="all 0.2s ease-in-out"
    >
      <HStack spacing={2}>
        <Icon as={icon} boxSize={4} color={`${colorScheme}.500`} />
        <Text fontWeight="600" fontSize="sm">
          {label}
        </Text>
      </HStack>
      <Badge colorScheme={status_color} variant="solid" px={3} py={1} borderRadius="full">
        <HStack spacing={1}>
          <Icon as={status_icon} boxSize={4} />
          <Text>{status_text}</Text>
        </HStack>
      </Badge>
    </Flex>
  );
};

const CommonConfigurationStatus: React.FC<CommonConfigurationStatusProps> = ({
  title = 'Configuration Status',
  items,
  colorScheme,
  columns = { base: 1, md: 2, lg: 3 },
}) => {
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <Box>
      <HStack spacing={2} mb={4} align="center">
        <Box
          bg={`${colorScheme}.500`}
          bgGradient={`linear(135deg, ${colorScheme}.400, ${colorScheme}.600)`}
          borderRadius="full"
          p={2}
          shadow="md"
        >
          <Icon as={FiSettings} boxSize={4} color="white" />
        </Box>

        <Text fontSize="lg" fontWeight="bold">
          {title}
        </Text>
      </HStack>
      <SimpleGrid columns={columns} spacing={3} ml="6">
        {items.map((item, index) => (
          <StatusComponent
            key={index}
            label={item.label}
            value={item.value}
            icon={item.icon}
            colorScheme={colorScheme}
          />
        ))}
      </SimpleGrid>
    </Box>
  );
};

export default CommonConfigurationStatus;
