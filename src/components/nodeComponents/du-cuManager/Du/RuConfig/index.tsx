import React from 'react';
import { <PERSON>, But<PERSON>, Text, VStack, Alert, AlertIcon, Flex, Heading, Icon, useDisclosure } from '@chakra-ui/react';
import { FiEye } from 'react-icons/fi';

import MetadataComponent from '../common';
import RUVitals from './Vitals';
import { DetailedConfiguration } from './DetailedConfiguration';
import JsonViewerDialog from '../JsonViewerDialog';

// Main RuConfig Component
const RuConfig = ({ data, colorScheme }: { data: any; colorScheme: string }) => {
  const { isOpen: isJsonViewerOpen, onOpen: onJsonViewerOpen, onClose: onJsonViewerClose } = useDisclosure();
  if (!data || !data.content || !data.status) {
    return (
      <Alert status="error" borderRadius="lg">
        <AlertIcon />
        <Box>
          <Text fontWeight="bold">No RU Configuration Data</Text>
          <Text>The RU configuration information could not be loaded.</Text>
        </Box>
      </Alert>
    );
  }

  const { status, content } = data;
  const { spec, metadata } = content;

  return (
    <VStack spacing={6} align="stretch">
      {/* HEADER WITH JSON VIEWER BUTTON */}
      <Flex justify="space-between" align="center" mb={1}>
        <Heading size="xl" color={`${colorScheme}.700`}>
          RU Configuration
        </Heading>
        <Button
          leftIcon={<Icon as={FiEye} />}
          colorScheme={colorScheme}
          variant="outline"
          size="md"
          onClick={onJsonViewerOpen}
        >
          View Raw JSON
        </Button>
      </Flex>

      {/* METADATA SECTION */}
      <MetadataComponent metadata={metadata} colorScheme={colorScheme} />

      {/* RU VITALS SECTION */}
      <RUVitals spec={spec} colorScheme={colorScheme} />

      {/* DETAILED CONFIGURATION SECTION */}
      <DetailedConfiguration spec={spec} colorScheme={colorScheme} />

      {/* JSON VIEWER DIALOG */}
      <JsonViewerDialog
        isOpen={isJsonViewerOpen}
        onClose={onJsonViewerClose}
        title="RU Configuration"
        data={data}
        colorScheme={colorScheme}
      />
    </VStack>
  );
};

export default RuConfig;
