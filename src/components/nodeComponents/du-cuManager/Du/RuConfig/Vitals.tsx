import {
  Card,
  CardHeader,
  Icon,
  HStack,
  VStack,
  Box,
  Heading,
  Text,
  Badge,
  CardBody,
  SimpleGrid,
  Divider,
  Flex,
  Stat,
  StatLabel,
  StatNumber,
  Collapse,
} from '@chakra-ui/react';
import { useState } from 'react';
import {
  FiRadio,
  FiCheckCircle,
  FiXCircle,
  FiHash,
  FiSettings,
  FiActivity,
  FiBarChart,
  FiClock,
  FiToggleLeft,
} from 'react-icons/fi';
import { startCase } from 'lodash';
import { StatusRow } from '../common';
import CommonConfigurationStatus from '../CommonConfigurationStatus';
import CommonMetricsGrid from '../CommonMetricsComponent';

const RUVitals = ({ spec, colorScheme }: { spec: any; colorScheme: string }) => {
  const [showContent, setShowContent] = useState(true);
  const carriers = spec.carrier || [];
  // Calculate metrics
  const totalRFChains = carriers.reduce((acc: number, carrier: any) => acc + (carrier.RFChain?.length || 0), 0);
  const avgTxPower =
    carriers.length > 0
      ? carriers.reduce((acc: number, carrier: any) => {
          const chainPower = carrier.RFChain?.[0]?.configuredMaxTxPower || 0;
          return acc + chainPower;
        }, 0) / carriers.length
      : 0;

  const statusColor = spec.enabled ? 'green' : 'red';

  return (
    <Card
      variant="outline"
      shadow="lg"
      borderWidth={1}
      borderColor={`${colorScheme}.200`}
      transition="all 0.3s ease-in-out"
      _hover={{ transform: 'translateY(-3px)' }}
    >
      <CardHeader
        bg={showContent ? `${colorScheme}.100` : 'transparent'}
        py={4}
        onClick={() => setShowContent(!showContent)}
        cursor="pointer"
        shadow="md"
        transition="all 0.3s ease-in-out"
        _hover={{
          bg: `${colorScheme}.100`,
          transform: 'translateY(-3px)',
        }}
      >
        <HStack justify="space-between" align="center">
          <HStack spacing={3}>
            <Box
              bg={`${colorScheme}.500`}
              bgGradient={`linear(135deg, ${colorScheme}.400, ${colorScheme}.600)`}
              borderRadius="full"
              p={2}
              shadow="md"
            >
              <Icon as={FiRadio} boxSize={6} color="white" />
            </Box>
            <Box>
              <Heading size="md" color={`${colorScheme}.700`}>
                RU Vitals
              </Heading>
              <Text fontSize="sm" fontWeight="bold">
                An overview of runtime status and performance
              </Text>
            </Box>
          </HStack>
          <VStack spacing={1} align="end">
            <Badge colorScheme={statusColor} variant="solid" px={3} py={1} borderRadius="full">
              <HStack spacing={1}>
                <Icon as={spec.enabled ? FiCheckCircle : FiXCircle} boxSize={6} />
                <Text>{spec.enabled ? 'Enabled' : 'Disabled'}</Text>
              </HStack>
            </Badge>
          </VStack>
        </HStack>
      </CardHeader>
      <Collapse in={showContent} animate>
        {showContent && (
          <CardBody py={4}>
            <VStack spacing={6} align="stretch" px={4}>
              <CommonConfigurationStatus
                title="Configuration Status"
                items={[{ label: 'RU Configuration', value: spec.enabled, icon: FiRadio }]}
                colorScheme={colorScheme}
              />

              <Divider borderColor={`${colorScheme}.200`} />

              {/* Performance Metrics */}

              <CommonMetricsGrid
                metrics={{
                  totalCarriers: carriers.length,
                  totalRFChains: totalRFChains,
                  avgMaxTxPower: Math.round(avgTxPower),
                  configurationID: spec.id,
                }}
                colorScheme={colorScheme}
              />
            </VStack>
          </CardBody>
        )}
      </Collapse>
    </Card>
  );
};

export default RUVitals;
