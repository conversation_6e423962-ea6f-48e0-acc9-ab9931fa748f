import {
  Accordion,
  Card,
  CardBody,
  CardHeader,
  <PERSON>ing,
  HStack,
  Icon,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Box,
  Text,
  Badge,
} from '@chakra-ui/react';
import { FiSettings, FiRss } from 'react-icons/fi';
import { CarrierOverview } from './CarrierOverview';

// Centralized accordion configuration
const accordionConfig = [
  {
    id: 'carrier',
    title: 'Carrier Overview',
    icon: FiRss,
    color: 'orange',
    badge: (spec: any) => `${spec.carrier?.length || 0} carriers`,
    component: CarrierOverview,
  },
];

export const DetailedConfiguration = ({ spec, colorScheme }: { spec: any; colorScheme: string }) => {
  return (
    <Card bg="white" variant="outline" shadow="lg" borderColor={`${colorScheme}.200`}>
      <CardHeader py={6}>
        <HStack spacing={3}>
          <Box
            bg={`${colorScheme}.500`}
            bgGradient={`linear(135deg, ${colorScheme}.400, ${colorScheme}.600)`}
            borderRadius="full"
            p={2}
            shadow="md"
          >
            <Icon as={FiSettings} boxSize={6} color="white" />
          </Box>
          <Heading size="md" color={`${colorScheme}.700`}>
            RU Detailed Configuration
          </Heading>
        </HStack>
      </CardHeader>
      <CardBody pt={0}>
        <Accordion mx="4" allowMultiple>
          {accordionConfig.map((config) => (
            <AccordionItem
              key={config.id}
              borderWidth="1px"
              borderColor={`${colorScheme}.200`}
              borderRadius="lg"
              mb={3}
              shadow="md"
              _hover={{
                shadow: 'md',
                borderColor: `${colorScheme}.200`,
                transform: 'translateY(-5px)',
              }}
              transition="all 0.3s ease-in-out"
            >
              <h2>
                <AccordionButton
                  py={4}
                  px={5}
                  shadow="md"
                  borderRadius="lg"
                  _hover={{ bg: `${colorScheme}.100` }}
                  _expanded={{ bg: `${colorScheme}.100` }}
                >
                  <Box as="span" flex="1" textAlign="left" fontWeight="bold" fontSize="lg">
                    <HStack spacing={3}>
                      <Box
                        bg={`${colorScheme}.500`}
                        bgGradient={`linear(135deg, ${colorScheme}.400, ${colorScheme}.600)`}
                        borderRadius="full"
                        p={2}
                        shadow="md"
                      >
                        <Icon as={config.icon} color="white" boxSize={5} />
                      </Box>
                      <Text>{config.title}</Text>
                      {config.badge(spec) && (
                        <Badge colorScheme={colorScheme} variant="solid" borderRadius="full">
                          {config.badge(spec)}
                        </Badge>
                      )}
                    </HStack>
                  </Box>
                  <AccordionIcon />
                </AccordionButton>
              </h2>
              <AccordionPanel>
                <config.component spec={spec} />
              </AccordionPanel>
            </AccordionItem>
          ))}
        </Accordion>
      </CardBody>
    </Card>
  );
};
