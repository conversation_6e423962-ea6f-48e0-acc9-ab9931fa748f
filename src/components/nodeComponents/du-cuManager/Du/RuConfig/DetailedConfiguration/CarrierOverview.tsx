import React from 'react';
import {
  H<PERSON><PERSON>ck,
  VStack,
  Icon,
  Text,
  Badge,
  Heading,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  TableContainer,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Box,
} from '@chakra-ui/react';
import { FiRss, FiLink, FiTarget, FiArrowUp, FiArrowDown, FiZap, FiHash, FiHardDrive } from 'react-icons/fi';

interface CarrierOverviewProps {
  spec: any;
}

export const CarrierOverview: React.FC<CarrierOverviewProps> = ({ spec }) => {
  const carriers = spec.carrier || [];

  return (
    <Tabs variant="soft-rounded" colorScheme="orange" size="lg">
      <TabList bg="white" p={4} gap={2}>
        {carriers.map((carrier: any) => (
          <Tab
            key={carrier.id}
            fontWeight="bold"
            fontSize="md"
            shadow="lg"
            _selected={{ bg: 'orange.100' }}
            _hover={{
              shadow: 'lg',
              bg: 'orange.100',
              transform: 'translateY(-2px)',
            }}
            transition="all 0.3s ease"
          >
            <HStack spacing={3}>
              <Icon as={FiRss} color="orange.600" boxSize={4} />
              <Text>Carrier {carrier.id}</Text>
            </HStack>
          </Tab>
        ))}
      </TabList>
      <TabPanels>
        {carriers.map((carrier: any) => (
          <TabPanel key={carrier.id}>
            <VStack spacing={4} align="stretch" px={4} mx={4} mt={1}>
              {/* RF Chain Table */}
              <Box>
                <VStack mb={4} align="start">
                  <HStack spacing={2} mb="4">
                    <Icon as={FiHardDrive} color="orange.600" boxSize={5} />
                    <Text>TLV Bytes</Text>
                    <Text color="orange.600" fontSize="lg">
                      {carrier.RFCfgTLVBytes?.join(' | ')}
                    </Text>
                  </HStack>
                  <HStack spacing={2}>
                    <Text fontSize="md" fontWeight="bold" color="gray.700">
                      <Icon as={FiLink} mr={2} color="orange.600" boxSize={4} />
                      RF Chain Configuration
                    </Text>
                  </HStack>
                </VStack>
                <TableContainer bg="white" borderRadius="lg" shadow="sm" borderWidth={1} borderColor="orange.200">
                  <Table size="md">
                    <Thead bg="orange.50">
                      <Tr>
                        <Th>
                          <HStack>
                            <Icon as={FiHash} color="orange.600" boxSize={4} />
                            <Text>ID</Text>
                          </HStack>
                        </Th>
                        <Th>
                          <HStack>
                            <Icon as={FiTarget} color="orange.600" boxSize={4} />
                            <Text>RF Chain Index</Text>
                          </HStack>
                        </Th>
                        <Th>
                          <HStack>
                            <Icon as={FiArrowUp} color="orange.600" boxSize={4} />
                            <Text>RF TX Gain</Text>
                          </HStack>
                        </Th>
                        <Th>
                          <HStack>
                            <Icon as={FiArrowDown} color="orange.600" boxSize={4} />
                            <Text>RF RX Gain</Text>
                          </HStack>
                        </Th>
                        <Th>
                          <HStack>
                            <Icon as={FiZap} color="orange.600" boxSize={4} />
                            <Text>Max TX Power</Text>
                          </HStack>
                        </Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      {(carrier.RFChain || []).map((chain: any) => (
                        <Tr key={chain.id}>
                          <Td fontWeight="bold" color="orange.700">
                            {chain.id}
                          </Td>
                          <Td>
                            <Badge colorScheme="orange" variant="outline">
                              {chain.RFChainIdx}
                            </Badge>
                          </Td>
                          <Td>
                            <Badge colorScheme="green" variant="solid">
                              {chain.RFTxGain}
                            </Badge>
                          </Td>
                          <Td>
                            <Badge colorScheme="blue" variant="solid">
                              {chain.RFRxGain}
                            </Badge>
                          </Td>
                          <Td>
                            <Badge colorScheme="red" variant="solid" px={3}>
                              {chain.configuredMaxTxPower}
                            </Badge>
                          </Td>
                        </Tr>
                      ))}
                    </Tbody>
                  </Table>
                </TableContainer>
              </Box>
            </VStack>
          </TabPanel>
        ))}
      </TabPanels>
    </Tabs>
  );
};
