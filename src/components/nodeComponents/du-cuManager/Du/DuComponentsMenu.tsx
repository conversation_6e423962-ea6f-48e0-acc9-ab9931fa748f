// DuDashboard.js
import { useCallback, useRef, useState } from 'react';
import {
  AlertDialog,
  AlertDialogBody,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogOverlay,
  Button,
  ChakraProvider,
  Box,
  SimpleGrid,
  Card,
  CardBody,
  Heading,
  Text,
  VStack,
  HStack,
  Badge,
  Icon,
  useColorModeValue,
  CardHeader,
  Flex,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  useDisclosure,
} from '@chakra-ui/react';
import {
  FiCheckCircle,
  FiXCircle,
  FiClock,
  FiAlertTriangle,
  FiRadio,
  FiServer,
  FiSettings,
  FiWifi,
  FiActivity,
  FiArrowUp,
  FiArrowDown,
  FiLock,
  FiUnlock,
  FiHash,
  FiPlay,
  FiUser,
} from 'react-icons/fi';
import { ChevronDownIcon } from '@chakra-ui/icons';

import DeploymentDetails from './Deployment';
import AppConfig from './AppConfig';
import DuCellConfig from './CellConfig';
import RuConfig from './RuConfig';
import { CopyButton, getConfigStatus } from './common';
import CellProfile from './CellProfile';
import { AUTH_TOKEN_KEY, READ_ONLY_ACCESS_ROLES, ROLE_OF_NODE } from '../../../../data/constants';
import useLogin from '../../../../hooks/useLogin';
import { ActionPhase } from '../../../../pages/CellOverview/hooks/services/use_Orc_ServerTasks';
import { usePostActionOranDuApiCall } from '../../../../pages/CellOverview/hooks/services/use_Radysis_OranDuTasks';
import useCustomResource from '../../../../pages/OranDuCuManager/hooks/services/useCustomResource';
import { getIconForAction, getIconForActionButton } from '../actionIcons';
import { actionButtonColor, OranRuActionType } from '../OranRuActionTypes';
import { formatActionText } from '../RadioCard';
import useDeploymentFinder from '../useDeploymentFinder';
import { isEmpty } from 'lodash';
import { MdOutlineSettingsInputAntenna } from 'react-icons/md';

type UsePostActionParams = {
  local_node_id: string;
  selectedOption: ActionPhase | '';
  actionType: OranRuActionType | '';
  caller: string;
  deploymentInfo: any;
  nodeType: string;
  setTriggerGetAction: (value: boolean) => void;
  setPostActionData: (value: any) => void;
};

const usePostAction = ({
  local_node_id,
  selectedOption,
  actionType,
  caller,
  deploymentInfo,
  nodeType,
  setTriggerGetAction,
  setPostActionData,
}: UsePostActionParams) => {
  const { triggerPostAction, isMutating } = usePostActionOranDuApiCall(
    deploymentInfo?.deploymentName || '',
    nodeType,
    actionType,
    setTriggerGetAction,
    setPostActionData
  );

  const isDeploymentReady = deploymentInfo?.deploymentName && deploymentInfo?.siteName;

  const initiatePostAction = useCallback(() => {
    if (selectedOption && actionType && isDeploymentReady) {
      triggerPostAction();
    }
  }, [selectedOption, actionType, isDeploymentReady, triggerPostAction]);

  return { initiatePostAction, isMutating };
};

const ActionButton = ({
  actionType,
  onClick,
  isDisabled,
}: {
  actionType: OranRuActionType;
  onClick: () => void;
  isDisabled: boolean;
}) => {
  const buttonIcon = actionType && getIconForActionButton(actionType);
  const buttonColor = actionButtonColor[actionType];
  return (
    <Button
      bg={buttonColor}
      color="white"
      variant="solid"
      size="md"
      p="4"
      mr="2"
      shadow="lg"
      onClick={onClick}
      _hover={{ bg: buttonColor, opacity: 0.9, transform: 'translateY(-5px)' }}
      _disabled={{ bg: buttonColor, opacity: 0.3, cursor: 'not-allowed' }}
      _focus={{ boxShadow: 'lg' }}
      isDisabled={isDisabled}
    >
      <Box ml="1" p="2">
        {buttonIcon ? getIconForAction(buttonIcon) : null}
      </Box>
      <Text>{formatActionText(buttonIcon || '')}</Text>
    </Button>
  );
};

// Dummy payload in case none is provided
const defaultPayload = {
  Split6DuDeployment: {
    status: {},
    content: {
      metadata: {},
      spec: { network: { F1Uinterfaces: [], FHinterfaces: [] } },
      status: { podStatus: {}, cellStatus: [] },
    },
  },
  Split6DuAppConfig: { status: {}, content: { metadata: {}, spec: { F1Mapping: [], duLog: [] }, status: {} } },
  DuCellConfig: { status: {}, content: { metadata: {}, spec: { NRDUCell: [] }, status: {} } },
  DuCellProfile: {
    status: {},
    content: { metadata: {}, spec: { profiles: [{ DrxData: [], beamIdMapping: {}, mac: {}, xran: {} }] }, status: {} },
  },
  Split6RuConfig: { status: {}, content: { metadata: {}, spec: { carrier: [] }, status: {} } },
};

// Helper component for status badges
const StatusBadge = ({ status }: { status: string }) => {
  const statusMap = {
    up: { color: 'green', icon: FiArrowUp },
    down: { color: 'red', icon: FiArrowDown },
    enabled: { color: 'green', icon: FiCheckCircle },
    enable: { color: 'green', icon: FiCheckCircle },
    disabled: { color: 'red', icon: FiXCircle },
    disable: { color: 'red', icon: FiXCircle },
    ready: { color: 'green', icon: FiCheckCircle },
    success: { color: 'green', icon: FiCheckCircle },
    failed: { color: 'red', icon: FiXCircle },
    locked: { color: 'red', icon: FiLock },
    lock: { color: 'red', icon: FiLock },
    unlocked: { color: 'green', icon: FiUnlock },
    unlock: { color: 'green', icon: FiUnlock },
    pendingapp: { color: 'yellow', icon: FiClock },
    pending: { color: 'yellow', icon: FiClock },
  };

  const s = status ? status.toLowerCase().replace(/\s+/g, '') : 'unknown';
  const { color, icon } = (statusMap as any)[s] || { color: 'gray', icon: FiAlertTriangle };

  return (
    <Badge colorScheme={color} p={1.5} borderRadius="md" display="flex" alignItems="center">
      <Icon as={icon} mr={1.5} />
      {status}
    </Badge>
  );
};

// Compact Component Cards
const CompactCard = ({
  title,
  status,
  hash,
  icon,
  isActive,
  onClick,
  cardTheme,
}: {
  title: string;
  status: string;
  hash?: string;
  icon: any;
  isActive: boolean;
  onClick: () => void;
  cardTheme: string;
}) => {
  const { status_color, status_icon, status_text } = getConfigStatus(status.toString());

  return (
    <Card
      variant="outline"
      cursor="pointer"
      _hover={{
        shadow: 'lg',
        bg: `${cardTheme}.100`,
        transform: 'translateY(-10px)',
      }}
      bg={isActive ? `${cardTheme}.100` : 'white'}
      transition="all 0.3s ease"
      onClick={onClick}
      position="relative"
      overflow="hidden"
      borderRadius="xl"
      shadow="xl"
    >
      <CardBody py={1} px={1} position="relative">
        <VStack>
          <HStack spacing={1.5} align="center" pt={4}>
            <Box
              bg={`${cardTheme}.500`}
              bgGradient={`linear(135deg, ${cardTheme}.400, ${cardTheme}.600)`}
              borderRadius="full"
              p={3}
              shadow="md"
            >
              <Icon as={icon} boxSize={8} color="white" />
            </Box>

            <Text
              fontWeight={isActive ? 'bold' : 'semibold'}
              color={isActive ? `${cardTheme}.600` : `${cardTheme}.300`}
              fontSize="xl"
              textAlign="center"
              textShadow={isActive ? '0 1px 2px rgba(0,0,0,0.1)' : 'none'}
            >
              {title}
            </Text>

            <Badge
              colorScheme={status_color}
              p={2}
              borderRadius="full"
              display="flex"
              alignItems="center"
              fontSize={'sm'}
              fontWeight="bold"
              variant="solid"
            >
              <Icon as={status_icon} mr={2} boxSize={5} />
              {status}
            </Badge>
          </HStack>
        </VStack>
        {hash && (
          <HStack spacing={1} align="end" justify="end" pt={3} pb={2}>
            <Icon as={FiHash} color="green.500" boxSize={4} />
            <Text fontSize="xs" color="green.600" fontFamily="mono">
              Hash: {hash}
            </Text>
            <CopyButton text={hash} label="Hash" />
          </HStack>
        )}
      </CardBody>
    </Card>
  );
};

// Main Dashboard Component
const DuComponentsMenu = ({
  payload,
  node_serial_no,
  nodeType = ROLE_OF_NODE.DU,
}: {
  payload?: any;
  node_serial_no?: string;
  nodeType?: string;
}) => {
  const [selectedComponent, setSelectedComponent] = useState<string | null>('cellconfig');
  const [actionType, setActionType] = useState<OranRuActionType | ''>('');
  const [selectedOption, setSelectedOption] = useState<ActionPhase | ''>('');
  const [triggerGetAction, setTriggerGetAction] = useState<boolean>(false);
  const [postActionData, setPostActionData] = useState<any[] | null>(null);
  const { checkApplicationAccess } = useLogin(AUTH_TOKEN_KEY);
  const isReadOnlyAccess = checkApplicationAccess(READ_ONLY_ACCESS_ROLES);
  const cancelRef = useRef<HTMLButtonElement>(null);
  const {
    isOpen: isSelectReasonAlertOpen,
    onOpen: onSelectReasonAlertOpen,
    onClose: onSelectReasonAlertClose,
  } = useDisclosure();

  const local_node_id = node_serial_no || '';
  const caller = 'DuComponentsMenu';
  const clockStatus = payload?.DuCellConfig?.content?.spec?.NRDUCell[0]?.administrativeState;

  // API calls
  const { getAllDeployments } = useCustomResource();

  //Get all Deployments
  const { data: allDeployments, isLoading: isAllDeploymentsLoading } = getAllDeployments('edit');

  const deploymentInfo = useDeploymentFinder(payload, allDeployments, nodeType);

  //API
  const { initiatePostAction, isMutating } = usePostAction({
    local_node_id,
    selectedOption,
    actionType,
    caller,
    deploymentInfo: isAllDeploymentsLoading ? null : deploymentInfo,
    nodeType,
    setTriggerGetAction,
    setPostActionData,
  });

  const handleActionButtonClick = (actionType: OranRuActionType) => {
    onSelectReasonAlertOpen();
    setActionType(actionType);
  };

  const handleSelect = (value: string) => {
    if (Object.values(ActionPhase).includes(value as ActionPhase)) {
      setSelectedOption((prevState) => (prevState === value ? '' : (value as ActionPhase)));
    }
  };

  const handleConfirmClick = () => {
    onSelectReasonAlertClose();
    initiatePostAction();
  };

  const handleCancelClick = () => {
    onSelectReasonAlertClose();
  };

  const data = payload || defaultPayload;
  const cardBg = useColorModeValue('gray.50', 'gray.800');
  const components = [
    {
      id: 'cellconfig',
      title: 'Cell Config',
      status: data.DuCellConfig.status.configStatus || 'unknown',
      hash: data.DuCellConfig.status.specHash,
      icon: FiRadio,
      theme: 'purple',
      component: <DuCellConfig data={data.DuCellConfig} colorScheme="purple" />,
    },
    {
      id: 'cellprofile',
      title: 'Cell Profile',
      status: data.DuCellProfile.status.configStatus || 'unknown',
      hash: data.DuCellProfile.status.id,
      icon: FiUser,
      theme: 'pink',
      component: <CellProfile data={data.DuCellProfile} colorScheme="pink" />,
    },
    {
      id: 'ruconfig',
      title: 'RU Config',
      status: data.Split6RuConfig.status.configStatus || 'unknown',
      hash: data.Split6RuConfig.status.id,
      icon: MdOutlineSettingsInputAntenna,
      theme: 'blue',
      component: <RuConfig data={data.Split6RuConfig} colorScheme="blue" />,
    },
    {
      id: 'appconfig',
      title: 'App Config',
      status: data.Split6DuAppConfig.status.configStatus || 'unknown',
      hash: data.Split6DuAppConfig.status.specHash,
      icon: FiSettings,
      theme: 'orange',
      component: <AppConfig data={data.Split6DuAppConfig} colorScheme="orange" />,
    },
    {
      id: 'deployment',
      title: 'Deployment',
      status: data.Split6DuDeployment.status.deploymentStatus || 'unknown',
      hash: data.Split6DuDeployment.status.specHash,
      icon: FiServer,
      theme: 'green',
      component: <DeploymentDetails data={data.Split6DuDeployment} colorScheme="green" />,
    },
  ];

  const selectedComponentData = components.find((c) => c.id === selectedComponent);

  return (
    <ChakraProvider>
      <Box bg={cardBg} p={{ base: 4, md: 6 }} minH="100vh">
        <VStack align="start" mx="auto">
          {/* Compact Cards Row with Actions */}
          <Flex mb={6} flexDirection={{ base: 'column', md: 'column' }}>
            {/* Action Buttons */}
            {payload && node_serial_no && (
              <VStack spacing={3} align="end" mr="4" mb="4">
                <HStack>
                  <ActionButton
                    actionType={OranRuActionType.RESTART}
                    onClick={() => handleActionButtonClick(OranRuActionType.RESTART)}
                    isDisabled={
                      isMutating ||
                      isReadOnlyAccess ||
                      !payload ||
                      isEmpty(payload) ||
                      !allDeployments ||
                      isEmpty(allDeployments)
                    }
                  />
                  {nodeType === ROLE_OF_NODE.DU && (
                    <>
                      <ActionButton
                        actionType={OranRuActionType.LOCK}
                        onClick={() => handleActionButtonClick(OranRuActionType.LOCK)}
                        isDisabled={
                          isMutating ||
                          isReadOnlyAccess ||
                          clockStatus === 'Locked' ||
                          !payload ||
                          isEmpty(payload) ||
                          !allDeployments ||
                          isEmpty(allDeployments)
                        }
                      />
                      <ActionButton
                        actionType={OranRuActionType.UNLOCK}
                        onClick={() => handleActionButtonClick(OranRuActionType.UNLOCK)}
                        isDisabled={
                          isMutating ||
                          isReadOnlyAccess ||
                          clockStatus === 'Unlocked' ||
                          !payload ||
                          isEmpty(payload) ||
                          !allDeployments ||
                          isEmpty(allDeployments)
                        }
                      />
                    </>
                  )}
                </HStack>
              </VStack>
            )}

            <SimpleGrid columns={5} spacing={3} mx="0" flex="1" w="100%">
              {components.map((component) => (
                <CompactCard
                  key={component.id}
                  title={component.title}
                  status={component.status}
                  icon={component.icon}
                  isActive={selectedComponent === component.id}
                  onClick={() => setSelectedComponent(component.id)}
                  cardTheme={component.theme}
                  hash={component.hash}
                />
              ))}
            </SimpleGrid>
          </Flex>

          {/* Selected Component Content */}
          {selectedComponent && selectedComponentData && (
            <Box w="100%" ml="4">
              {selectedComponentData.component}
            </Box>
          )}
        </VStack>
      </Box>

      {/* Action button dialogue */}
      <AlertDialog
        isOpen={isSelectReasonAlertOpen}
        onClose={onSelectReasonAlertClose}
        isCentered
        leastDestructiveRef={cancelRef}
        size="lg"
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              <Flex flexDirection="row">Select a reason for the {actionType} action</Flex>
            </AlertDialogHeader>
            <AlertDialogBody>
              <Menu>
                <MenuButton
                  as={Button}
                  rightIcon={<Icon as={ChevronDownIcon} />}
                  variant="outline"
                  colorScheme={actionButtonColor[actionType as keyof typeof actionButtonColor]}
                  size="sm"
                >
                  {selectedOption || 'Click to select an option'}
                </MenuButton>
                <MenuList>
                  {Object.entries(ActionPhase).map(([key, value]) => (
                    <MenuItem
                      key={key}
                      onClick={() => handleSelect(value)}
                      _hover={{
                        backgroundColor:
                          actionType && actionButtonColor[actionType] ? actionButtonColor[actionType] : 'gray.500',
                        color: 'white',
                      }}
                    >
                      {value}
                    </MenuItem>
                  ))}
                </MenuList>
              </Menu>
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button onClick={handleCancelClick} colorScheme="blue" ref={cancelRef}>
                Cancel
              </Button>
              <Button
                bg={actionType && actionButtonColor[actionType] ? actionButtonColor[actionType] : 'gray.500'}
                color="white"
                _hover={{
                  bg: actionType && actionButtonColor[actionType] ? actionButtonColor[actionType] : 'gray.500',
                  opacity: 0.9,
                }}
                onClick={handleConfirmClick}
                isDisabled={isEmpty(selectedOption)}
                _disabled={{
                  bg: actionType && actionButtonColor[actionType] ? actionButtonColor[actionType] : 'gray.500',
                  opacity: 0.3,
                  cursor: 'not-allowed',
                }}
                ml={3}
              >
                Confirm
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </ChakraProvider>
  );
};

export default DuComponentsMenu;
