import React from 'react';
import { Box, SimpleGrid, Text, Icon, Flex, HStack, Badge, Stat, StatLabel, StatNumber } from '@chakra-ui/react';
import { FiSettings, FiCheckCircle, FiXCircle, FiToggleLeft, FiBarChart } from 'react-icons/fi';

// Common Metrics Grid Component
interface MetricItem {
  key: string;
  value: any;
  unit?: string;
  format?: 'number' | 'string' | 'percentage' | 'bytes' | 'custom';
}

interface CommonMetricsGridProps {
  title?: string;
  metrics: Record<string, any> | MetricItem[];
  colorScheme: string;
  columns?: { base: number; md: number; lg?: number };
  icon?: any;
}

const MetricCard = ({
  label,
  value,
  unit = '',
  format = 'auto',
  colorScheme,
}: {
  label: string;
  value: any;
  unit?: string;
  format?: string;
  colorScheme: string;
}) => {
  const formatValue = (val: any, fmt: string, unitStr: string) => {
    if (val === null || val === undefined) return 'N/A';

    switch (fmt) {
      case 'number':
        return typeof val === 'number' ? val.toLocaleString() + unitStr : String(val) + unitStr;
      case 'percentage':
        return `${val}%`;
      case 'bytes':
        if (typeof val === 'number') {
          if (val >= 1024 * 1024 * 1024) return `${(val / (1024 * 1024 * 1024)).toFixed(2)} GB`;
          if (val >= 1024 * 1024) return `${(val / (1024 * 1024)).toFixed(2)} MB`;
          if (val >= 1024) return `${(val / 1024).toFixed(2)} KB`;
          return `${val} B`;
        }
        return String(val);
      default:
        return typeof val === 'number' ? val.toLocaleString() + unitStr : String(val) + unitStr;
    }
  };

  return (
    <Stat
      p={3}
      borderRadius="lg"
      borderWidth={1}
      borderColor={`${colorScheme}.100`}
      textAlign="center"
      bg="white"
      _hover={{
        bg: `${colorScheme}.50`,
        transform: 'translateY(-2px)',
        shadow: 'lg',
      }}
      transition="all 0.3s ease"
    >
      <StatLabel fontSize="xs" color={`${colorScheme}.600`} fontWeight="bold" textTransform="capitalize">
        {label.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase())}
      </StatLabel>
      <StatNumber fontSize="lg" color={`${colorScheme}.700`} fontFamily="mono">
        {formatValue(value, format, unit)}
      </StatNumber>
    </Stat>
  );
};

export const CommonMetricsGrid: React.FC<CommonMetricsGridProps> = ({
  title = 'Performance Metrics',
  metrics,
  colorScheme,
  columns = { base: 2, md: 3, lg: 4 },
  icon = FiBarChart,
}) => {
  if (!metrics) {
    return null;
  }

  // Convert metrics to array format if it's an object
  const metricsArray = Array.isArray(metrics)
    ? metrics
    : Object.entries(metrics).map(([key, value]) => ({ key, value }));

  if (metricsArray.length === 0) {
    return null;
  }

  return (
    <Box>
      <HStack spacing={2} mb={4} align="center">
        <Box
          bg={`${colorScheme}.500`}
          bgGradient={`linear(135deg, ${colorScheme}.400, ${colorScheme}.600)`}
          borderRadius="full"
          p={2}
          shadow="md"
        >
          <Icon as={icon} boxSize={4} color="white" />
        </Box>

        <Text fontSize="lg" fontWeight="bold">
          {title}
        </Text>
      </HStack>
      <SimpleGrid columns={columns} spacing={3} ml="6">
        {metricsArray.map((metric, index) => (
          <MetricCard
            key={index}
            label={metric.key}
            value={metric.value}
            unit={metric.unit}
            format={metric.format}
            colorScheme={colorScheme}
          />
        ))}
      </SimpleGrid>
    </Box>
  );
};

export default CommonMetricsGrid;
