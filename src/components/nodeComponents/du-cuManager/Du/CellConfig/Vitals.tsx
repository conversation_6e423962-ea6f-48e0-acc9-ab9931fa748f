import { useState } from 'react';
import {
  Box,
  SimpleGrid,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Text,
  VStack,
  HStack,
  Icon,
  useColorModeValue,
  Divider,
  Stat,
  StatLabel,
  StatNumber,
  Collapse,
} from '@chakra-ui/react';
import { FiSettings, FiActivity, FiShield, FiRadio, FiUnlock, FiTarget, FiBarChart } from 'react-icons/fi';

import { StatusRow } from '../common';
import CommonConfigurationStatus from '../CommonConfigurationStatus';
import CommonMetricsGrid from '../CommonMetricsComponent';
import { startCase } from 'lodash';

// Metrics Component
const MetricsGrid = ({ metrics, colorScheme }: { metrics: any; colorScheme: string }) => {
  return (
    <SimpleGrid columns={{ base: 2, md: 3 }} spacing={4}>
      {Object.entries(metrics).map(([key, value]) => (
        <Stat
          key={key}
          p={3}
          borderRadius="lg"
          borderWidth={1}
          borderColor={`${colorScheme}.100`}
          textAlign="center"
          _hover={{ bg: `${colorScheme}.100`, transform: 'translateY(-3px)', shadow: 'lg' }}
          transition="all 0.3s ease"
        >
          <StatLabel fontSize="xs" color={`${colorScheme}.500`} fontWeight="bold">
            {startCase(key)}
          </StatLabel>
          <StatNumber fontSize="lg" color={`${colorScheme}.700`}>
            {typeof value === 'number' ? value.toLocaleString() : String(value)}
          </StatNumber>
        </Stat>
      ))}
    </SimpleGrid>
  );
};

const CellVitals = ({ cell, cellIndex, colorScheme }: { cell: any; cellIndex: number; colorScheme: string }) => {
  const [showContent, setShowContent] = useState(true);

  return (
    <VStack spacing={6} align="stretch">
      {/* Cell Status & Metrics */}
      <Card variant="outline" shadow="lg" borderColor={`${colorScheme}.200`}>
        <CardHeader
          py={4}
          onClick={() => setShowContent(!showContent)}
          bg={showContent ? `${colorScheme}.100` : 'transparent'}
          _hover={{
            bg: `${colorScheme}.100`,
            transform: 'translateY(-3px)',
            shadow: 'lg',
          }}
          shadow="lg"
          cursor="pointer"
          transition="all 0.3s ease"
        >
          <HStack spacing={3}>
            <Box
              bg={`${colorScheme}.500`}
              bgGradient={`linear(135deg, ${colorScheme}.400, ${colorScheme}.600)`}
              borderRadius="full"
              p={2}
              shadow="md"
            >
              <Icon as={FiActivity} boxSize={6} color="white" />
            </Box>

            <Box>
              <Heading size="md" color={`${colorScheme}.700`}>
                Cell {cell.NRDUCellID} - Vitals
              </Heading>
              <Text fontSize="sm" fontWeight="bold">
                An overview of runtime status and performance
              </Text>
            </Box>
          </HStack>
        </CardHeader>
        <Collapse in={showContent} animateOpacity>
          <CardBody py={4}>
            <VStack spacing={6} align="stretch" p={4}>
              {/* Configuration Status */}
              <CommonConfigurationStatus
                title="Cell Status Configuration"
                colorScheme={colorScheme}
                columns={{ base: 1, md: 2, lg: 3 }}
                items={[
                  {
                    label: 'Administrative State',
                    value: cell.administrativeState,
                    icon: FiUnlock,
                  },
                  {
                    label: 'Cell Barred',
                    value: !cell.cellBarred,
                    icon: FiShield,
                  },
                  {
                    label: 'EMS Heartbeat Monitor',
                    value: cell.emsHbmon,
                    icon: FiActivity,
                  },
                ]}
              />

              <Divider borderColor={`${colorScheme}.200`} />

              {/* Performance & Radio Metrics */}
              <CommonMetricsGrid
                title="Performance & Radio Metrics"
                colorScheme={colorScheme}
                columns={{ base: 2, md: 3, lg: 3 }}
                metrics={{
                  maxNumOfUes: cell.maxNumUes,
                  maxNumOfCaUes: cell.maxNumOfCaUes,
                  nRPCI: cell.nRPCI,
                  nSsbPower: `${cell.nSsbPwr} dBm`,
                  numOfCoordinateCells: cell.numCoOrdinateCells,
                  nTimingAdvanceOffset: cell.nTimingAdvanceOffset,
                }}
              />
            </VStack>
          </CardBody>
        </Collapse>
      </Card>
    </VStack>
  );
};

export default CellVitals;
