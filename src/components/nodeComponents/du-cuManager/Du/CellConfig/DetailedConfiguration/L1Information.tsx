import React from 'react';
import {
  <PERSON>,
  H<PERSON>tack,
  VStack,
  Icon,
  Text,
  Badge,
  SimpleGrid,
  Card,
  CardBody,
  Tabs,
  TabList,
  TabPanel,
  TabPanels,
  Tab,
  Alert,
  AlertIcon,
  Stat,
  StatLabel,
  StatNumber,
  As,
  Flex,
} from '@chakra-ui/react';
import { FiRadio, FiClock, FiSettings, FiShield, FiInfo, FiCheckCircle, FiBarChart } from 'react-icons/fi';
import { getConfigStatus } from '../../common';

interface CellConfigL1InformationProps {
  cell: any;
}

interface UETmr {
  n310: string;
  n311: string;
  t300: string;
  t301: string;
  t304: string;
  t310: string;
  t311: string;
  t319: string;
}

interface CellSelectionInfo {
  qQualMin: { rsrpLvl: number };
  qRxLevMin: { rsrpLvl: number };
}

interface UACBarringInfo {
  uacBarringFactor: string;
  uacBarringForAccessId: number;
  uacBarringTime: string;
}

interface UACBarringPerCat {
  accessCatagory: number;
  maxBarringSetIndex: number;
}

interface UACBarrierInfo {
  isUacBarringCommonEn: boolean;
  uacBarringInfoSet: UACBarringInfo[];
  uacBarringPerCat: UACBarringPerCat[];
}

interface SIB1Params {
  cellSelectionInfo: CellSelectionInfo;
  isUacBarringEn: boolean;
  sib1Periodicity: string;
  uacBarrierInfo: UACBarrierInfo;
  ueTmr: UETmr;
}

interface L1Info {
  sib1Params: SIB1Params;
}

const StatBox = ({
  icon,
  label,
  value,
  colorScheme,
  isValueStatus,
}: {
  icon: As;
  label: string;
  value: string | number;
  colorScheme: string;
  isValueStatus?: boolean;
}) => {
  const { status_color, status_icon, status_text } = getConfigStatus(value.toString());
  return (
    <Stat
      p={3}
      bg={`${colorScheme}.50`}
      borderRadius="md"
      textAlign="center"
      borderWidth="1px"
      borderColor={`${colorScheme}.100`}
    >
      <StatLabel fontSize="sm" color={`${colorScheme}.600`} fontWeight="medium">
        <HStack justify="center" spacing={1.5}>
          <Icon as={icon} />
          <Text>{label}</Text>
        </HStack>
      </StatLabel>
      {isValueStatus ? (
        <Badge colorScheme={status_color} variant="solid" px={3} py={1} borderRadius="full">
          <HStack spacing={1}>
            <Icon as={status_icon} boxSize={4} />
            <Text>{status_text}</Text>
          </HStack>
        </Badge>
      ) : (
        <StatNumber fontSize="xl" color={`${colorScheme}.800`} fontFamily="mono" mt={1}>
          {value}
        </StatNumber>
      )}
    </Stat>
  );
};

const CellSelectionDetails = ({ sib1Params }: { sib1Params: SIB1Params }) => {
  return (
    <VStack spacing={4} align="stretch">
      <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
        <StatBox
          icon={FiRadio}
          label="Q-Qual Min"
          value={`${sib1Params.cellSelectionInfo.qQualMin.rsrpLvl} dBm`}
          colorScheme="blue"
        />
        <StatBox
          icon={FiRadio}
          label="Q-RxLev Min"
          value={`${sib1Params.cellSelectionInfo.qRxLevMin.rsrpLvl} dBm`}
          colorScheme="green"
        />
        <StatBox icon={FiClock} label="SIB1 Periodicity" value={sib1Params.sib1Periodicity} colorScheme="purple" />
        <StatBox
          icon={sib1Params.isUacBarringEn ? FiShield : FiCheckCircle}
          label="UAC Barring"
          value={sib1Params.isUacBarringEn ? 'Enabled' : 'Disabled'}
          colorScheme={sib1Params.isUacBarringEn ? 'red' : 'green'}
          isValueStatus={true}
        />
      </SimpleGrid>

      <VStack spacing={2} align="stretch" p={3} borderRadius="lg" borderWidth="1px">
        <HStack px={1}>
          <Icon as={FiClock} color="yellow.500" />
          <Text fontWeight="bold" fontSize="md" color="gray.600">
            UE Timer Configuration
          </Text>
        </HStack>
        <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3}>
          {Object.entries(sib1Params.ueTmr).map(([key, value]) => (
            <Box key={key} p={2} bg="gray.50" borderRadius="md">
              <Text fontSize="xs" color="gray.500" fontWeight="medium" textTransform="uppercase">
                {key.toUpperCase()}
              </Text>
              <Text fontSize="sm" fontWeight="bold" color="gray.700" fontFamily="mono">
                {value}
              </Text>
            </Box>
          ))}
        </SimpleGrid>
      </VStack>
    </VStack>
  );
};

const UACBarringDetails = ({ uacBarrierInfo }: { uacBarrierInfo: UACBarrierInfo }) => {
  return (
    <VStack spacing={4} align="stretch">
      <SimpleGrid columns={{ base: 2, md: 3 }} spacing={4}>
        <StatBox
          icon={uacBarrierInfo.isUacBarringCommonEn ? FiShield : FiCheckCircle}
          label="Common Barring"
          value={uacBarrierInfo.isUacBarringCommonEn ? 'Enabled' : 'Disabled'}
          colorScheme={uacBarrierInfo.isUacBarringCommonEn ? 'red' : 'green'}
          isValueStatus={true}
        />
        <StatBox
          icon={FiBarChart}
          label="Barring Info Sets"
          value={uacBarrierInfo.uacBarringInfoSet.length}
          colorScheme="blue"
        />
        <StatBox
          icon={FiSettings}
          label="Categories"
          value={uacBarrierInfo.uacBarringPerCat.length}
          colorScheme="purple"
        />
      </SimpleGrid>

      <VStack spacing={2} align="stretch" p={3} borderRadius="lg" borderWidth="1px">
        <HStack px={1}>
          <Icon as={FiInfo} color="orange.500" />
          <Text fontWeight="bold" fontSize="md" color="gray.600">
            UAC Barring Info Sets
          </Text>
        </HStack>
        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={3}>
          {uacBarrierInfo.uacBarringInfoSet.map((info, index) => (
            <Box key={index} p={3} bg="orange.50" borderRadius="md" borderWidth="1px" borderColor="orange.200">
              <VStack spacing={2} align="stretch">
                <HStack justify="space-between">
                  <Text fontSize="sm" fontWeight="bold" color="orange.700">
                    Set {index + 1}
                  </Text>
                  <Badge colorScheme="orange" variant="solid">
                    Access ID: {info.uacBarringForAccessId}
                  </Badge>
                </HStack>
                <HStack spacing={2}>
                  <Badge colorScheme="blue" variant="subtle">
                    Factor: {info.uacBarringFactor.replace('UAC_BARRING_FACTOR_', '')}
                  </Badge>
                  <Badge colorScheme="purple" variant="subtle">
                    Time: {info.uacBarringTime.replace('UAC_BARRING_TIME_', '')}
                  </Badge>
                </HStack>
              </VStack>
            </Box>
          ))}
        </SimpleGrid>
      </VStack>

      <VStack spacing={2} align="stretch" p={3} borderRadius="lg" borderWidth="1px">
        <HStack px={1}>
          <Icon as={FiSettings} color="purple.500" />
          <Text fontWeight="bold" fontSize="md" color="gray.600">
            Access Categories
          </Text>
        </HStack>
        <SimpleGrid columns={{ base: 2, md: 3 }} spacing={2}>
          {uacBarrierInfo.uacBarringPerCat.map((cat, index) => (
            <Box key={index} p={2} bg="purple.50" borderRadius="md" borderWidth="1px" borderColor="purple.200">
              <Flex justify="space-between" align="center">
                <Text fontSize="sm" fontWeight="bold" color="purple.700">
                  Cat {cat.accessCatagory}
                </Text>
                <Badge colorScheme="purple" variant="solid" size="sm">
                  Max: {cat.maxBarringSetIndex}
                </Badge>
              </Flex>
            </Box>
          ))}
        </SimpleGrid>
      </VStack>
    </VStack>
  );
};

export const L1Information: React.FC<CellConfigL1InformationProps> = ({ cell }) => {
  const l1Info: L1Info | undefined = cell.l1Info;

  if (!l1Info || !l1Info.sib1Params) {
    return (
      <Alert status="info" borderRadius="lg">
        <AlertIcon />
        <Text>No L1 information data available.</Text>
      </Alert>
    );
  }

  const tabData = [
    {
      id: 'cell-selection',
      title: 'Cell Selection & Timers',
      icon: FiRadio,
      component: () => <CellSelectionDetails sib1Params={l1Info.sib1Params} />,
    },
    {
      id: 'uac-barring',
      title: 'UAC Barring',
      icon: FiShield,
      component: () => <UACBarringDetails uacBarrierInfo={l1Info.sib1Params.uacBarrierInfo} />,
    },
  ];

  return (
    <Card variant="outline" bg="white" border="none">
      <CardBody>
        <Tabs variant="soft-rounded" colorScheme="yellow">
          <TabList display="flex" flexDirection="row" gap={2}>
            {tabData.map((tab) => (
              <Tab
                key={tab.id}
                shadow="lg"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'yellow.200', transform: 'translateY(-5px)' }}
                _selected={{ bg: 'yellow.200' }}
                transition="all 0.3s ease-in-out"
              >
                <HStack spacing={2}>
                  <Icon as={tab.icon} boxSize={6} color="yellow.500" />
                  <Text>{tab.title}</Text>
                </HStack>
              </Tab>
            ))}
          </TabList>
          <TabPanels mt={4}>
            {tabData.map((tab) => (
              <TabPanel key={tab.id} p={4} mx={4} mt={4}>
                <tab.component />
              </TabPanel>
            ))}
          </TabPanels>
        </Tabs>
      </CardBody>
    </Card>
  );
};
