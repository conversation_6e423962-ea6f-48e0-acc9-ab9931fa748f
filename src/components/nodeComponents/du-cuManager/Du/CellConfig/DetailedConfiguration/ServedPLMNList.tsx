import React from 'react';
import {
  Box,
  HStack,
  VStack,
  Icon,
  Text,
  Badge,
  Divider,
  Flex,
  Card,
  CardBody,
  Tabs,
  TabList,
  TabPanel,
  TabPanels,
  Tab,
  Alert,
  AlertIcon,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  As,
} from '@chakra-ui/react';
import { FiGlobe, FiTarget, FiCheckCircle, FiXCircle, FiGrid, FiLayers, FiHash } from 'react-icons/fi';
import { CopyButton } from '../../common';

interface Slice {
  id: number;
  sliceIdx: number;
  sst: number;
  sd: number;
  duSliceGroupId: number;
}

interface PLMNId {
  id: number;
  MCC: string;
  MNC: string;
  plmnActivationFlag: boolean;
  sliceList: Slice[];
}

interface ServedPLMN {
  id: number;
  cellReservedOperatorUse: string;
  ranac: number;
  tacBitMap: number;
  pLMNIdList: PLMNId[];
}

interface ServedPLMNListProps {
  cell: {
    servedPlmnList?: ServedPLMN[];
  };
}

const SliceDisplay = ({ slice }: { slice: Slice }) => {
  return (
    <Box
      p={3}
      bg="gray.50"
      borderRadius="lg"
      borderWidth="1px"
      borderColor="gray.200"
      transition="all 0.2s ease-in-out"
      _hover={{
        bg: 'gray.100',
        borderColor: 'purple.300',
      }}
    >
      <Flex justify="space-between" align="start" wrap="wrap" gap={2} flexDirection="column">
        <HStack spacing={2}>
          <Icon as={FiGrid} color="purple.500" boxSize={4} />
          <Text fontSize="sm" fontWeight="semibold" color="gray.700">
            Slice {slice.sliceIdx}
          </Text>
        </HStack>

        <HStack spacing={2} fontSize="xs">
          <Badge colorScheme="purple" variant="subtle" px={2} py={1}>
            SST: {slice.sst}
          </Badge>
          <Badge colorScheme="blue" variant="subtle" px={2} py={1}>
            SD: {slice.sd}
          </Badge>
          <Badge colorScheme="green" variant="solid" px={2} py={1}>
            Group: {slice.duSliceGroupId}
          </Badge>
        </HStack>
      </Flex>
    </Box>
  );
};

const PLMNIdDisplay = ({ plmnId }: { plmnId: PLMNId }) => {
  return (
    <Box
      p={4}
      bg="white"
      borderRadius="lg"
      borderWidth="1px"
      borderColor="gray.200"
      shadow="sm"
      transition="all 0.2s ease-in-out"
      _hover={{
        shadow: 'md',
        borderColor: 'blue.300',
      }}
    >
      <VStack spacing={3} align="stretch">
        {/* PLMN ID Header */}
        <Flex justify="space-between" align="center">
          <HStack spacing={3}>
            <Icon as={FiGlobe} color="blue.500" boxSize={5} />
            <VStack align="start" spacing={0}>
              <Text fontSize="xs" color="gray.500" fontWeight="medium" textTransform="uppercase">
                PLMN
              </Text>
              <HStack spacing={2}>
                <Text fontSize="xl" fontWeight="bold" color="blue.700" fontFamily="mono">
                  {plmnId.MCC}-{plmnId.MNC}
                </Text>
                <CopyButton text={`${plmnId.MCC}-${plmnId.MNC}`} label="PLMN ID" />
              </HStack>
            </VStack>
          </HStack>

          <Badge
            colorScheme={plmnId.plmnActivationFlag ? 'green' : 'red'}
            variant="solid"
            fontSize="sm"
            px={3}
            py={1}
            borderRadius="full"
          >
            <HStack spacing={1}>
              <Icon as={plmnId.plmnActivationFlag ? FiCheckCircle : FiXCircle} mr={1} boxSize={4} />
              <Text fontSize="sm" fontWeight="semibold">
                {plmnId.plmnActivationFlag ? 'ACTIVE' : 'INACTIVE'}
              </Text>
            </HStack>
          </Badge>
        </Flex>

        {/* Slices Section */}
        {plmnId.sliceList && plmnId.sliceList.length > 0 && (
          <VStack align="stretch" spacing={2}>
            <HStack spacing={2}>
              <Icon as={FiLayers} color="purple.500" boxSize={4} />
              <Text fontSize="sm" fontWeight="semibold" color="gray.700">
                Network Slices ({plmnId.sliceList.length})
              </Text>
            </HStack>
            <VStack align="stretch" spacing={2}>
              {plmnId.sliceList.map((slice) => (
                <SliceDisplay key={slice.id} slice={slice} />
              ))}
            </VStack>
          </VStack>
        )}
      </VStack>
    </Box>
  );
};

const StatBox = ({
  icon,
  label,
  value,
  colorScheme,
}: {
  icon: As;
  label: string;
  value: string | number;
  colorScheme: string;
}) => (
  <Stat
    p={3}
    bg={`${colorScheme}.50`}
    borderRadius="md"
    textAlign="center"
    borderWidth="1px"
    borderColor={`${colorScheme}.100`}
  >
    <StatLabel fontSize="sm" color={`${colorScheme}.600`} fontWeight="medium">
      <HStack justify="center" spacing={1.5}>
        <Icon as={icon} />
        <Text>{label}</Text>
      </HStack>
    </StatLabel>
    <StatNumber fontSize="xl" color={`${colorScheme}.800`} fontFamily="mono" mt={1}>
      {value}
    </StatNumber>
  </Stat>
);

const PLMNDetails = ({ plmn }: { plmn: ServedPLMN }) => {
  return (
    <VStack spacing={4} align="stretch">
      <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
        <StatBox icon={FiHash} label="PLMN ID" value={plmn.id} colorScheme="blue" />
        <StatBox icon={FiTarget} label="RAN Area Code" value={plmn.ranac} colorScheme="green" />
        <StatBox icon={FiGrid} label="TAC Bitmap" value={plmn.tacBitMap} colorScheme="purple" />
        <StatBox
          icon={plmn.cellReservedOperatorUse === 'notReserved' ? FiCheckCircle : FiXCircle}
          label="Status"
          value={plmn.cellReservedOperatorUse}
          colorScheme={plmn.cellReservedOperatorUse === 'notReserved' ? 'green' : 'orange'}
        />
      </SimpleGrid>

      <VStack spacing={2} align="stretch" p={3} borderRadius="lg" borderWidth="1px">
        <HStack px={1}>
          <Icon as={FiLayers} color="blue.500" />
          <Text fontWeight="bold" fontSize="md" color="gray.600">
            PLMN ID Details
          </Text>
        </HStack>
        <VStack align="stretch" spacing={3}>
          {(plmn.pLMNIdList || []).map((plmnId) => (
            <PLMNIdDisplay key={plmnId.id} plmnId={plmnId} />
          ))}
        </VStack>
      </VStack>
    </VStack>
  );
};

export const ServedPLMNList: React.FC<ServedPLMNListProps> = ({ cell }) => {
  const servedPlmnList = cell.servedPlmnList || [];

  if (servedPlmnList.length === 0) {
    return (
      <Alert status="info" borderRadius="lg">
        <AlertIcon />
        <Text>No served PLMN data available.</Text>
      </Alert>
    );
  }

  return (
    <Card variant="outline" bg="white" border="none">
      <CardBody>
        <Tabs variant="soft-rounded" colorScheme="blue">
          <TabList display="flex" flexDirection="row" gap={2}>
            {servedPlmnList.map((plmn) => (
              <Tab
                key={plmn.id}
                shadow="lg"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'blue.200', transform: 'translateY(-5px)' }}
                _selected={{ bg: 'blue.200' }}
                transition="all 0.3s ease-in-out"
              >
                <VStack>
                  <HStack spacing={2}>
                    <Icon as={FiGlobe} boxSize={6} color="blue.500" />
                    <Text>PLMN {plmn.id}</Text>
                  </HStack>
                  {(plmn.pLMNIdList || []).map((plmnId) => (
                    <HStack key={plmnId.id}>
                      <Text>
                        {plmnId.MCC}-{plmnId.MNC}
                      </Text>
                    </HStack>
                  ))}
                </VStack>
              </Tab>
            ))}
          </TabList>
          <TabPanels mt={4}>
            {servedPlmnList.map((plmn) => (
              <TabPanel key={plmn.id} p={4} mx={4} mt={4}>
                <PLMNDetails plmn={plmn} />
              </TabPanel>
            ))}
          </TabPanels>
        </Tabs>
      </CardBody>
    </Card>
  );
};
