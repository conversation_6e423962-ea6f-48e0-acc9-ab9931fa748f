import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  H<PERSON>tack,
  Icon,
  Accordion,
  CardBody,
  useColorModeValue,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Box,
  Text,
  Badge,
  Button,
  Flex,
  VStack,
} from '@chakra-ui/react';
import {
  FiSettings,
  FiRadio,
  FiLayers,
  FiGlobe,
  FiInfo,
  FiWifi,
  FiTarget,
  FiCpu,
  FiRss,
  FiChevronUp,
  FiHash,
  FiCheckCircle,
  FiXCircle,
  FiDownload,
  FiUpload,
} from 'react-icons/fi';
import { DUSliceList } from './DUSliceList';
import { RACHConfiguration } from './RACHConfiguration';
import { ServedPLMNList } from './ServedPLMNList';
import { FrequencyConfiguration } from './FrequencyConfiguration';
import { L1Information } from './L1Information';
import { CSIRSConfiguration } from './CSIRSConfiguration';
import { useState } from 'react';

// Centralized accordion configuration
const accordionConfig = [
  {
    id: 'frequency',
    title: 'Frequency Configuration',
    icon: FiRadio,
    color: 'green',
    component: FrequencyConfiguration,
    crucialDetails: (cell: any) => {
      const dlFreq = cell.dlFreq || 'N/A';
      const ulFreq = cell.ulFreq || 'N/A';

      return (
        <HStack>
          <VStack align="start" borderWidth="1px" borderColor="purple.200" borderRadius="md" p={2}>
            <HStack>
              <Icon as={FiDownload} boxSize={5} color="purple.500" />
              <Text fontSize="xs" color="gray.600">
                DL:
              </Text>
              <Badge colorScheme="purple" variant="subtle">
                {dlFreq.arfcnDL}
              </Badge>
            </HStack>

            <HStack>
              <Text fontSize="xs" color="gray.600">
                BW:
              </Text>
              <Badge colorScheme="purple" variant="subtle">
                {dlFreq.bSChannelBwDL}
              </Badge>
            </HStack>

            <HStack>
              <Text fontSize="xs" color="gray.600">
                Band:
              </Text>
              <Badge colorScheme="purple" variant="subtle">
                {dlFreq.nrFreqBand}
              </Badge>
            </HStack>
          </VStack>

          <VStack align="start" borderWidth="1px" borderColor="purple.200" borderRadius="md" p={2}>
            <HStack>
              <Icon as={FiUpload} boxSize={5} color="purple.500" />
              <Text fontSize="xs" color="gray.600">
                UL:
              </Text>
              <Badge colorScheme="purple" variant="subtle">
                {ulFreq.arfcnUL}
              </Badge>
            </HStack>
            <HStack>
              <Text fontSize="xs" color="gray.600">
                BW:
              </Text>
              <Badge colorScheme="purple" variant="subtle">
                {ulFreq.bSChannelBwUl}
              </Badge>
            </HStack>
            <HStack>
              <Text fontSize="xs" color="gray.600">
                Band:
              </Text>
              <Badge colorScheme="purple" variant="subtle">
                {ulFreq.nrFreqBand}
              </Badge>
            </HStack>
          </VStack>
        </HStack>
      );
    },
  },
  {
    id: 'plmn',
    title: 'Served PLMN Configuration',
    icon: FiGlobe,
    color: 'blue',
    component: ServedPLMNList,
    crucialDetails: (cell: any) => {
      const servedPlmnList = cell.servedPlmnList || [];

      return servedPlmnList.map((plmn: any) => (
        <VStack key={plmn.id} align="start">
          <Text fontSize="sm" color="purple.500" fontWeight="medium" textTransform="uppercase">
            <Icon as={FiGlobe} boxSize={4} color="purple.500" mr="2" />
            PLMN {plmn.id} - {plmn.cellReservedOperatorUse}
          </Text>
          {(plmn.pLMNIdList || []).map((plmnId: any) => (
            <VStack key={plmnId.id} align="start">
              <Badge colorScheme="purple" variant="subtle" px={1} py={2} borderRadius="full">
                MCC: {plmnId.MCC} - MNC: {plmnId.MNC}
              </Badge>
            </VStack>
          ))}
        </VStack>
      ));
    },
  },
  {
    id: 'slices',
    title: 'DU Slice Configuration',
    icon: FiLayers,
    color: 'orange',
    component: DUSliceList,
    crucialDetails: (cell: any) => {
      const duSliceList = cell.duSliceList || [];
      return duSliceList.map((slice: any) => (
        <VStack key={slice.id} align="start">
          <Text fontSize="sm" color="purple.500" fontWeight="medium" textTransform="uppercase">
            Slice {slice.duSliceGroupId}
          </Text>

          <Badge colorScheme={slice.activeStatus ? 'green' : 'red'} variant="solid" px={1} py={1} borderRadius="full">
            <HStack spacing={1}>
              <Icon as={slice.activeStatus ? FiCheckCircle : FiXCircle} boxSize={3} />
              <Text fontSize="xs">{slice.activeStatus ? 'ACTIVE' : 'INACTIVE'}</Text>
            </HStack>
          </Badge>
        </VStack>
      ));
    },
  },
  {
    id: 'l1',
    title: 'L1 Information',
    icon: FiCpu,
    color: 'yellow',
    component: L1Information,
    crucialDetails: (cell: any) => {
      const l1Info = cell.l1Info || {};
      return (
        <VStack align="start">
          <HStack>
            <Text fontSize="sm" color="purple.500" fontWeight="medium" textTransform="uppercase">
              Q Qual Min:
            </Text>
            <Badge colorScheme="purple" variant="subtle" px={1} py={1} borderRadius="full">
              {l1Info.sib1Params?.cellSelectionInfo?.qQualMin?.rsrpLvl} dBm
            </Badge>
          </HStack>
          <HStack>
            <Text fontSize="sm" color="purple.500" fontWeight="medium" textTransform="uppercase">
              Q Rx Lev Min:
            </Text>
            <Badge colorScheme="purple" variant="subtle" px={1} py={1} borderRadius="full">
              {l1Info.sib1Params?.cellSelectionInfo?.qRxLevMin?.rsrpLvl} dBm
            </Badge>
          </HStack>
        </VStack>
      );
    },
  },
  {
    id: 'csirs',
    title: 'CSI-RS Configuration',
    icon: FiRss,
    color: 'purple',
    component: CSIRSConfiguration,
    badge: () => null,
  },
  {
    id: 'rach',
    title: 'RACH Configuration',
    icon: FiTarget,
    color: 'red',
    component: RACHConfiguration,
    badge: () => null,
  },
];

export const DetailedConfiguration = ({ cell, colorScheme }: { cell: any; colorScheme: string }) => {
  const cardBg = useColorModeValue('white', 'gray.700');
  const [expandedItems, setExpandedItems] = useState<number[]>([]);
  const hasExpandedItems = expandedItems.length > 0;
  const collapseAll = () => {
    setExpandedItems([]);
  };

  return (
    <Card variant="outline" shadow="lg" borderColor={`${colorScheme}.200`}>
      <CardHeader py={6}>
        <Flex justifyContent="space-between" alignItems="center">
          <HStack spacing={3}>
            <Box
              bg={`${colorScheme}.500`}
              bgGradient={`linear(135deg, ${colorScheme}.400, ${colorScheme}.600)`}
              borderRadius="full"
              p={2}
              shadow="md"
            >
              <Icon as={FiSettings} boxSize={6} color="white" />
            </Box>

            <Heading size="md" color={`${colorScheme}.700`}>
              Cell {cell.NRDUCellID} - Detailed Configuration
            </Heading>
          </HStack>
          <Button
            size="md"
            colorScheme={colorScheme}
            variant="outline"
            onClick={collapseAll}
            isDisabled={!hasExpandedItems}
            mr="4"
            shadow="lg"
            _hover={{
              shadow: 'md',
              transform: 'translateY(-5px)',
            }}
            _active={{
              shadow: 'md',
              transform: 'translateY(-5px)',
            }}
            _focus={{
              shadow: 'md',
              transform: 'translateY(-5px)',
            }}
            transition="all 0.3s ease-in-out"
          >
            <Icon as={FiChevronUp} boxSize={6} />
            Collapse All
          </Button>
        </Flex>
      </CardHeader>
      <CardBody pt="0">
        <Accordion
          allowMultiple
          mx="4"
          index={expandedItems}
          onChange={(expandedIndex) => setExpandedItems(expandedIndex as number[])}
        >
          {accordionConfig.map((config, index) => (
            <AccordionItem
              key={config.id}
              mb={3}
              shadow="md"
              _hover={{
                shadow: 'md',
                transform: 'translateY(-5px)',
              }}
              transition="all 0.3s ease-in-out"
              borderColor={`${colorScheme}.100`}
              borderWidth="1px"
              borderRadius="lg"
            >
              <h2>
                <AccordionButton
                  py={4}
                  px={5}
                  shadow="md"
                  _hover={{ bg: `${colorScheme}.100` }}
                  _expanded={{ bg: `${colorScheme}.100` }}
                >
                  <HStack as="span" flex="1" textAlign="left" spacing={2}>
                    <Box
                      bg={`${colorScheme}.500`}
                      bgGradient={`linear(135deg, ${colorScheme}.400, ${colorScheme}.600)`}
                      borderRadius="full"
                      p={2}
                      shadow="md"
                    >
                      <Icon as={config.icon} boxSize={5} color="white" />
                    </Box>

                    <Text fontWeight="bold" fontSize="md">
                      {config.title}
                    </Text>
                    {config.crucialDetails && config.crucialDetails(cell)}
                  </HStack>
                  <AccordionIcon />
                </AccordionButton>
              </h2>
              <AccordionPanel>
                <config.component cell={cell} data={cell.csiRs} />
              </AccordionPanel>
            </AccordionItem>
          ))}
        </Accordion>
      </CardBody>
    </Card>
  );
};
