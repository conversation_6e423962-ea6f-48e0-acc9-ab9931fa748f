import React from 'react';
import {
  <PERSON>,
  Badge,
  TableContainer,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Icon,
  HStack,
  Text,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import { FiHash, FiSettings, FiTarget, FiRadio } from 'react-icons/fi';

interface RACHConfigurationProps {
  cell: any;
}

export const RACHConfiguration: React.FC<RACHConfigurationProps> = ({ cell }) => {
  const rachData = cell.rach || [];

  if (rachData.length === 0) {
    return (
      <Alert status="info" borderRadius="lg">
        <AlertIcon />
        <Text>No RACH configuration data available.</Text>
      </Alert>
    );
  }

  return (
    <TableContainer bg="white" borderRadius="lg" shadow="sm">
      <Table size="md">
        <Thead bg="purple.50">
          <Tr>
            <Th color="purple.700">
              <HStack>
                <Icon as={FiHash} color="purple.500" boxSize={4} />
                <Text>ID</Text>
              </HStack>
            </Th>
            <Th color="purple.700">
              <HStack>
                <Icon as={FiSettings} color="purple.500" boxSize={4} />
                <Text>Root Sequence Type</Text>
              </HStack>
            </Th>
            <Th color="purple.700">
              <HStack>
                <Icon as={FiTarget} color="purple.500" boxSize={4} />
                <Text>Root Sequence Value</Text>
              </HStack>
            </Th>
            <Th color="purple.700">
              <HStack>
                <Icon as={FiRadio} color="purple.500" boxSize={4} />
                <Text>UL BWP ID</Text>
              </HStack>
            </Th>
          </Tr>
        </Thead>
        <Tbody>
          {rachData.map((rachConfig: any) => (
            <Tr
              key={rachConfig.id}
              _hover={{
                bg: 'purple.100',
                transform: 'translateY(-5px)',
              }}
              transition="all 0.3s ease"
            >
              <Td fontWeight="bold" color="purple.700" fontFamily="mono">
                {rachConfig.id}
              </Td>
              <Td>
                <Badge colorScheme="purple" variant="solid" px={3} py={1} borderRadius="full">
                  {rachConfig.rootSeqType}
                </Badge>
              </Td>
              <Td fontWeight="medium" fontFamily="mono">
                {rachConfig.rootSeqVal}
              </Td>
              <Td fontWeight="medium" fontFamily="mono">
                {rachConfig.ulbwpId}
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
};
