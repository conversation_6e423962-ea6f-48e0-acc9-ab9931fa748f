import {
  <PERSON>ert,
  AlertIcon,
  TableContainer,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  HStack,
  Icon,
  Text,
} from '@chakra-ui/react';
import { FiHash, FiTarget } from 'react-icons/fi';

interface CsiRs {
  id: number;
  csiRsId: number;
  csiRsOffset: number;
}

export const CSIRSConfiguration = ({ data }: { data: CsiRs[] }) => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <Alert status="info" borderRadius="lg">
        <AlertIcon />
        <Text>No CSI-RS configuration data available.</Text>
      </Alert>
    );
  }

  return (
    <TableContainer bg="white" borderRadius="lg" shadow="sm">
      <Table size="md">
        <Thead bg="purple.50">
          <Tr>
            <Th color="purple.700">
              <HStack>
                <Icon as={FiHash} color="purple.500" boxSize={4} />
                <Text>CSI-RS ID</Text>
              </HStack>
            </Th>
            <Th color="purple.700">
              <HStack>
                <Icon as={FiTarget} color="purple.500" boxSize={4} />
                <Text>Offset</Text>
              </HStack>
            </Th>
          </Tr>
        </Thead>
        <Tbody>
          {data.map((item) => (
            <Tr
              key={item.id}
              _hover={{
                bg: 'purple.100',
                transform: 'translateY(-5px)',
              }}
              transition="all 0.3s ease"
            >
              <Td fontWeight="bold" color="purple.700" fontFamily="mono">
                {item.csiRsId}
              </Td>
              <Td fontWeight="bold" fontFamily="mono">
                {item.csiRsOffset}
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
};
