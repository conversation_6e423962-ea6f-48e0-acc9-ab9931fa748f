import React from 'react';
import {
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Badge,
  Box,
  Card,
  CardBody,
  CardHeader,
  Grid,
  Heading,
  HStack,
  Icon,
  SimpleGrid,
  Text,
  VStack,
} from '@chakra-ui/react';
import { FiArrowDown, FiArrowUp, FiBarChart, FiDownload, FiRadio, FiTool, FiUpload, FiZap } from 'react-icons/fi';

interface DownlinkFrequency {
  arfcnDL?: number;
  absArfcnSsb?: number;
  bSChannelBwDL?: string;
  nrFreqBand?: number;
}

interface UplinkFrequency {
  arfcnUL?: number;
  pMax?: number;
  bSChannelBwUl?: string;
  nrFreqBand?: number;
}

const InfoRow = ({
  icon,
  label,
  value,
  isBadge,
  colorScheme,
}: {
  icon: React.ElementType;
  label: string;
  value: string | number;
  isBadge?: boolean;
  colorScheme?: 'green' | 'purple';
}) => (
  <Grid templateColumns="140px 1fr" alignItems="center" gap={4}>
    <HStack spacing={3}>
      <Icon as={icon} color={`${colorScheme}.500`} boxSize={4} />
      <Text fontSize="sm" fontWeight="600" color="gray.600">
        {label}:
      </Text>
    </HStack>
    {isBadge ? (
      <Badge colorScheme={colorScheme} py={0.5} px={2} borderRadius="md" fontSize="sm" width="100px">
        {value}
      </Badge>
    ) : (
      <Text fontSize="sm" fontWeight="bold" color="gray.800" fontFamily="mono">
        {value}
      </Text>
    )}
  </Grid>
);

const FrequencyCard = ({
  title,
  icon,
  colorScheme,
  data,
}: {
  title: string;
  icon: React.ElementType;
  colorScheme: 'green' | 'purple';
  data: (DownlinkFrequency & UplinkFrequency) | undefined;
}) => {
  const isDownlink = title.includes('Downlink');

  return (
    <Card
      variant="outline"
      bg="white"
      shadow="sm"
      borderColor={`${colorScheme}.200`}
      _hover={{
        borderColor: `${colorScheme}.300`,
        shadow: 'md',
        transform: 'translateY(-5px)',
      }}
      transition="all 0.2s ease-in-out"
    >
      <CardHeader pb={2} bg={`${colorScheme}.50`}>
        <Heading size="md" color={`${colorScheme}.700`}>
          <Icon as={icon} mr={3} boxSize={6} />
          {title}
        </Heading>
      </CardHeader>
      <CardBody>
        <VStack align="stretch" spacing={3.5}>
          <InfoRow
            icon={FiRadio}
            label={isDownlink ? 'ARFCN DL' : 'ARFCN UL'}
            value={data?.arfcnDL ?? data?.arfcnUL ?? 'N/A'}
            colorScheme={colorScheme}
          />
          {isDownlink ? (
            <InfoRow icon={FiRadio} label="SSB ARFCN" value={data?.absArfcnSsb ?? 'N/A'} colorScheme={colorScheme} />
          ) : (
            <InfoRow
              icon={FiZap}
              label="P Max"
              value={data?.pMax ? `${data.pMax}dBm` : 'N/A'}
              colorScheme={colorScheme}
            />
          )}
          <InfoRow
            icon={FiBarChart}
            label="Bandwidth"
            value={data?.bSChannelBwDL ?? data?.bSChannelBwUl ?? 'N/A'}
            isBadge
            colorScheme={colorScheme}
          />
          <InfoRow icon={FiTool} label="NR Band" value={data?.nrFreqBand ?? 'N/A'} isBadge colorScheme={colorScheme} />
        </VStack>
      </CardBody>
    </Card>
  );
};

export const FrequencyConfiguration = ({
  cell,
}: {
  cell: { dlFreq?: DownlinkFrequency; ulFreq?: UplinkFrequency };
}) => {
  return (
    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6} p="4">
      <FrequencyCard title="Downlink Frequency" icon={FiDownload} colorScheme="green" data={cell.dlFreq} />
      <FrequencyCard title="Uplink Frequency" icon={FiUpload} colorScheme="purple" data={cell.ulFreq} />
    </SimpleGrid>
  );
};
