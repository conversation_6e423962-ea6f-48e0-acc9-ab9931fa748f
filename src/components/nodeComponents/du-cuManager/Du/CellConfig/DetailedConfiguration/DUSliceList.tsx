import React from 'react';
import {
  <PERSON>,
  HStack,
  VStack,
  Icon,
  Text,
  Badge,
  Card,
  CardHeader,
  CardBody,
  Heading,
  SimpleGrid,
  Wrap,
  WrapItem,
  Tag,
} from '@chakra-ui/react';
import { FiLayers, FiCheckCircle, FiXCircle } from 'react-icons/fi';

interface DUSliceListProps {
  cell: any;
}

export const DUSliceList: React.FC<DUSliceListProps> = ({ cell }) => {
  return (
    <VStack spacing={4} align="stretch" p="4">
      {(cell.duSliceList || []).map((slice: any) => (
        <Card
          key={slice.duSliceGroupId}
          variant="outline"
          bg="white"
          shadow="sm"
          _hover={{
            transform: 'translateY(-5px)',
          }}
          borderColor={slice.activeStatus ? 'green.200' : 'red.200'}
          transition="all 0.3s ease"
        >
          <CardHeader bg={slice.activeStatus ? 'green.50' : 'red.50'}>
            <HStack justify="space-between">
              <Heading size="md" color={slice.activeStatus ? 'green.700' : 'red.700'}>
                <Icon as={FiLayers} mr={3} />
                Slice Group {slice.duSliceGroupId}
              </Heading>
              <HStack spacing={2}>
                <Badge
                  colorScheme={slice.activeStatus ? 'green' : 'red'}
                  variant="solid"
                  px={3}
                  py={1}
                  borderRadius="full"
                >
                  <HStack spacing={1}>
                    <Icon as={slice.activeStatus ? FiCheckCircle : FiXCircle} boxSize={4} />
                    <Text>{slice.activeStatus ? 'ACTIVE' : 'INACTIVE'}</Text>
                  </HStack>
                </Badge>
                <Badge
                  colorScheme={slice.isDefaultSlice ? 'blue' : 'gray'}
                  variant="solid"
                  px={3}
                  py={1}
                  borderRadius="full"
                >
                  <HStack spacing={1}>
                    <Icon as={slice.isDefaultSlice ? FiCheckCircle : FiXCircle} boxSize={4} />
                    <Text>{slice.isDefaultSlice ? 'DEFAULT' : 'CUSTOM'}</Text>
                  </HStack>
                </Badge>
              </HStack>
            </HStack>
          </CardHeader>
          <CardBody>
            <SimpleGrid columns={{ base: 2, md: 4 }} spacing={3} mb={4}>
              <Box textAlign="center" p={2} bg="orange.50" borderRadius="md">
                <Text fontSize="xs" fontWeight="bold" color="orange.700">
                  Priority
                </Text>
                <Text fontSize="lg" fontWeight="bold">
                  {slice.priority}
                </Text>
              </Box>
              <Box textAlign="center" p={2} bg="blue.50" borderRadius="md">
                <Text fontSize="xs" fontWeight="bold" color="blue.700">
                  DL PRB Share
                </Text>
                <Text fontSize="lg" fontWeight="bold">
                  {slice.dlPrbShare}%
                </Text>
              </Box>
              <Box textAlign="center" p={2} bg="purple.50" borderRadius="md">
                <Text fontSize="xs" fontWeight="bold" color="purple.700">
                  UL PRB Share
                </Text>
                <Text fontSize="lg" fontWeight="bold">
                  {slice.ulPrbShare}%
                </Text>
              </Box>
              <Box textAlign="center" p={2} bg="green.50" borderRadius="md">
                <Text fontSize="xs" fontWeight="bold" color="green.700">
                  5QI Items
                </Text>
                <Text fontSize="lg" fontWeight="bold">
                  {slice.slice5QIList?.length || 0}
                </Text>
              </Box>
            </SimpleGrid>

            <Text fontSize="sm" fontWeight="bold" color="gray.700" mb={2}>
              5QI Configuration:
            </Text>
            <Wrap spacing={1}>
              {(slice.slice5QIList || []).map((qiItem: any, idx: number) => (
                <WrapItem key={idx}>
                  <Tag colorScheme="green" size="sm">
                    QI{qiItem.slice5QIKey}: {qiItem.slice5QIVal}
                  </Tag>
                </WrapItem>
              ))}
            </Wrap>
          </CardBody>
        </Card>
      ))}
    </VStack>
  );
};
