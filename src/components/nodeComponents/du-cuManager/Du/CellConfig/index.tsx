import {
  Box,
  Button,
  Card,
  CardBody,
  Text,
  VStack,
  HStack,
  Badge,
  Icon,
  useColorModeValue,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  Alert,
  AlertIcon,
  Flex,
  Heading,
  useDisclosure,
} from '@chakra-ui/react';
import { <PERSON>Radio, <PERSON>L<PERSON>, <PERSON>Un<PERSON>, <PERSON>Eye } from 'react-icons/fi';

import MetadataComponent from '../common';
import { DetailedConfiguration } from './DetailedConfiguration';
import CellVitals from './Vitals';
import JsonViewerDialog from '../JsonViewerDialog';

const DuCellConfig = ({ data, colorScheme }: { data: any; colorScheme: string }) => {
  const cardBg = useColorModeValue('white', 'gray.700');
  const { isOpen: isJsonViewerOpen, onOpen: onJsonViewerOpen, onClose: onJsonViewerClose } = useDisclosure();

  if (!data || !data.content || !data.status) {
    return (
      <Alert status="error" borderRadius="lg">
        <AlertIcon />
        <Box>
          <Text fontWeight="bold">No DU Cell Configuration Data</Text>
          <Text>The DU Cell configuration information could not be loaded.</Text>
        </Box>
      </Alert>
    );
  }

  const { status, content } = data;
  const { spec, metadata } = content;
  const cells = spec.NRDUCell || [];

  return (
    <VStack spacing={6} align="stretch">
      {/* HEADER WITH JSON VIEWER BUTTON */}
      <Flex justify="space-between" align="center" mb={1}>
        <Heading size="xl" color={`${colorScheme}.700`}>
          DU Cell Configuration
        </Heading>
        <Button
          leftIcon={<Icon as={FiEye} />}
          colorScheme={colorScheme}
          variant="outline"
          size="md"
          onClick={onJsonViewerOpen}
        >
          View Raw JSON
        </Button>
      </Flex>

      {/* METADATA SECTION */}
      <MetadataComponent metadata={metadata} colorScheme={colorScheme} />

      {/* CELL TABS */}
      <Card bg={cardBg} variant="outline" shadow="lg" borderColor={`${colorScheme}.200`}>
        <CardBody pt={0}>
          <Tabs variant="soft-rounded" colorScheme={colorScheme} size="lg">
            <TabList mt={3} bg="white" p={2} gap={4}>
              {cells.map((cell: any, index: number) => (
                <Tab
                  key={cell.NRDUCellID}
                  fontWeight="bold"
                  fontSize="lg"
                  shadow="lg"
                  _hover={{
                    bg: `${colorScheme}.100`,
                    transform: 'translateY(-10px)',
                    shadow: 'lg',
                  }}
                  _selected={{
                    borderColor: `${colorScheme}.600`,
                    shadow: 'lg',
                    bg: `${colorScheme}.100`,
                  }}
                  transition="all 0.3s ease"
                >
                  <HStack spacing={2} py="2">
                    <Box
                      bg={`${colorScheme}.500`}
                      bgGradient={`linear(135deg, ${colorScheme}.300, ${colorScheme}.700)`}
                      borderRadius="full"
                      p={2}
                      shadow="md"
                    >
                      <Icon as={FiRadio} boxSize={8} color="white" />
                    </Box>

                    <Heading size="xl" color={`${colorScheme}.700`}>
                      Cell {cell.NRDUCellID}
                    </Heading>

                    <Badge
                      colorScheme={cell.administrativeState === 'Unlocked' ? 'green' : 'red'}
                      variant="solid"
                      px={3}
                      py={1}
                      borderRadius="full"
                    >
                      <HStack spacing={1}>
                        <Icon as={cell.administrativeState === 'Unlocked' ? FiUnlock : FiLock} boxSize={6} />
                        <Text>{cell.administrativeState === 'Unlocked' ? 'Unlocked' : 'Locked'}</Text>
                      </HStack>
                    </Badge>
                  </HStack>
                </Tab>
              ))}
            </TabList>
            <TabPanels>
              {cells.map((cell: any, index: number) => (
                <TabPanel key={cell.NRDUCellID} mx={5}>
                  <Flex direction="column" gap={5}>
                    <CellVitals cell={cell} cellIndex={index} colorScheme={colorScheme} />
                    <DetailedConfiguration cell={cell} colorScheme={colorScheme} />
                  </Flex>
                </TabPanel>
              ))}
            </TabPanels>
          </Tabs>
        </CardBody>
      </Card>

      {/* JSON VIEWER DIALOG */}
      <JsonViewerDialog
        isOpen={isJsonViewerOpen}
        onClose={onJsonViewerClose}
        title="DU Cell Configuration"
        data={data}
        colorScheme={colorScheme}
      />
    </VStack>
  );
};

export default DuCellConfig;
