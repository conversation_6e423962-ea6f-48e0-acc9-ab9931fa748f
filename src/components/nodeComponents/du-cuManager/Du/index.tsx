import { AlertIcon, Alert, Box, Text } from '@chakra-ui/react';
import DuComponentsMenu from './DuComponentsMenu';
import InstanceDetails from './InstanceDetails';

const DuComponentCard = ({ data, node_serial_no }: { data: any; node_serial_no: string }) => {
  return (
    <>
      {data?.pod?.detail ? (
        <>
          <InstanceDetails data={data?.pod?.detail} pod_status={data?.pod?.status} node_serial_no={node_serial_no} />

          {data?.pod?.crs && Object.keys(data?.pod?.crs).length > 0 ? (
            <DuComponentsMenu payload={data?.pod?.crs} node_serial_no={node_serial_no} nodeType="DU" />
          ) : (
            <Box display="flex" justifyContent="center">
              <Alert
                status="info"
                borderRadius="xl"
                bg="blue.50"
                borderColor="blue.200"
                mt="4"
                justifyContent="center"
                alignItems="center"
                textAlign="center"
              >
                <AlertIcon color="blue.500" />
                <Text color="blue.700" fontSize="xl">
                  No CRs are currently available.
                </Text>
              </Alert>
            </Box>
          )}
        </>
      ) : (
        <Box display="flex" justifyContent="center">
          <Alert status="info" borderRadius="xl" bg="blue.50" borderColor="blue.200">
            <AlertIcon color="blue.500" />
            <Box>
              <Text fontWeight="bold" color="blue.800">
                No Instance Data Available
              </Text>
              <Text color="blue.700">Instance runtime information is not currently available.</Text>
            </Box>
          </Alert>
        </Box>
      )}
    </>
  );
};

export default DuComponentCard;
