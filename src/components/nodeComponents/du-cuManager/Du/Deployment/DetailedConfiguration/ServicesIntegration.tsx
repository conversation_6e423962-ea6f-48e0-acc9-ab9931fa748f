import React from 'react';
import {
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Box,
  HStack,
  VStack,
  Icon,
  Text,
  Badge,
  SimpleGrid,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Flex,
} from '@chakra-ui/react';
import {
  FiGlobe,
  FiTerminal,
  FiLink,
  FiLock,
  FiUnlock,
  FiActivity,
  FiServer,
  FiMonitor,
  FiGitBranch,
  FiType,
  FiBarChart2,
  FiRefreshCw,
  FiCheckCircle,
  FiXCircle,
  FiKey,
} from 'react-icons/fi';
import { CopyButton } from '../../common';

interface SslAuth {
  enabled: boolean;
  secretName: string;
}

interface F1APService {
  enabled: boolean;
  id: number;
  localExternalCidr: string;
  localPort: number;
  resourceName: string;
  resourcePrefix: string;
  sslAuth: SslAuth;
  type: string;
}

interface MetricsService {
  enabled: boolean;
  localExternalIP: string;
  localPort: number;
  sslAuth: SslAuth;
  type: string;
}

interface VesCollector {
  FQDN: string;
  path: string;
  port: number;
  protocol: string;
  sslAuth: SslAuth;
}

interface ServicesSpec {
  service?: {
    f1ap: F1APService[];
    metrics: MetricsService;
  };
  syncmon?: string;
  vesCollector?: VesCollector;
}

interface ServicesIntegrationProps {
  spec: ServicesSpec;
}

const DetailItem = ({
  icon,
  label,
  colorScheme,
  children,
}: {
  icon: React.ElementType;
  label: string;
  colorScheme: string;
  children: React.ReactNode;
}) => (
  <HStack align="start" spacing={3}>
    <Icon as={icon} color={`${colorScheme}.500`} mt={1} />
    <Text fontWeight="600" color={'gray.600'} w="110px" flexShrink={0}>
      {label}:
    </Text>
    <Box>{children}</Box>
  </HStack>
);

const ServiceCard = ({
  title,
  icon,
  colorScheme,
  isEnabled,
  children,
}: {
  title: string;
  icon: React.ElementType;
  colorScheme: string;
  isEnabled: boolean;
  children: React.ReactNode;
}) => (
  <Card variant="outline" bg="white" shadow="sm" _hover={{ transform: 'translateY(-5px)' }} transition="all 0.3s ease">
    <CardHeader pb={2} bg={`${colorScheme}.50`}>
      <Flex justify="space-between" align="center">
        <Heading size="md" color={`${colorScheme}.700`}>
          <Icon as={icon} mr={3} />
          {title}
        </Heading>
        <Badge colorScheme={isEnabled ? 'green' : 'red'} variant="solid" px={3} py={1} borderRadius="full">
          <HStack spacing={1.5}>
            <Icon as={isEnabled ? FiCheckCircle : FiXCircle} boxSize={6} />
            <Text fontSize="xs">{isEnabled ? 'Enabled' : 'Disabled'}</Text>
          </HStack>
        </Badge>
      </Flex>
    </CardHeader>
    <CardBody>
      <VStack align="stretch" spacing={4}>
        {children}
      </VStack>
    </CardBody>
  </Card>
);

const F1APServiceCard = ({ f1ap }: { f1ap: F1APService }) => {
  return (
    <ServiceCard title="F1AP Service" icon={FiTerminal} colorScheme="blue" isEnabled={f1ap.enabled}>
      <DetailItem icon={FiType} label="Type" colorScheme="blue">
        <Badge colorScheme="blue">{f1ap.type}</Badge>
      </DetailItem>
      <DetailItem icon={FiLink} label="Local CIDR" colorScheme="blue">
        <HStack spacing={1}>
          <Text fontFamily="mono" fontSize="sm">
            {f1ap.localExternalCidr}
          </Text>
          <CopyButton text={f1ap.localExternalCidr} label="Local CIDR" />
        </HStack>
      </DetailItem>
      <DetailItem icon={FiGitBranch} label="Resource Name" colorScheme="blue">
        <HStack spacing={1}>
          <Text fontFamily="mono" fontSize="sm">
            {f1ap.resourceName}
          </Text>
          <CopyButton text={f1ap.resourceName} label="Resource Name" />
        </HStack>
      </DetailItem>
      <DetailItem icon={f1ap.sslAuth.enabled ? FiLock : FiUnlock} label="SSL Auth" colorScheme="blue">
        <Badge colorScheme={f1ap.sslAuth.enabled ? 'green' : 'red'} variant="solid" px={3} py={1} borderRadius="full">
          <HStack spacing={1.5}>
            <Icon as={f1ap.sslAuth.enabled ? FiCheckCircle : FiXCircle} boxSize={4} />
            <Text fontSize="xs">{f1ap.sslAuth.enabled ? 'Enabled' : 'Disabled'}</Text>
          </HStack>
        </Badge>
      </DetailItem>
      <DetailItem icon={FiKey} label="Secret Name" colorScheme="blue">
        <HStack spacing={1}>
          <Text fontFamily="mono" fontSize="sm">
            {f1ap.sslAuth.secretName}
          </Text>
          {f1ap.sslAuth.secretName && <CopyButton text={f1ap.sslAuth.secretName} label="Secret Name" />}
        </HStack>
      </DetailItem>
    </ServiceCard>
  );
};

const MetricsServiceCard = ({ metrics }: { metrics: MetricsService }) => {
  return (
    <ServiceCard title="Metrics Service" icon={FiBarChart2} colorScheme="teal" isEnabled={metrics.enabled}>
      <DetailItem icon={FiType} label="Type" colorScheme="teal">
        <Badge colorScheme="teal">{metrics.type}</Badge>
      </DetailItem>
      <DetailItem icon={FiServer} label="Local IP" colorScheme="teal">
        <HStack spacing={1}>
          <Text fontFamily="mono" fontSize="sm">
            {metrics.localExternalIP}
          </Text>
          <CopyButton text={metrics.localExternalIP} label="Local IP" />
        </HStack>
      </DetailItem>
      <DetailItem icon={FiMonitor} label="Port" colorScheme="teal">
        <Text fontWeight="bold">{metrics.localPort}</Text>
      </DetailItem>
      <DetailItem icon={metrics.sslAuth.enabled ? FiLock : FiUnlock} label="SSL Auth" colorScheme="teal">
        <Badge
          colorScheme={metrics.sslAuth.enabled ? 'green' : 'red'}
          variant="solid"
          px={3}
          py={1}
          borderRadius="full"
        >
          <HStack spacing={1.5}>
            <Icon as={metrics.sslAuth.enabled ? FiCheckCircle : FiXCircle} boxSize={4} />
            <Text fontSize="xs">{metrics.sslAuth.enabled ? 'Enabled' : 'Disabled'}</Text>
          </HStack>
        </Badge>
      </DetailItem>
      <DetailItem icon={FiKey} label="Secret Name" colorScheme="teal">
        <HStack spacing={1}>
          <Text fontFamily="mono" fontSize="sm">
            {metrics.sslAuth.secretName}
          </Text>
          {metrics.sslAuth.secretName && <CopyButton text={metrics.sslAuth.secretName} label="Secret Name" />}
        </HStack>
      </DetailItem>
    </ServiceCard>
  );
};

const VESCollectorCard = ({ ves }: { ves: VesCollector }) => {
  return (
    <ServiceCard title="VES Collector" icon={FiActivity} colorScheme="purple" isEnabled={!ves.sslAuth.enabled}>
      <DetailItem icon={FiServer} label="FQDN" colorScheme="purple">
        <HStack spacing={1}>
          <Text fontFamily="mono" fontSize="xs" wordBreak="break-all">
            {ves.FQDN}
          </Text>
          <CopyButton text={ves.FQDN} label="FQDN" />
        </HStack>
      </DetailItem>
      <DetailItem icon={FiLink} label="Protocol" colorScheme="purple">
        <Badge colorScheme="purple">{ves.protocol.toUpperCase()}</Badge>
      </DetailItem>
      <DetailItem icon={FiMonitor} label="Port" colorScheme="purple">
        <Text fontWeight="bold">{ves.port}</Text>
      </DetailItem>
      <DetailItem icon={ves.sslAuth.enabled ? FiLock : FiUnlock} label="SSL Auth" colorScheme="purple">
        <Badge colorScheme={ves.sslAuth.enabled ? 'green' : 'red'} variant="solid" px={3} py={1} borderRadius="full">
          <HStack spacing={1.5}>
            <Icon as={ves.sslAuth.enabled ? FiCheckCircle : FiXCircle} boxSize={4} />
            <Text fontSize="xs">{ves.sslAuth.enabled ? 'Enabled' : 'Disabled'}</Text>
          </HStack>
        </Badge>
      </DetailItem>
      <DetailItem icon={FiKey} label="Secret Name" colorScheme="purple">
        <HStack spacing={1}>
          <Text fontFamily="mono" fontSize="sm">
            {ves.sslAuth.secretName}
          </Text>
          {ves.sslAuth.secretName && <CopyButton text={ves.sslAuth.secretName} label="Secret Name" />}
        </HStack>
      </DetailItem>
    </ServiceCard>
  );
};

export const ServicesIntegration: React.FC<ServicesIntegrationProps> = ({ spec }) => {
  const f1ap = spec.service?.f1ap?.[0];
  const metrics = spec.service?.metrics;
  const ves = spec.vesCollector;

  return (
    <Card
      variant="outline"
      bg="white"
      shadow="sm"
      _hover={{ transform: 'translateY(-5px)' }}
      transition="all 0.3s ease"
      border="none"
    >
      <CardBody>
        <SimpleGrid columns={{ base: 1, lg: 3 }} spacing={6} mx="4">
          {/* F1AP Service Card */}
          {f1ap && <F1APServiceCard f1ap={f1ap} />}

          {/* Metrics Service Card */}
          {metrics && <MetricsServiceCard metrics={metrics} />}

          {/* VES Collector Card */}
          {ves && <VESCollectorCard ves={ves} />}
        </SimpleGrid>
      </CardBody>
    </Card>
  );
};
