import React from 'react';
import {
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Box,
  HStack,
  Icon,
  Text,
  Badge,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  TableContainer,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Card,
  CardBody,
} from '@chakra-ui/react';
import {
  FiWifi,
  FiGlobe,
  FiServer,
  FiMonitor,
  FiEye,
  FiSlash,
  FiZap,
  FiCheckCircle,
  FiXCircle,
  FiHash,
  FiShare2,
} from 'react-icons/fi';
import { BsShieldCheck, BsShieldExclamation } from 'react-icons/bs';
import { TbFunction } from 'react-icons/tb';
import { CopyButton } from '../../common';

interface DeploymentNetworkConfigurationProps {
  spec: any;
}

const FHInterfaceTable = ({ spec }: { spec: any }) => {
  return (
    <TableContainer bg="white" borderRadius="lg" shadow="sm">
      <Table size="md">
        <Thead bg="green.50">
          <Tr>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiHash} color="green.500" boxSize={4} />
                <Text>ID</Text>
              </HStack>
            </Th>
            <Th px={1}>
              <HStack spacing={1}>
                <Icon as={FiHash} color="green.500" boxSize={4} />
                <Text>Ep Fhu Id</Text>
              </HStack>
            </Th>
            <Th px={1}>
              <HStack spacing={1}>
                <Icon as={FiHash} color="green.500" boxSize={4} />
                <Text>Rx Group Id</Text>
              </HStack>
            </Th>
            <Th px={2}>
              <HStack spacing={1}>
                <Icon as={TbFunction} color="green.500" boxSize={4} />
                <Text>Function</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiServer} color="green.500" boxSize={4} />
                <Text>Resource Name</Text>
              </HStack>
            </Th>
            <Th p={1}>
              <HStack spacing={1}>
                <Icon as={FiGlobe} color="green.500" boxSize={4} />
                <Text>Resource Prefix</Text>
              </HStack>
            </Th>
            <Th p={1}>
              <HStack spacing={1}>
                <Icon as={FiMonitor} color="green.500" boxSize={4} />
                <Text>MTU Size</Text>
              </HStack>
            </Th>
            <Th p={1}>
              <HStack spacing={1}>
                <Icon as={FiShare2} color="green.500" boxSize={4} />
                <Text>V Lan</Text>
              </HStack>
            </Th>
            <Th p={1}>
              <HStack spacing={1}>
                <Icon as={FiZap} color="green.500" boxSize={4} />
                <Text>V Host En</Text>
              </HStack>
            </Th>

            <Th p={1}>
              <HStack spacing={1}>
                <Icon as={BsShieldCheck} color="green.500" boxSize={4} />
                <Text>Trust</Text>
              </HStack>
            </Th>

            <Th p={1}>
              <HStack spacing={1}>
                <Icon as={FiEye} color="green.500" boxSize={4} />
                <Text>Spoof Check</Text>
              </HStack>
            </Th>
          </Tr>
        </Thead>
        <Tbody>
          {spec.network.FHinterfaces.map((iface: any) => (
            <Tr
              key={iface.id}
              _hover={{
                bg: 'green.100',
                transform: 'translateY(-5px)',
              }}
              transition="all 0.3s ease"
            >
              <Td fontWeight="bold" color="green.700">
                {iface.id}
              </Td>
              <Td fontWeight="bold" color="green.700">
                {iface.epFhuID}
              </Td>
              <Td fontFamily="mono" fontSize="sm">
                {iface.rxGroupId}
              </Td>
              <Td fontFamily="mono" fontSize="sm">
                {iface.function}
              </Td>
              <Td>
                <HStack spacing={2}>
                  <Text fontFamily="mono" fontSize="sm">
                    {iface.resourceName}
                  </Text>
                  <CopyButton text={iface.resourceName} label="Resource Name" />
                </HStack>
              </Td>
              <Td fontFamily="mono" fontSize="sm">
                {iface.resourcePrefix}
              </Td>
              <Td>{iface.mtuSize}</Td>

              <Td fontFamily="mono" fontSize="sm">
                {iface.vlan}
              </Td>

              <Td p={2}>
                <Badge
                  colorScheme={iface.vhostEn === '1' ? 'green' : 'red'}
                  variant="solid"
                  px={3}
                  py={1}
                  borderRadius="full"
                >
                  <HStack spacing={1}>
                    <Icon as={iface.vhostEn === '1' ? FiCheckCircle : FiXCircle} boxSize={4} />
                    <Text>{iface.vhostEn === '1' ? 'Enabled' : 'Disabled'}</Text>
                  </HStack>
                </Badge>
              </Td>

              <Td p={2}>
                <Badge colorScheme={iface.trust ? 'green' : 'red'} variant="solid" px={3} py={1} borderRadius="full">
                  <HStack spacing={1}>
                    <Icon as={iface.trust ? BsShieldCheck : BsShieldExclamation} boxSize={4} />
                    <Text>{iface.trust ? 'Trusted' : 'Untrusted'}</Text>
                  </HStack>
                </Badge>
              </Td>

              <Td p={2}>
                <Badge
                  colorScheme={iface.spoofCheck ? 'green' : 'red'}
                  variant="solid"
                  px={3}
                  py={1}
                  borderRadius="full"
                >
                  <HStack spacing={1}>
                    <Icon as={iface.spoofCheck ? FiEye : FiSlash} boxSize={4} />
                    <Text>{iface.spoofCheck ? 'Enabled' : 'Disabled'}</Text>
                  </HStack>
                </Badge>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
};

const F1UInterfaceTable = ({ spec }: { spec: any }) => {
  return (
    <TableContainer bg="white" borderRadius="lg" shadow="sm">
      <Table size="md">
        <Thead bg="green.50">
          <Tr>
            <Th>
              <HStack>
                <Icon as={FiHash} color="green.500" boxSize={4} />
                <Text>ID</Text>
              </HStack>
            </Th>
            <Th px={1}>
              <HStack>
                <Icon as={FiHash} color="green.500" boxSize={4} />
                <Text>Rx Group Id</Text>
              </HStack>
            </Th>
            <Th>
              <HStack>
                <Icon as={TbFunction} color="green.500" boxSize={4} />
                <Text>Function</Text>
              </HStack>
            </Th>
            <Th>
              <HStack>
                <Icon as={FiServer} color="green.500" boxSize={4} />
                <Text>Resource Name</Text>
              </HStack>
            </Th>
            <Th px={1}>
              <HStack>
                <Icon as={FiGlobe} color="green.500" boxSize={4} />
                <Text>Resource Prefix</Text>
              </HStack>
            </Th>
            <Th>
              <HStack>
                <Icon as={FiMonitor} color="green.500" boxSize={4} />
                <Text>MTU Size</Text>
              </HStack>
            </Th>

            <Th>
              <HStack>
                <Icon as={BsShieldCheck} color="green.500" boxSize={4} />
                <Text>Trust</Text>
              </HStack>
            </Th>
            <Th>
              <HStack>
                <Icon as={FiEye} color="green.500" boxSize={4} />
                <Text>Spoof Check</Text>
              </HStack>
            </Th>
          </Tr>
        </Thead>
        <Tbody>
          {spec.network.F1Uinterfaces.map((iface: any) => (
            <Tr
              key={iface.id}
              _hover={{
                bg: 'green.100',
                transform: 'translateY(-5px)',
              }}
              transition="all 0.3s ease"
            >
              <Td fontWeight="bold" color="green.700">
                {iface.id}
              </Td>
              <Td px={1}>{iface.rxGroupId}</Td>
              <Td>{iface.function}</Td>
              <Td>
                <HStack spacing={2}>
                  <Text fontFamily="mono" fontSize="sm">
                    {iface.resourceName}
                  </Text>
                  <CopyButton text={iface.resourceName} label="Resource Name" />
                </HStack>
              </Td>

              <Td px={1}>{iface.resourcePrefix}</Td>
              <Td>{iface.mtuSize}</Td>
              <Td>
                <Badge colorScheme={iface.trust ? 'green' : 'red'} variant="solid" px={3} py={1} borderRadius="full">
                  <HStack spacing={1}>
                    <Icon as={iface.trust ? BsShieldCheck : BsShieldExclamation} boxSize={4} />
                    <Text>{iface.trust ? 'Trusted' : 'Untrusted'}</Text>
                  </HStack>
                </Badge>
              </Td>
              <Td>
                <Badge
                  colorScheme={iface.spoofCheck ? 'green' : 'red'}
                  variant="solid"
                  px={3}
                  py={1}
                  borderRadius="full"
                >
                  <HStack spacing={1}>
                    <Icon as={iface.spoofCheck ? FiEye : FiSlash} boxSize={4} />
                    <Text>{iface.spoofCheck ? 'Enabled' : 'Disabled'}</Text>
                  </HStack>
                </Badge>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
};

export const NetworkConfiguration: React.FC<DeploymentNetworkConfigurationProps> = ({ spec }) => {
  return (
    <Card
      variant="outline"
      bg="white"
      border="none"
      _hover={{ transform: 'translateY(-3px)' }}
      transition="all 0.3s ease"
    >
      <CardBody>
        <Tabs variant="soft-rounded" colorScheme="green" size="md">
          <TabList mb={4} bg="white" p={1} borderRadius="lg" gap={2} mx="4">
            <Tab
              fontWeight="bold"
              _hover={{
                bg: 'green.100',
                transform: 'translateY(-5px)',
              }}
              shadow="md"
              transition="all 0.3s ease"
            >
              <HStack spacing={2}>
                <Icon as={FiWifi} color="green.500" />
                <Text>F1U Interfaces</Text>
                <Badge colorScheme="green" size="sm">
                  {spec.network.F1Uinterfaces.length}
                </Badge>
              </HStack>
            </Tab>
            <Tab
              fontWeight="bold"
              _hover={{
                bg: 'green.100',
                transform: 'translateY(-5px)',
              }}
              shadow="md"
              transition="all 0.3s ease"
            >
              <HStack spacing={2}>
                <Icon as={FiWifi} color="green.500" />
                <Text>FH Interfaces</Text>
                <Badge colorScheme="green" size="sm">
                  {spec.network.FHinterfaces.length}
                </Badge>
              </HStack>
            </Tab>
          </TabList>
          <TabPanels>
            <TabPanel p={0} mx="4" shadow="md">
              <F1UInterfaceTable spec={spec} />
            </TabPanel>
            <TabPanel p={0} mx="4" shadow="md">
              <FHInterfaceTable spec={spec} />
            </TabPanel>
          </TabPanels>
        </Tabs>
      </CardBody>
    </Card>
  );
};
