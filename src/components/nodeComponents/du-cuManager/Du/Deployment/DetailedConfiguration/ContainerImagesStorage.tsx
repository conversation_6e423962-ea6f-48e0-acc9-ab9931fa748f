import React from 'react';
import {
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Box,
  HStack,
  VStack,
  Icon,
  Text,
  Badge,
  SimpleGrid,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Flex,
  Tooltip,
  Grid,
} from '@chakra-ui/react';
import {
  FiPackage,
  FiBox,
  FiKey,
  FiHardDrive,
  FiDatabase,
  FiCheckCircle,
  FiXCircle,
  FiFileText,
  FiDownloadCloud,
  FiTag,
} from 'react-icons/fi';
import { FiSave } from 'react-icons/fi';
import { CopyButton } from '../../common';

interface DeploymentContainerImagesStorageProps {
  spec: any;
}

const PersistencyCard = ({
  icon,
  title,
  data,
}: {
  icon: React.ElementType;
  title: string;
  data?: { enabled: boolean; numRunsToKeep: number; storage: string };
}) => {
  const isEnabled = data?.enabled === true;
  const cardColor = isEnabled ? 'green' : 'gray';

  return (
    <Card variant="outline" borderColor={`${cardColor}.200`}>
      <CardHeader bg={`${cardColor}.50`} py={3}>
        <Flex justify="space-between" align="center">
          <HStack>
            <Icon as={icon} color={`${cardColor}.500`} boxSize={5} />
            <Heading size="sm" color={`${cardColor}.700`}>
              {title} Persistency
            </Heading>
          </HStack>
          <Badge colorScheme={isEnabled ? 'green' : 'red'} variant="solid" px={3} py={1} borderRadius="full">
            <HStack spacing={1.5}>
              <Icon as={isEnabled ? FiCheckCircle : FiXCircle} boxSize={3.5} />
              <Text fontSize="xs">{isEnabled ? 'Enabled' : 'Disabled'}</Text>
            </HStack>
          </Badge>
        </Flex>
      </CardHeader>
      <CardBody>
        <VStack spacing={3} align="stretch">
          <HStack>
            <Text fontSize="sm" fontWeight="600" color="gray.600" w="160px">
              Num of Runs to Keep:
            </Text>
            <Text fontFamily="mono" bg="green.100" px={2} py={0.5} borderRadius="md" fontSize="sm">
              {data?.numRunsToKeep ?? 'N/A'}
            </Text>
          </HStack>
          <HStack>
            <Text fontSize="sm" fontWeight="600" color="gray.600" w="160px">
              Storage:
            </Text>
            <Text fontFamily="mono" bg="green.100" px={2} py={0.5} borderRadius="md" fontSize="sm">
              {data?.storage ?? 'N/A'}
            </Text>
          </HStack>
        </VStack>
      </CardBody>
    </Card>
  );
};

const StorageCard = ({ spec }: { spec: any }) => {
  return (
    <Card variant="outline" bg="white" _hover={{ transform: 'translateY(-3px)' }} transition="all 0.3s ease">
      <CardHeader pb={2} bg="green.50">
        <Heading size="md" color="green.700">
          <Icon as={FiHardDrive} mr={3} />
          Storage Configuration
        </Heading>
      </CardHeader>
      <CardBody>
        <VStack align="start" spacing={3}>
          <HStack>
            <Icon as={FiHardDrive} color="green.500" />
            <Text fontWeight="600">Storage Type:</Text>
            <Badge colorScheme="green">{spec.persistency?.type}</Badge>
          </HStack>
          <VStack align="start" spacing={1}>
            <HStack>
              <Icon as={FiDatabase} color="green.500" />
              <Text fontWeight="600">Mount Path:</Text>
              <Text fontFamily="mono" bg="gray.100" px={2} py={1} borderRadius="sm" fontSize="sm">
                {spec.persistency?.path}
              </Text>
            </HStack>
          </VStack>
          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4} w="full">
            <PersistencyCard icon={FiFileText} title="Log" data={spec?.persistency?.log} />
            <PersistencyCard icon={FiSave} title="Core" data={spec?.persistency?.core} />
          </SimpleGrid>
        </VStack>
      </CardBody>
    </Card>
  );
};

const pullPolicyColorScheme = (policy: string) => {
  switch (policy) {
    case 'Always':
      return 'blue';
    case 'IfNotPresent':
      return 'green';
    case 'Never':
      return 'red';
    default:
      return 'gray';
  }
};

const ContainerImageCard = ({ spec }: { spec: any }) => {
  return (
    <Card
      variant="outline"
      bg="white"
      _hover={{ transform: 'translateY(-3px)' }}
      transition="all 0.3s ease"
      borderColor="orange.100"
    >
      <CardHeader pb={2} bg="orange.50">
        <Flex justify="space-between" align="center">
          <Heading size="md" color="orange.700">
            <Icon as={FiPackage} mr={3} />
            Container Images
          </Heading>
          <HStack spacing={3}>
            <Icon as={FiKey} color="orange.700" boxSize={4} />
            <Text fontWeight="600" fontSize="sm">
              Pull Secret:
            </Text>
            <Badge colorScheme="orange" variant="solid" px={2} py={0.5}>
              {spec.imagePullSecrets?.[0]?.name ?? 'Not Specified'}
            </Badge>
          </HStack>
        </Flex>
      </CardHeader>
      <CardBody>
        {/* Header Row */}
        <Grid
          templateColumns={{ md: '2fr 3fr auto auto' }}
          gap={4}
          px={4}
          py={2}
          borderBottom="1px"
          borderColor="orange.200"
          alignItems="center"
        >
          <HStack spacing={2}>
            <Icon as={FiBox} color="orange.500" boxSize={3.5} />
            <Text fontSize="xs" fontWeight="bold" color="gray.500" textTransform="uppercase">
              Image Name
            </Text>
          </HStack>
          <HStack spacing={2}>
            <Icon as={FiDatabase} color="orange.500" boxSize={3.5} />
            <Text fontSize="xs" fontWeight="bold" color="gray.500" textTransform="uppercase">
              Repository
            </Text>
          </HStack>
          <HStack spacing={2}>
            <Icon as={FiTag} color="orange.500" boxSize={3.5} />
            <Text fontSize="xs" fontWeight="bold" color="gray.500" textTransform="uppercase">
              Tag
            </Text>
          </HStack>
          <HStack spacing={2}>
            <Icon as={FiDownloadCloud} color="orange.500" boxSize={3.5} />
            <Text fontSize="xs" fontWeight="bold" color="gray.500" textTransform="uppercase">
              Pull Policy
            </Text>
          </HStack>
        </Grid>

        {/* Data Rows */}
        <VStack spacing={2.5} mt={3} align="stretch">
          {Object.entries(spec.images || {}).map(([name, config]) => (
            <Grid
              key={name}
              templateColumns={{ md: '2fr 3fr auto auto' }}
              gap={4}
              p={3}
              borderRadius="lg"
              borderWidth="1px"
              borderColor="orange.100"
              _hover={{
                borderColor: 'orange.200',
                bg: 'orange.100',
                transform: 'translateY(-2px)',
              }}
              transition="all 0.2s ease-in-out"
              alignItems="center"
            >
              <Tooltip label={name} placement="top" hasArrow>
                <Text fontWeight="bold" fontSize="sm" isTruncated>
                  {name}
                </Text>
              </Tooltip>
              <Tooltip label={(config as any).repository} placement="top" hasArrow>
                <HStack spacing={0}>
                  <Text isTruncated maxW="150px" fontSize="sm">
                    {(config as any).repository || '-'}
                  </Text>
                  {(config as any).repository && <CopyButton text={(config as any).repository} label="Repository" />}
                </HStack>
              </Tooltip>
              <Badge colorScheme="blue" variant="solid" px={2.5} py={1}>
                {(config as any).tag || 'latest'}
              </Badge>
              <Badge colorScheme={pullPolicyColorScheme((config as any).pullPolicy)} variant="subtle" px={2.5} py={1}>
                {(config as any).pullPolicy}
              </Badge>
            </Grid>
          ))}
        </VStack>
      </CardBody>
    </Card>
  );
};

export const ContainerImagesStorage: React.FC<DeploymentContainerImagesStorageProps> = ({ spec }) => {
  return (
    <Card
      variant="outline"
      bg="white"
      _hover={{ transform: 'translateY(-5px)' }}
      transition="all 0.3s ease"
      border="none"
    >
      <CardBody>
        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6} mx="4">
          <ContainerImageCard spec={spec} />
          <StorageCard spec={spec} />
        </SimpleGrid>
      </CardBody>
    </Card>
  );
};
