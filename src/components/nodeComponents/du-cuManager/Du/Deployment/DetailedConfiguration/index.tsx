import {
  Accordion,
  Card,
  CardHeader,
  CardBody,
  HStack,
  Icon,
  Heading,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Box,
  Text,
  Badge,
  Button,
  Flex,
} from '@chakra-ui/react';
import { FiSettings, FiWifi, FiGlobe, FiPackage, FiChevronUp } from 'react-icons/fi';
import { ServicesIntegration } from './ServicesIntegration';
import { NetworkConfiguration } from './NetworkConfiguration';
import { ContainerImagesStorage } from './ContainerImagesStorage';
import { useState } from 'react';

// Centralized accordion configuration
const accordionConfig = [
  {
    id: 'network',
    title: 'Network Interfaces',
    icon: FiWifi,
    color: 'green',
    component: NetworkConfiguration,
    badge: (spec: any) => `${spec.network.F1Uinterfaces.length + spec.network.FHinterfaces.length} total`,
  },
  {
    id: 'services',
    title: 'Services & VES Integration',
    icon: FiGlobe,
    color: 'blue',
    component: ServicesIntegration,
    badge: (spec: any) => (spec.syncmon ? `Sync: ${spec.syncmon}` : null),
  },
  {
    id: 'containers',
    title: 'Container Images & Storage',
    icon: FiPackage,
    color: 'orange',
    component: ContainerImagesStorage,
    badge: () => null,
  },
];

export const DetailedConfiguration = ({ spec, colorScheme }: { spec: any; colorScheme: string }) => {
  const [expandedItems, setExpandedItems] = useState<number[]>([]);
  const hasExpandedItems = expandedItems.length > 0;
  const collapseAll = () => {
    setExpandedItems([]);
  };
  return (
    <Card bg="white" variant="outline" shadow="lg" borderColor={`${colorScheme}.200`}>
      <CardHeader py={6}>
        <Flex justifyContent="space-between" alignItems="center">
          <HStack spacing={3}>
            <Box
              bg={`${colorScheme}.500`}
              bgGradient={`linear(135deg, ${colorScheme}.400, ${colorScheme}.600)`}
              borderRadius="full"
              p={2}
              shadow="md"
            >
              <Icon as={FiSettings} boxSize={6} color="white" />
            </Box>
            <Heading size="md" color={`${colorScheme}.700`}>
              Deployment Detailed Configuration
            </Heading>
          </HStack>
          <Button
            size="md"
            colorScheme={colorScheme}
            variant="outline"
            onClick={collapseAll}
            isDisabled={!hasExpandedItems}
            mr="4"
            shadow="lg"
            _hover={{
              shadow: 'md',
              transform: 'translateY(-5px)',
            }}
            _active={{
              shadow: 'md',
              transform: 'translateY(-5px)',
            }}
            _focus={{
              shadow: 'md',
              transform: 'translateY(-5px)',
            }}
            transition="all 0.3s ease-in-out"
          >
            <Icon as={FiChevronUp} boxSize={6} />
            Collapse All
          </Button>
        </Flex>
      </CardHeader>
      <CardBody pt={0}>
        <Accordion
          allowMultiple
          mx="4"
          index={expandedItems}
          onChange={(expandedIndex) => setExpandedItems(expandedIndex as number[])}
        >
          {accordionConfig.map((config, index) => (
            <AccordionItem
              key={config.id}
              borderWidth="1px"
              borderColor={`${colorScheme}.200`}
              borderRadius="lg"
              mb={3}
              shadow="md"
              _hover={{
                shadow: 'md',
                borderColor: `${colorScheme}.200`,
                transform: 'translateY(-5px)',
              }}
              transition="all 0.3s ease-in-out"
            >
              <h2>
                <AccordionButton
                  py={4}
                  px={5}
                  shadow="md"
                  borderRadius="lg"
                  _hover={{ bg: `${colorScheme}.100` }}
                  _expanded={{ bg: `${colorScheme}.100` }}
                >
                  <Box as="span" flex="1" textAlign="left" fontWeight="bold" fontSize="lg">
                    <HStack spacing={3}>
                      <Box
                        bg={`${colorScheme}.500`}
                        bgGradient={`linear(135deg, ${colorScheme}.400, ${colorScheme}.600)`}
                        borderRadius="full"
                        p={2}
                        shadow="md"
                      >
                        <Icon as={config.icon} color="white" boxSize={5} />
                      </Box>
                      <Text>{config.title}</Text>
                      {config.badge(spec) && (
                        <Badge colorScheme={colorScheme} variant="solid" borderRadius="full">
                          {config.badge(spec)}
                        </Badge>
                      )}
                    </HStack>
                  </Box>
                  <AccordionIcon />
                </AccordionButton>
              </h2>
              <AccordionPanel>
                <config.component spec={spec} />
              </AccordionPanel>
            </AccordionItem>
          ))}
        </Accordion>
      </CardBody>
    </Card>
  );
};
