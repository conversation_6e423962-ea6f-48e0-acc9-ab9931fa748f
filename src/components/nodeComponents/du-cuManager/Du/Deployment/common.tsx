import { Badge, Icon } from '@chakra-ui/react';

// Enhanced Status Badge with improved styling
const StatusBadge = ({
  status,
  color,
  icon,
  size = 'md',
}: {
  status: string;
  color: string;
  icon: any;
  size?: 'sm' | 'md' | 'lg';
}) => {
  return (
    <Badge
      colorScheme={color}
      p={size === 'lg' ? 3 : size === 'md' ? 2 : 1}
      borderRadius="full"
      display="flex"
      alignItems="center"
      fontSize={size === 'lg' ? 'md' : size === 'md' ? 'sm' : 'xs'}
      fontWeight="bold"
      variant="solid"
    >
      <Icon as={icon} mr={size === 'sm' ? 1 : 2} boxSize={size === 'lg' ? 5 : size === 'md' ? 4 : 3} />
      {status}
    </Badge>
  );
};

export default StatusBadge;
