import React from 'react';
import { <PERSON>, Button, VStack, Text, Alert, AlertIcon, Flex, Heading, Icon, useDisclosure } from '@chakra-ui/react';
import { FiEye } from 'react-icons/fi';

import MetadataComponent from '../common';
import DeploymentVitals from './DeploymentVitals';
import { DetailedConfiguration } from './DetailedConfiguration';
import JsonViewerDialog from '../JsonViewerDialog';

const DeploymentDetails = ({ data, colorScheme }: { data: any; colorScheme: string }) => {
  const { isOpen: isJsonViewerOpen, onOpen: onJsonViewerOpen, onClose: onJsonViewerClose } = useDisclosure();
  if (!data || !data.content || !data.status) {
    return (
      <Alert status="error" borderRadius="lg">
        <AlertIcon />
        <Box>
          <Text fontWeight="bold">No Deployment Data</Text>
          <Text>The deployment information could not be loaded.</Text>
        </Box>
      </Alert>
    );
  }

  const { status, content } = data;
  const { spec, metadata } = content;

  return (
    <VStack spacing={6} align="stretch">
      {/* HEADER WITH JSON VIEWER BUTTON */}
      <Flex justify="space-between" align="center" mb={1}>
        <Heading size="xl" color={`${colorScheme}.700`}>
          Deployment Details
        </Heading>
        <Button
          leftIcon={<Icon as={FiEye} />}
          colorScheme={colorScheme}
          variant="outline"
          size="md"
          onClick={onJsonViewerOpen}
        >
          View Raw JSON
        </Button>
      </Flex>

      <MetadataComponent metadata={metadata} colorScheme={colorScheme} />
      <DeploymentVitals status={status} spec={spec} colorScheme={colorScheme} />
      <DetailedConfiguration spec={spec} colorScheme={colorScheme} />

      {/* JSON VIEWER DIALOG */}
      <JsonViewerDialog
        isOpen={isJsonViewerOpen}
        onClose={onJsonViewerClose}
        title="Deployment Details"
        data={data}
        colorScheme={colorScheme}
      />
    </VStack>
  );
};

export default DeploymentDetails;
