import {
  Card,
  CardHeader,
  HStack,
  Icon,
  Box,
  Text,
  SimpleGrid,
  VStack,
  Divider,
  Badge,
  CardBody,
} from '@chakra-ui/react';
import {
  FiServer,
  FiUser,
  FiCloud,
  FiBriefcase,
  FiRefreshCw,
  FiPlayCircle,
  FiGitBranch,
  FiCheckCircle,
  FiXCircle,
} from 'react-icons/fi';

const SpecificationRow = ({
  icon,
  label,
  value,
  colorScheme,
}: {
  icon: any;
  label: string;
  value: string;
  colorScheme: string;
}) => {
  return (
    <HStack spacing={2} align="center" _hover={{ transform: 'translateY(-3px)' }} transition="all 0.3s ease">
      <Icon as={icon} color={`${colorScheme}.500`} boxSize={5} />
      <Text fontSize="sm" fontWeight="600" color="gray.600" w="90px">
        {label}:
      </Text>
      <Badge colorScheme={colorScheme} variant="subtle" fontWeight="bold">
        {value}
      </Badge>
    </HStack>
  );
};

const Specifications = ({ spec, status, colorScheme }: { spec: any; status: any; colorScheme: string }) => {
  const getDeploymentColor = (status: string) => {
    const s = status?.toLowerCase() || 'unknown';
    switch (s) {
      case 'ready':
        return 'green';
      case 'pending':
        return 'yellow';
      case 'failed':
        return 'red';
      default:
        return 'gray';
    }
  };

  const deploymentColor = getDeploymentColor(status.deploymentStatus);

  const rows = [
    { icon: FiUser, label: 'Profile', value: spec.deploymentProfile, colorScheme: colorScheme },
    { icon: FiCloud, label: 'Cloud Infra', value: spec.cloudInfra, colorScheme: colorScheme },
    { icon: FiBriefcase, label: 'RU Vendor', value: spec.ruVendor, colorScheme: colorScheme },
    { icon: FiRefreshCw, label: 'Syncmon', value: spec.syncmon, colorScheme: colorScheme },
    { icon: FiPlayCircle, label: 'Action', value: spec.action, colorScheme: colorScheme },
  ];

  return (
    <Card
      variant="outline"
      shadow="lg"
      borderColor={`${colorScheme}.200`}
      _hover={{ transform: 'translateY(-5px)' }}
      transition="all 0.3s ease"
    >
      <CardHeader borderTopRadius="md" py={4}>
        <HStack justify="space-between" align="center">
          <HStack spacing={3}>
            <Box
              bg={`${colorScheme}.500`}
              bgGradient={`linear(135deg, ${colorScheme}.400, ${colorScheme}.600)`}
              borderRadius="full"
              p={2}
              shadow="md"
            >
              <Icon as={FiServer} boxSize={6} color="white" />
            </Box>
            <Box>
              <Text fontSize="lg" fontWeight="bold">
                Specifications
              </Text>
            </Box>
          </HStack>
        </HStack>
      </CardHeader>
      <CardBody py={4}>
        <HStack spacing={4} align="stretch">
          <VStack spacing={4} align="stretch">
            {/* Key Configuration Info */}
            <SimpleGrid columns={{ base: 1, md: 1 }} spacingX={8} spacingY={4}>
              {rows.map((row) => (
                <SpecificationRow key={row.label} {...row} />
              ))}
            </SimpleGrid>
          </VStack>

          <VStack spacing={4} align="stretch">
            <HStack spacing={2} align="center" _hover={{ transform: 'translateY(-2px)' }} transition="all 0.3s ease">
              <Icon as={FiGitBranch} color={`${colorScheme}.500`} boxSize={4} />
              <Text fontSize="sm" fontWeight="600" color="gray.600" w="90px">
                FHmplane:
              </Text>
              <Badge
                colorScheme={spec.FHmplane === 'enable' ? 'green' : 'red'}
                variant="solid"
                px={3}
                py={1}
                borderRadius="full"
              >
                <HStack spacing={1}>
                  <Icon as={spec.FHmplane === 'enable' ? FiCheckCircle : FiXCircle} boxSize={4} />
                  <Text>{spec.FHmplane === 'enable' ? 'Enabled' : 'Disabled'}</Text>
                </HStack>
              </Badge>
            </HStack>

            <HStack spacing={2} align="center" _hover={{ transform: 'translateY(-2px)' }} transition="all 0.3s ease">
              <Icon as={FiCheckCircle} color={`${colorScheme}.500`} boxSize={4} />
              <Text fontSize="sm" fontWeight="600" color="gray.600" w="90px">
                Status:
              </Text>
              <Badge colorScheme={spec.enabled ? 'green' : 'red'} variant="solid" px={3} py={1} borderRadius="full">
                <HStack spacing={1}>
                  <Icon as={spec.enabled ? FiCheckCircle : FiXCircle} boxSize={4} />
                  <Text>{spec.enabled ? 'Enabled' : 'Disabled'}</Text>
                </HStack>
              </Badge>
            </HStack>
          </VStack>
        </HStack>
      </CardBody>
    </Card>
  );
};

export default Specifications;
