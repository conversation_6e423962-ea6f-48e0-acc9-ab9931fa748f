import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, SimpleGrid, CardBody, Collapse, Text } from '@chakra-ui/react';
import StatusBadge from '../common';
import PodStatus from './PodStatus';
import CellStatus from './CellStatus';
import { useState } from 'react';
import { FiActivity } from 'react-icons/fi';
import { Box, Heading, Icon } from '@chakra-ui/react';
import Specifications from './Specifications';
import { getConfigStatus } from '../../common';

const DeploymentVitals = ({ status, spec, colorScheme }: { status: any; spec: any; colorScheme: string }) => {
  const [showContent, setShowContent] = useState(true);

  const totalPods = Object.keys(status.podStatus || {}).length;
  const readyPods = Object.values(status.podStatus || {}).filter((s) => s === 'Ready').length;
  const totalCells = status.cellStatus?.length || 0;
  const upCells = status.cellStatus?.filter((c: any) => c.cellState === 'UP').length || 0;

  const podHealth = totalPods > 0 ? Math.round((readyPods / totalPods) * 100) : 0;
  const cellHealth = totalCells > 0 ? Math.round((upCells / totalCells) * 100) : 0;

  const { status_color, status_icon, status_text } = getConfigStatus(status.deploymentStatus.toString());
  return (
    <Card
      variant="outline"
      shadow="lg"
      borderColor={`${colorScheme}.200`}
      overflow="hidden"
      _hover={{
        shadow: 'xl',
        transform: 'translateY(-2px)',
      }}
      transition="all 0.3s ease-in-out"
    >
      <CardHeader
        py={3}
        cursor="pointer"
        bg={showContent ? `${colorScheme}.100` : 'transparent'}
        shadow="md"
        onClick={() => setShowContent(!showContent)}
        _hover={{
          bg: `${colorScheme}.100`,
        }}
        transition="all 0.2s ease-in-out"
      >
        <HStack justify="space-between" align="center">
          <HStack spacing={3}>
            <Box
              bg={`${colorScheme}.500`}
              bgGradient={`linear(135deg, ${colorScheme}.400, ${colorScheme}.600)`}
              borderRadius="full"
              p={2}
              shadow="md"
            >
              <Icon as={FiActivity} boxSize={6} color="white" />
            </Box>
            <Box>
              <Heading size="md" color={`${colorScheme}.700`}>
                Deployment Vitals
              </Heading>
              <Text fontSize="sm" fontWeight="bold">
                An overview of runtime status
              </Text>
            </Box>
          </HStack>
          <StatusBadge status={status_text} size="md" color={status_color} icon={status_icon} />
        </HStack>
      </CardHeader>
      <Collapse in={showContent} animateOpacity>
        <CardBody py={4}>
          <SimpleGrid columns={{ base: 1, lg: 3 }} spacing={6}>
            {/* CARD 1: DEPLOYMENT STATUS */}
            <Specifications spec={spec} status={status} colorScheme={colorScheme} />
            <PodStatus
              status={status}
              podHealth={podHealth}
              readyPods={readyPods}
              totalPods={totalPods}
              colorScheme={colorScheme}
            />
            <CellStatus
              status={status}
              cellHealth={cellHealth}
              upCells={upCells}
              totalCells={totalCells}
              colorScheme={colorScheme}
            />
            {/* CARD 3: CELL STATUS */}
          </SimpleGrid>
        </CardBody>
      </Collapse>
    </Card>
  );
};

export default DeploymentVitals;
