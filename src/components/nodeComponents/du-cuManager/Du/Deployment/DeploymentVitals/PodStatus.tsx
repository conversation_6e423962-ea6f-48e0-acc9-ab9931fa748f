import {
  Card,
  Card<PERSON><PERSON>er,
  HStack,
  Icon,
  Box,
  Heading,
  Text,
  CircularProgress,
  CircularProgressLabel,
  VStack,
  Flex,
  CardBody,
} from '@chakra-ui/react';
import { FiCpu, FiBox } from 'react-icons/fi';

import StatusBadge from '../common';
import { getConfigStatus } from '../../common';

const PodStatus = ({
  status,
  podHealth,
  readyPods,
  totalPods,
  colorScheme,
}: {
  status: any;
  podHealth: number;
  readyPods: number;
  totalPods: number;
  colorScheme: string;
}) => {
  return (
    <Card
      variant="outline"
      shadow="lg"
      borderColor={`${colorScheme}.200`}
      _hover={{ transform: 'translateY(-5px)' }}
      transition="all 0.3s ease"
    >
      <CardHeader borderTopRadius="md" py={1}>
        <HStack justify="space-between" align="center">
          <HStack spacing={3}>
            <Box
              bg={`${colorScheme}.500`}
              bgGradient={`linear(135deg, ${colorScheme}.400, ${colorScheme}.600)`}
              borderRadius="full"
              p={2}
              shadow="md"
            >
              <Icon as={FiCpu} boxSize={6} color="white" />
            </Box>
            <Box>
              <Heading size="md">Pod Status</Heading>
              <Text fontSize="sm" fontWeight="500">
                Container Health
              </Text>
            </Box>
          </HStack>
          <Box textAlign="center">
            <CircularProgress value={podHealth} color={`${colorScheme}.400`} size="60px" thickness="8px">
              <CircularProgressLabel fontSize="sm" fontWeight="bold" color={`${colorScheme}.700`}>
                {podHealth}%
              </CircularProgressLabel>
            </CircularProgress>
            <Text fontSize="md" color={`${colorScheme}.600`} mt={1}>
              {readyPods}/{totalPods} Ready
            </Text>
          </Box>
        </HStack>
      </CardHeader>

      <CardBody py={3}>
        <VStack spacing={2} align="stretch">
          {status.podStatus &&
            Object.entries(status.podStatus).map(([pod, podState]) => {
              const { status_color, status_icon, status_text } = getConfigStatus(podState as string);
              return (
                <Flex
                  key={pod}
                  justify="space-between"
                  align="center"
                  p={3}
                  borderRadius="lg"
                  borderWidth={1}
                  borderColor={`${colorScheme}.200`}
                  _hover={{ bg: `${colorScheme}.50`, transform: 'translateY(-2px)' }}
                  transition="all 0.2s"
                >
                  <HStack spacing={2} justify="space-between">
                    <Icon as={FiBox} color={`${colorScheme}.500`} boxSize={6} />
                    <Text fontSize="md">{pod}</Text>
                  </HStack>
                  <StatusBadge status={status_text} size="sm" color={status_color} icon={status_icon} />
                </Flex>
              );
            })}
        </VStack>
      </CardBody>
    </Card>
  );
};

export default PodStatus;
