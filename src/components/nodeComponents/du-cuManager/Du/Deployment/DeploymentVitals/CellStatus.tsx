import {
  Card,
  CardHeader,
  HStack,
  Icon,
  Box,
  Heading,
  Text,
  CardBody,
  CircularProgressLabel,
  CircularProgress,
  SimpleGrid,
  Flex,
} from '@chakra-ui/react';
import { FiRadio } from 'react-icons/fi';
import StatusBadge from '../common';
import { getConfigStatus } from '../../common';

const CellStatus = ({
  status,
  cellHealth,
  upCells,
  totalCells,
  colorScheme,
}: {
  status: any;
  cellHealth: number;
  upCells: number;
  totalCells: number;
  colorScheme: string;
}) => {
  return (
    <Card
      variant="outline"
      shadow="lg"
      borderColor={`${colorScheme}.200`}
      _hover={{ transform: 'translateY(-5px)' }}
      transition="all 0.3s ease"
    >
      <CardHeader borderTopRadius="md" py={1}>
        <HStack justify="space-between" align="center">
          <HStack spacing={3}>
            <Box
              bg={`${colorScheme}.500`}
              bgGradient={`linear(135deg, ${colorScheme}.400, ${colorScheme}.600)`}
              borderRadius="full"
              p={2}
              shadow="md"
            >
              <Icon as={FiRadio} boxSize={6} color="white" />
            </Box>
            <Box>
              <Heading size="md">Cell Status</Heading>
              <Text fontSize="sm">Radio Network</Text>
            </Box>
          </HStack>
          <Box textAlign="center">
            <CircularProgress value={cellHealth} color={`${colorScheme}.400`} size="60px" thickness="8px">
              <CircularProgressLabel fontSize="sm" fontWeight="bold" color={`${colorScheme}.700`}>
                {cellHealth}%
              </CircularProgressLabel>
            </CircularProgress>
            <Text fontSize="md" color={`${colorScheme}.600`} mt={1}>
              {upCells}/{totalCells} Active
            </Text>
          </Box>
        </HStack>
      </CardHeader>

      <CardBody py={3}>
        <SimpleGrid columns={1} spacing={3}>
          {status.cellStatus?.map((cell: any) => {
            const { status_color, status_icon, status_text } = getConfigStatus(cell.cellState as string);
            return (
              <Flex
                key={cell.cellId}
                justify="space-between"
                align="center"
                p={3}
                borderRadius="lg"
                borderWidth={1}
                borderColor={`${colorScheme}.200`}
                _hover={{ bg: `${colorScheme}.50`, transform: 'translateY(-2px)' }}
                transition="all 0.2s"
              >
                <HStack spacing={2} justify="space-between">
                  <Icon as={FiRadio} boxSize={6} color={`${colorScheme}.500`} />
                  <Text fontSize="md">Cell {cell.cellId}</Text>
                  <StatusBadge status={status_text} size="sm" color={status_color} icon={status_icon} />
                </HStack>
              </Flex>
            );
          })}
        </SimpleGrid>
      </CardBody>
    </Card>
  );
};

export default CellStatus;
