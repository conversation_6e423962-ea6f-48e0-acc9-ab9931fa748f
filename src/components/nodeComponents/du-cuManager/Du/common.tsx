import React from 'react';
import {
  <PERSON>H<PERSON>er,
  Card,
  HStack,
  Icon,
  Heading,
  VStack,
  SimpleGrid,
  CardBody,
  Text,
  Badge,
  Box,
  Wrap,
  WrapItem,
  Tag,
  Tooltip,
  IconButton,
  useClipboard,
  useToast,
  Divider,
  useColorModeValue,
  Collapse,
  useDisclosure,
  Flex,
} from '@chakra-ui/react';
import {
  FiServer,
  FiGlobe,
  FiLayers,
  FiClock,
  FiKey,
  FiDatabase,
  FiTag,
  FiRefreshCw,
  FiCopy,
  FiCheck,
  FiChevronDown,
  FiChevronUp,
  FiCalendar,
  FiPackage,
  FiCheckCircle,
  FiXCircle,
  FiAlertTriangle,
  FiLock,
  FiUnlock,
  FiToggleLeft,
  FiArrowUp,
  FiArrowDown,
} from 'react-icons/fi';
import { FaMapMarkerAlt } from 'react-icons/fa';
import { TimeElapsed } from '../../server/acp/AcpCard';
import { MdDirectionsRun } from 'react-icons/md';

// Copy button component with animation
export const CopyButton = ({ text, label }: { text: string | React.ReactNode; label: string }) => {
  const { hasCopied, onCopy } = useClipboard(text as string);
  const toast = useToast();

  const handleCopy = () => {
    onCopy();
    toast({
      title: `${label} copied!`,
      status: 'success',
      duration: 2000,
      isClosable: true,
      position: 'top-right',
      size: 'sm',
    });
  };

  return (
    <Tooltip label={`Copy ${label}`} hasArrow placement="top">
      <IconButton
        aria-label={`Copy ${label}`}
        icon={<Icon as={hasCopied ? FiCheck : FiCopy} />}
        size="xs"
        variant="ghost"
        colorScheme={hasCopied ? 'green' : 'gray'}
        onClick={handleCopy}
        _hover={{
          transform: 'scale(1.1)',
          bg: hasCopied ? 'green.50' : 'gray.100',
        }}
        transition="all 0.2s"
      />
    </Tooltip>
  );
};

// Simplified info row component
const InfoRow = ({
  icon,
  label,
  value,
  colorScheme,
  badge,
  copyable = false,
}: {
  icon: any;
  label: string;
  value: string | React.ReactNode;
  colorScheme?: string;
  badge?: boolean;
  copyable?: boolean;
}) => {
  return (
    <HStack
      spacing={3}
      p={2}
      borderRadius="md"
      _hover={{
        bg: colorScheme ? `${colorScheme}.50` : 'blue.50',
        transform: 'translateY(-2px)',
      }}
      transition="all 0.2s ease"
    >
      <Icon as={icon} color={`${colorScheme}.500`} boxSize={4} />

      <VStack align="start" spacing={0} flex={1}>
        <Text fontSize="xs" fontWeight="bold" color="gray.500" textTransform="uppercase">
          {label}
        </Text>
        {badge ? (
          <Badge
            colorScheme={colorScheme}
            variant="subtle"
            fontSize="xs"
            px={2}
            py={1}
            borderRadius="md"
            fontWeight="bold"
          >
            {value}
          </Badge>
        ) : (
          <Text
            fontSize="sm"
            fontWeight="600"
            color="gray.700"
            fontFamily={copyable ? 'mono' : 'inherit'}
            wordBreak={copyable ? 'break-all' : 'normal'}
          >
            {value}
          </Text>
        )}
      </VStack>

      {copyable && <CopyButton text={value as string} label={label} />}
    </HStack>
  );
};

const ConfigStatusBadge = ({ status }: { status: any }) => {
  // status values can be success, pendingapp
  switch (status.configStatus) {
    case 'success':
      return (
        <Badge colorScheme="green" variant="solid" px={3} py={1} borderRadius="full">
          <Icon as={FiCheckCircle} boxSize={6} />
          <Text>{status.configStatus.toUpperCase()}</Text>
        </Badge>
      );
    case 'pendingapp':
      return (
        <Badge colorScheme="yellow" variant="solid" px={3} py={1} borderRadius="full">
          <Icon as={FiClock} boxSize={6} />
          <Text>{status.configStatus.toUpperCase()}</Text>
        </Badge>
      );
    case 'failed':
      return (
        <Badge colorScheme="red" variant="solid" px={3} py={1} borderRadius="full">
          <Icon as={FiXCircle} boxSize={6} />
          <Text>{status.configStatus.toUpperCase()}</Text>
        </Badge>
      );
    default:
      return (
        <Badge colorScheme="gray" variant="solid" px={3} py={1} borderRadius="full">
          <Icon as={FiAlertTriangle} boxSize={6} />
          <Text>{status.configStatus.toUpperCase()}</Text>
        </Badge>
      );
  }
};

const SpecStatusBadge = ({ spec }: { spec: any }) => {
  if (spec.enabled) {
    return (
      <Badge colorScheme="green" variant="solid" px={3} py={1} borderRadius="full">
        <Icon as={FiCheckCircle} boxSize={6} />
        <Text>ENABLED</Text>
      </Badge>
    );
  } else {
    return (
      <Badge colorScheme="red" variant="solid" px={3} py={1} borderRadius="full">
        <Icon as={FiXCircle} boxSize={6} />
        <Text>DISABLED</Text>
      </Badge>
    );
  }
};

// Status Row Component
export const StatusRow = ({
  label,
  value,
  icon,
  colorScheme,
}: {
  label: string;
  value: boolean | string;
  icon?: any;
  colorScheme?: string;
}) => {
  const isEnabled = typeof value === 'boolean' ? value : value === 'enabled' || value === 'Unlocked';

  // Determine the appropriate icon based on the value
  let statusIcon;
  let statusValue;
  let statusColor;
  if (typeof value === 'string') {
    const lowerValue = value.toLowerCase();
    if (lowerValue === 'locked' || lowerValue === 'lock') {
      statusIcon = FiLock;
      statusValue = 'Locked';
      statusColor = 'red';
    } else if (lowerValue === 'unlocked' || lowerValue === 'unlock') {
      statusIcon = FiUnlock;
      statusValue = 'Unlocked';
      statusColor = 'green';
    } else if (lowerValue === 'enabled' || lowerValue === 'enable') {
      statusIcon = FiCheckCircle;
      statusValue = 'Enabled';
      statusColor = 'green';
    } else if (lowerValue === 'disabled' || lowerValue === 'disable') {
      statusIcon = FiXCircle;
      statusValue = 'Disabled';
      statusColor = 'red';
    } else {
      statusIcon = isEnabled ? FiCheckCircle : FiXCircle;
      statusValue = isEnabled ? 'Enabled' : 'Disabled';
      statusColor = isEnabled ? 'green' : 'red';
    }
  } else {
    statusIcon = isEnabled ? FiCheckCircle : FiXCircle;
    statusValue = isEnabled ? 'Enabled' : 'Disabled';
    statusColor = isEnabled ? 'green' : 'red';
  }

  return (
    <Flex
      justify="space-between"
      align="center"
      p={3}
      borderRadius="md"
      borderWidth={1}
      borderColor={`${colorScheme}.100`}
      _hover={{
        bg: `${statusColor}.100`,
        transform: 'translateY(-3px)',
        shadow: 'lg',
      }}
      transition="all 0.3s ease"
    >
      <HStack spacing={2}>
        <Icon as={icon || FiToggleLeft} color={`${colorScheme}.500`} boxSize={4} />
        <Text fontWeight="600" fontSize="sm">
          {label}
        </Text>
      </HStack>
      <Badge colorScheme={isEnabled ? 'green' : 'red'} variant="solid" px={3} py={1} borderRadius="full">
        <HStack spacing={1}>
          <Icon as={statusIcon} boxSize={4} />
          <Text>{statusValue.toUpperCase()}</Text>
        </HStack>
      </Badge>
    </Flex>
  );
};

const MetadataComponent = ({ metadata, colorScheme }: { metadata: any; colorScheme: string }) => {
  const { isOpen, onToggle } = useDisclosure({ defaultIsOpen: false });

  const createdDate = new Date(metadata.creationTimestamp);
  const timeAgo = TimeElapsed({ initialUptime: createdDate.toDateString(), fontSize: 'xlg' });

  return (
    <Card
      variant="outline"
      shadow="lg"
      borderColor={`${colorScheme}.200`}
      overflow="hidden"
      _hover={{
        shadow: 'xl',
        transform: 'translateY(-3px)',
      }}
      transition="all 0.3s ease"
    >
      <CardHeader
        py={3}
        bg={isOpen ? `${colorScheme}.100` : 'transparent'}
        cursor="pointer"
        onClick={onToggle}
        _hover={{
          bg: `${colorScheme}.100`,
          transform: 'translateY(-2px)',
        }}
        shadow="lg"
        transition="all 0.3s ease"
      >
        <HStack justify="space-between">
          <HStack spacing={3}>
            <Box
              bg={`${colorScheme}.500`}
              bgGradient={`linear(135deg, ${colorScheme}.400, ${colorScheme}.600)`}
              borderRadius="full"
              p={2}
              shadow="md"
            >
              <Icon as={FiPackage} boxSize={6} color="white" />
            </Box>

            <VStack align="start" spacing={0}>
              <Heading size="md" color={`${colorScheme}.700`}>
                Resource Metadata
              </Heading>
              <Text fontSize="md">Kubernetes Resource Information</Text>
            </VStack>
          </HStack>

          <Tooltip label={isOpen ? 'Collapse' : 'Expand'} hasArrow>
            <Icon
              as={isOpen ? FiChevronUp : FiChevronDown}
              boxSize={5}
              color={`${colorScheme}.600`}
              _hover={{ transform: 'scale(1.1)' }}
              transition="all 0.2s"
            />
          </Tooltip>
        </HStack>
      </CardHeader>

      <Collapse in={isOpen} animateOpacity>
        <CardBody p={4} bg="gray.25">
          <VStack spacing={4} align="stretch">
            {/* All Information in One Section */}
            <SimpleGrid columns={{ base: 1, md: 3 }} spacing={3}>
              <InfoRow icon={FiServer} label="Resource Name" value={metadata.name} copyable colorScheme={colorScheme} />
              <InfoRow
                icon={FaMapMarkerAlt}
                label="Site Name"
                value={metadata.labels.site}
                copyable
                badge
                colorScheme={colorScheme}
              />

              {/* TODO: Uncomment if we need to show namespace */}
              {/* <InfoRow icon={FiGlobe} label="Namespace" value={metadata.namespace} badge badgeColor="cyan" /> */}
              <InfoRow
                icon={FiLayers}
                label="Version"
                value={`v${metadata.annotations?.rel || 'N/A'}`}
                badge
                colorScheme={colorScheme}
              />
              <InfoRow
                icon={FiRefreshCw}
                label="Generation"
                value={metadata.generation?.toString() || 'N/A'}
                colorScheme={colorScheme}
              />
              <InfoRow
                icon={FiCalendar}
                label="Created"
                value={createdDate.toLocaleString()}
                colorScheme={colorScheme}
              />
              <InfoRow icon={FiClock} label="Age" value={timeAgo} badge colorScheme={colorScheme} />
              <InfoRow icon={FiKey} label="UID" value={metadata.uid} copyable colorScheme={colorScheme} />
              <InfoRow
                icon={FiDatabase}
                label="Resource Version"
                value={metadata.resourceVersion}
                copyable
                colorScheme={colorScheme}
              />
            </SimpleGrid>
          </VStack>
        </CardBody>
      </Collapse>
    </Card>
  );
};

export const DataItem = ({ label, value }: { label: string; value: any }) => (
  <Box bg="gray.50" p={3} borderRadius="md" borderWidth="1px" borderColor="gray.200">
    <VStack align="start" spacing={1}>
      <Text fontSize="sm" color="gray.500" fontWeight="medium">
        {label}
      </Text>
      <Text fontSize="md" fontWeight="bold" color="gray.800">
        {value}
      </Text>
    </VStack>
  </Box>
);

export const StatusIndicator = ({ isEnabled }: { isEnabled: boolean }) => (
  <Badge colorScheme={isEnabled ? 'green' : 'red'} variant="solid" borderRadius="full" px={3} py={1}>
    {isEnabled ? 'Enabled' : 'Disabled'}
  </Badge>
);

export const getConfigStatus = (status: string) => {
  let status_color, status_icon, status_text;

  switch (status.toString().toLowerCase()) {
    case 'true':
      status_color = 'green';
      status_icon = FiCheckCircle;
      status_text = 'ENABLED';
      break;
    case 'false':
      status_color = 'red';
      status_icon = FiXCircle;
      status_text = 'DISABLED';
      break;
    case 'yes':
      status_color = 'green';
      status_icon = FiCheckCircle;
      status_text = 'YES';
      break;
    case 'no':
      status_color = 'red';
      status_icon = FiXCircle;
      status_text = 'NO';
      break;
    case 'up':
      status_color = 'green';
      status_icon = FiArrowUp;
      status_text = 'UP';
      break;
    case 'running':
      status_color = 'green';
      status_icon = MdDirectionsRun;
      status_text = 'RUNNING';
      break;
    case 'down':
      status_color = 'red';
      status_icon = FiArrowDown;
      status_text = 'DOWN';
      break;
    case 'enabled':
    case 'enable':
      status_color = 'green';
      status_icon = FiCheckCircle;
      status_text = 'ENABLED';
      break;
    case 'ready':
      status_color = 'green';
      status_icon = FiCheckCircle;
      status_text = 'READY';
      break;
    case 'success':
      status_color = 'green';
      status_icon = FiCheckCircle;
      status_text = 'SUCCESS';
      break;
    case 'disabled':
    case 'disable':
      status_color = 'red';
      status_icon = FiXCircle;
      status_text = 'DISABLED';
      break;
    case 'error':
      status_color = 'red';
      status_icon = FiXCircle;
      status_text = 'ERROR';
      break;
    case 'failed':
      status_color = 'red';
      status_icon = FiXCircle;
      status_text = 'FAILED';
      break;
    case 'notready':
      status_color = 'red';
      status_icon = FiXCircle;
      status_text = 'NOT READY';
      break;
    case 'locked':
    case 'lock':
      status_color = 'red';
      status_icon = FiLock;
      status_text = 'LOCKED';
      break;
    case 'unlocked':
    case 'unlock':
      status_color = 'green';
      status_icon = FiUnlock;
      status_text = 'UNLOCKED';
      break;
    case 'pendingapp':
    case 'pending':
      status_color = 'yellow';
      status_icon = FiClock;
      status_text = 'PENDING';
      break;
    default:
      status_color = 'gray';
      status_icon = FiAlertTriangle;
      status_text = status;
  }
  return { status_color, status_icon, status_text };
};

export default MetadataComponent;
