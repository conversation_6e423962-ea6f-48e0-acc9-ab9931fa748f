import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import {
  AlertDialog,
  AlertDialogBody,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogOverlay,
  AlertDialogCloseButton,
  Badge,
  Box,
  Text,
  HStack,
  Icon,
  Collapse,
  Input,
  InputGroup,
  InputLeftElement,
  IconButton,
  Tooltip,
  Flex,
  useColorModeValue,
  Code,
} from '@chakra-ui/react';
import {
  FiChevronDown,
  FiChevronRight,
  FiChevronUp,
  FiSearch,
  FiCopy,
  FiDownload,
  FiEye,
  FiEyeOff,
} from 'react-icons/fi';
import { CopyButton } from './common';

interface JsonViewerDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  data: any;
  colorScheme?: string;
}

interface JsonNodeProps {
  data: any;
  keyName?: string;
  level?: number;
  searchTerm?: string;
  parentData?: any;
  colorScheme?: string;
  parentExpanded?: boolean;
  registerMatch: (el: HTMLElement | null, match: boolean) => void;
}

/****************************
 * Utility helpers
 ***************************/
const formatValue = (value: any): string => {
  if (value === null) return 'null';
  if (value === undefined) return 'undefined';
  if (typeof value === 'string') return `"${value}"`;
  return String(value);
};

const matchesSearch = (value: any, keyName: string, searchTerm: string): boolean => {
  if (!searchTerm) return false;
  const q = searchTerm.toLowerCase();
  if (keyName.toLowerCase().includes(q)) return true;
  if (typeof value === 'string' && value.toLowerCase().includes(q)) return true;
  if (typeof value === 'number' && value.toString().includes(q)) return true;
  if (typeof value === 'boolean' && value.toString().includes(q)) return true;
  return false;
};

const hasSiblingMatch = (parent: any, searchTerm: string): boolean => {
  if (!searchTerm || typeof parent !== 'object' || parent === null) return false;
  const q = searchTerm.toLowerCase();
  if (Array.isArray(parent)) {
    return parent.some((v, i) => matchesSearch(v, i.toString(), q));
  }
  return Object.entries(parent).some(([k, v]) => matchesSearch(v, k, q));
};

const hasMatchingChild = (obj: any, searchTerm: string): boolean => {
  if (!searchTerm || typeof obj !== 'object' || obj === null) return false;
  if (Array.isArray(obj)) {
    return obj.some((v, i) => matchesSearch(v, i.toString(), searchTerm) || hasMatchingChild(v, searchTerm));
  }
  return Object.entries(obj).some(([k, v]) => matchesSearch(v, k, searchTerm) || hasMatchingChild(v, searchTerm));
};

const shouldAutoExpand = (obj: any, searchTerm: string, level: number): boolean => {
  if (!searchTerm) return level < 2;
  return hasMatchingChild(obj, searchTerm);
};

const shouldShowNode = (
  data: any,
  keyName: string,
  searchTerm: string,
  parentData?: any,
  parentExpanded?: boolean
): boolean => {
  if (!searchTerm) return true;
  if (parentExpanded) return true;
  if (matchesSearch(data, keyName, searchTerm)) return true;
  if (hasMatchingChild(data, searchTerm)) return true;
  if (hasSiblingMatch(parentData, searchTerm)) return true;
  return false;
};

const countMatches = (obj: any, searchTerm: string, keyName = ''): number => {
  if (!searchTerm) return 0;
  let c = matchesSearch(obj, keyName, searchTerm) ? 1 : 0;
  if (typeof obj === 'object' && obj !== null) {
    if (Array.isArray(obj)) {
      obj.forEach((v, i) => (c += countMatches(v, searchTerm, i.toString())));
    } else {
      Object.entries(obj).forEach(([k, v]) => (c += countMatches(v, searchTerm, k)));
    }
  }
  return c;
};

/****************************
 * JsonNode component
 ***************************/
const JsonNode: React.FC<JsonNodeProps> = ({
  data,
  keyName,
  level = 0,
  searchTerm = '',
  parentData,
  colorScheme = 'teal',
  parentExpanded = false,
  registerMatch,
}) => {
  const [isExpanded, setIsExpanded] = useState(() => shouldAutoExpand(data, searchTerm, level));
  const [showRaw, setShowRaw] = useState(false);
  const [manualToggle, setManualToggle] = useState(false);
  const [toggleSearchSnapshot, setToggleSearchSnapshot] = useState('');

  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');

  const headerRef = useRef<HTMLDivElement>(null);
  const primitiveRef = useRef<HTMLDivElement>(null);

  const nodeMatches = matchesSearch(data, keyName || '', searchTerm);
  const shouldShow = shouldShowNode(data, keyName || '', searchTerm, parentData, parentExpanded);

  // sync expansion with searchTerm unless manually toggled in same query
  useEffect(() => {
    if (!manualToggle || toggleSearchSnapshot !== searchTerm) {
      setIsExpanded(shouldAutoExpand(data, searchTerm, level));
      setManualToggle(false);
    }
  }, [searchTerm]);

  // register match element for navigation
  useEffect(() => {
    const el = typeof data === 'object' && data !== null ? headerRef.current : primitiveRef.current;
    registerMatch(el, nodeMatches);
  }, [nodeMatches]);

  const handleToggle = () => {
    setIsExpanded((p) => !p);
    setManualToggle(true);
    setToggleSearchSnapshot(searchTerm);
  };

  const toggleRaw = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowRaw((p) => !p);
  };

  const copyValue = (e: React.MouseEvent) => {
    e.stopPropagation();
    const txt = typeof data === 'object' ? JSON.stringify(data, null, 2) : String(data);
    navigator.clipboard.writeText(txt);
  };

  if (!shouldShow) return null;

  /* Primitive value node */
  if (typeof data !== 'object' || data === null) {
    return (
      <HStack
        ref={primitiveRef}
        data-search-match={nodeMatches || undefined}
        spacing={2}
        py={1}
        px={2}
        borderRadius="md"
        bg={nodeMatches && searchTerm ? `${colorScheme}.100` : 'transparent'}
        _hover={{ bg: hoverBg }}
        minH="32px"
        align="center"
      >
        {keyName && (
          <Text fontSize="sm" fontWeight="medium" color={`${colorScheme}.600`}>
            {keyName}:
          </Text>
        )}
        <Code fontSize="sm" bg="transparent" p={0}>
          {formatValue(data)}
        </Code>
        <CopyButton text={String(data)} label={`${keyName}`} />
      </HStack>
    );
  }

  /* Object or array node */
  const isArray = Array.isArray(data);
  const entries = isArray ? data.map((v, i) => [i.toString(), v]) : Object.entries(data);
  const preview = entries.slice(0, 3);

  return (
    <Box>
      {/* Header row */}
      <HStack
        ref={headerRef}
        data-search-match={nodeMatches || undefined}
        spacing={2}
        py={2}
        px={2}
        borderRadius="md"
        cursor="pointer"
        _hover={{ bg: hoverBg }}
        bg={nodeMatches && searchTerm ? `${colorScheme}.100` : isExpanded ? `${colorScheme}.50` : 'transparent'}
        minH="40px"
        onClick={handleToggle}
      >
        <Icon as={isExpanded ? FiChevronDown : FiChevronRight} boxSize={4} color={`${colorScheme}.500`} />
        {keyName && (
          <Text fontSize="sm" fontWeight="semibold" color={`${colorScheme}.700`}>
            {keyName}:
          </Text>
        )}
        {!isExpanded && (
          <Text fontSize="xs" color="gray.500" isTruncated maxW="200px">
            {isArray ? '[' : '{'}
            {preview.map(([k, v], idx) => (
              <span key={k as string}>
                {isArray ? '' : `${k}: `}
                {typeof v === 'object' ? (Array.isArray(v) ? '[...]' : '{...}') : formatValue(v)}
                {idx < preview.length - 1 ? ', ' : ''}
              </span>
            ))}
            {entries.length > 3 && '...'}
            {isArray ? ']' : '}'}
          </Text>
        )}
        <Flex ml="auto">
          <Tooltip label="Toggle raw view">
            <IconButton
              aria-label="raw"
              icon={<Icon as={showRaw ? FiEyeOff : FiEye} />}
              size="xs"
              variant="ghost"
              onClick={toggleRaw}
            />
          </Tooltip>
          <Tooltip label="Copy JSON">
            <IconButton aria-label="copy" icon={<FiCopy />} size="xs" variant="ghost" onClick={copyValue} />
          </Tooltip>
        </Flex>
      </HStack>

      <Collapse in={showRaw} animateOpacity>
        <Box ml={12} mr={4} mb={2} p={3} bg="gray.900" borderRadius="md" border="1px solid" borderColor={borderColor}>
          <Code fontSize="xs" whiteSpace="pre" display="block" color="green.300" bg="transparent">
            {JSON.stringify(data, null, 2)}
          </Code>
        </Box>
      </Collapse>

      <Collapse in={isExpanded} animateOpacity>
        <Box pl={10}>
          {entries.map(([k, v]) => (
            <JsonNode
              key={k as string}
              data={v}
              keyName={k as string}
              level={level + 1}
              searchTerm={searchTerm}
              parentData={data}
              colorScheme={colorScheme}
              parentExpanded={isExpanded && manualToggle}
              registerMatch={registerMatch}
            />
          ))}
        </Box>
      </Collapse>
    </Box>
  );
};

/****************************
 * Main dialog component
 ***************************/
const JsonViewerDialog: React.FC<JsonViewerDialogProps> = ({ isOpen, onClose, title, data, colorScheme = 'teal' }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const cancelRef = useRef<HTMLButtonElement>(null);
  const matchElsRef = useRef<HTMLElement[]>([]);
  const [currentMatch, setCurrentMatch] = useState(0);

  const registerMatch = useCallback((el: HTMLElement | null, match: boolean) => {
    if (!el) return;
    if (match) {
      el.setAttribute('data-search-match', 'true');
    } else {
      el.removeAttribute('data-search-match');
    }
  }, []);

  // recalc matches when searchTerm changes
  useEffect(() => {
    if (!searchTerm) {
      matchElsRef.current = [];
      setCurrentMatch(0);
      return;
    }
    matchElsRef.current = Array.from(document.querySelectorAll('[data-search-match="true"]')) as HTMLElement[];
    setCurrentMatch(matchElsRef.current.length ? 0 : -1);
  }, [searchTerm, data]);

  const scrollToCurrent = useCallback(() => {
    if (currentMatch < 0 || matchElsRef.current.length === 0) return;
    matchElsRef.current[currentMatch].scrollIntoView({ behavior: 'smooth', block: 'center' });
  }, [currentMatch]);

  useEffect(() => {
    scrollToCurrent();
  }, [currentMatch, scrollToCurrent]);

  const nextMatch = () => {
    if (!matchElsRef.current.length) return;
    setCurrentMatch((i) => (i + 1) % matchElsRef.current.length);
  };
  const prevMatch = () => {
    if (!matchElsRef.current.length) return;
    setCurrentMatch((i) => (i - 1 + matchElsRef.current.length) % matchElsRef.current.length);
  };

  const totalResults = useMemo(() => countMatches(data, searchTerm), [data, searchTerm]);

  // download & copy all
  const downloadJson = () => {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${title.replace(/\s+/g, '_').toLowerCase()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };
  const copyAll = () => navigator.clipboard.writeText(JSON.stringify(data, null, 2));

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  return (
    <AlertDialog isOpen={isOpen} onClose={onClose} leastDestructiveRef={cancelRef} size="full" isCentered>
      <AlertDialogOverlay bg="blackAlpha.600" backdropFilter="blur(4px)">
        <AlertDialogContent
          maxH="90vh"
          bg={bgColor}
          border="1px solid"
          borderColor={borderColor}
          borderRadius="xl"
          shadow="2xl"
        >
          <AlertDialogHeader
            fontSize="lg"
            fontWeight="bold"
            borderBottom="1px solid"
            borderColor={borderColor}
            bg={`${colorScheme}.50`}
            borderTopRadius="xl"
          >
            <HStack justify="space-between">
              <HStack spacing={3}>
                <Icon as={FiEye} color={`${colorScheme}.600`} />
                <Text color={`${colorScheme}.800`}>{title} - JSON Viewer</Text>
              </HStack>
              <HStack spacing={2} mr="5">
                <Tooltip label="Download JSON">
                  <IconButton
                    aria-label="download"
                    icon={<FiDownload />}
                    size="sm"
                    variant="ghost"
                    onClick={downloadJson}
                  />
                </Tooltip>
                <Tooltip label="Copy all JSON">
                  <IconButton aria-label="copy-all" icon={<FiCopy />} size="sm" variant="ghost" onClick={copyAll} />
                </Tooltip>
              </HStack>
            </HStack>
            {/* search bar */}
            <Box mt={3}>
              <InputGroup>
                <InputLeftElement pointerEvents="none">
                  <Icon as={FiSearch} color={`${colorScheme}.600`} />
                </InputLeftElement>
                <Input
                  placeholder="Search keys and values..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') nextMatch();
                  }}
                  bg="white"
                  borderColor="gray.300"
                  _focus={{ borderColor: `${colorScheme}.400`, boxShadow: `0 0 0 1px ${colorScheme}.400` }}
                />
                {totalResults > 0 && (
                  <Flex align="center" gap={1} ml={2}>
                    <Tooltip label="Previous result">
                      <IconButton
                        aria-label="prev"
                        icon={<FiChevronUp />}
                        size="sm"
                        variant="ghost"
                        onClick={prevMatch}
                      />
                    </Tooltip>
                    <Text fontSize="sm" color="gray.600">
                      {currentMatch + 1}/{totalResults}
                    </Text>
                    <Tooltip label="Next result">
                      <IconButton
                        aria-label="next"
                        icon={<FiChevronDown />}
                        size="sm"
                        variant="ghost"
                        onClick={nextMatch}
                      />
                    </Tooltip>
                  </Flex>
                )}
              </InputGroup>
              {searchTerm && (
                <Flex justify="space-between" align="center" mt={3} gap={2}>
                  <Text fontSize="md" color="gray.500" ml="4">
                    Searching for: &quot;{searchTerm}&quot;
                  </Text>
                  <Badge
                    colorScheme={totalResults ? 'green' : 'orange'}
                    variant="solid"
                    borderRadius="full"
                    fontSize="md"
                  >
                    {totalResults} {totalResults === 1 ? 'result' : 'results'} found
                  </Badge>
                </Flex>
              )}
            </Box>
          </AlertDialogHeader>

          <AlertDialogCloseButton color={`${colorScheme}.600`} size="md" />
          <AlertDialogBody pb={10} maxH="full" overflowY="auto">
            <Box p={4}>
              {data ? (
                <JsonNode data={data} searchTerm={searchTerm} colorScheme={colorScheme} registerMatch={registerMatch} />
              ) : (
                <Box textAlign="center" py={8}>
                  <Text color="gray.500">No data available</Text>
                </Box>
              )}
            </Box>
          </AlertDialogBody>
        </AlertDialogContent>
      </AlertDialogOverlay>
    </AlertDialog>
  );
};

export default JsonViewerDialog;
