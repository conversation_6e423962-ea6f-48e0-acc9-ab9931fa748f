import React, { useState } from 'react';
import {
  Box,
  SimpleGrid,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Text,
  VStack,
  HStack,
  Badge,
  Icon,
  Flex,
  <PERSON>lapse,
  Tooltip,
  useDisclosure,
  Button,
} from '@chakra-ui/react';
import {
  FiCheckCircle,
  FiXCircle,
  FiServer,
  FiBox,
  FiHash,
  FiPackage,
  FiWifi,
  FiFileText,
  FiTag,
  FiEye,
} from 'react-icons/fi';
import { formatInReadableTimeDate } from '../../../../utils/formatInReadableTimeData';
import { TimeElapsed } from '../../server/acp/AcpCard';
import { MdDirectionsRun, MdHistory } from 'react-icons/md';
import { MdAccessTime } from 'react-icons/md';
import { MdWarning } from 'react-icons/md';
import { MdCheckCircle } from 'react-icons/md';
import { MdHelpOutline } from 'react-icons/md';
import { CopyButton } from './common';
import { startCase } from 'lodash';
import JsonViewerDialog from './JsonViewerDialog';

const getPhaseConfig = (phase: string) => {
  switch (phase?.toLowerCase()) {
    case 'running':
      return { color: 'green', icon: MdDirectionsRun, label: 'Running' };
    case 'pending':
      return { color: 'yellow', icon: MdAccessTime, label: 'Pending' };
    case 'failed':
      return { color: 'red', icon: MdWarning, label: 'Failed' };
    case 'succeeded':
      return { color: 'blue', icon: MdCheckCircle, label: 'Succeeded' };
    default:
      return { color: 'gray', icon: MdHelpOutline, label: 'Unknown' };
  }
};

// Modern Container & System Status Card
const ContainerCard = ({
  container,
  conditions,
  node_serial_no,
}: {
  container: any;
  conditions: any[];
  node_serial_no: any;
}) => {
  const statusColor = container.ready ? 'green' : 'red';
  const imageName = container.image.split('/').pop()?.split(':')[0] || 'Unknown';
  const imageTag = container.image.split(':').pop() || 'latest';

  // Sort conditions by timestamp for timeline
  const sortedConditions = [...conditions].sort(
    (a, b) => new Date(b.last_transition_time).getTime() - new Date(a.last_transition_time).getTime()
  );

  return (
    <Card
      shadow="lg"
      variant="outline"
      borderColor={`${statusColor}.100`}
      overflow="hidden"
      w="full"
      _hover={{ transform: 'translateY(-5px)' }}
      transition="all 0.3s ease"
    >
      <CardHeader py={4}>
        <Flex justify="space-between" align="center">
          <HStack spacing={3}>
            <Box bg={`${statusColor}.500`} borderRadius="full" p={3}>
              <Icon as={FiBox} boxSize={8} color="white" />
            </Box>
            <VStack align="start" spacing={1}>
              <HStack spacing={1}>
                <Heading size="md" color={`${statusColor}.700`}>
                  {node_serial_no}
                </Heading>
                <CopyButton text={node_serial_no} label="Node Serial No" />
              </HStack>
              <Text fontSize="sm">Container & System Status</Text>
            </VStack>
          </HStack>

          <VStack spacing={2} align="end">
            <Badge colorScheme={statusColor} variant="solid" px={3} py={1} borderRadius="full" fontSize="sm">
              <Icon as={container.ready ? FiCheckCircle : FiXCircle} mr={1} />
              {container.ready ? 'READY' : 'NOT READY'}
            </Badge>
            <Badge
              colorScheme={container.started ? 'green' : 'red'}
              variant="solid"
              px={2}
              py={1}
              borderRadius="full"
              fontSize="sm"
            >
              <Icon as={container.started ? FiCheckCircle : FiXCircle} mr={1} />
              {container.started ? 'STARTED' : 'STOPPED'}
            </Badge>
          </VStack>
        </Flex>
      </CardHeader>

      <CardBody py={5}>
        <VStack spacing={5} align="stretch">
          {/* Essential Info Grid */}
          <SimpleGrid columns={2} spacing={4}>
            <Box
              p={4}
              borderRadius="lg"
              border="1px solid"
              borderColor="gray.200"
              shadow="sm"
              _hover={{
                shadow: 'md',
                borderColor: 'blue.100',
              }}
              transition="all 0.2s ease-in-out"
            >
              <VStack
                spacing={4}
                align="stretch"
                _hover={{
                  transform: 'translateY(-3px)',
                }}
                transition="all 0.2s ease-in-out"
              >
                <HStack spacing={3}>
                  <Icon as={FiPackage} color="blue.500" boxSize={6} />
                  <Text fontSize="md" fontWeight="bold">
                    Image Information
                  </Text>
                </HStack>
                <VStack spacing={3} align="stretch" w="full" pl={1}>
                  {/* Image Name */}
                  <HStack align="center">
                    <Icon as={FiFileText} color="green.500" boxSize="1.2em" />
                    <Text fontSize="sm" fontWeight="medium" w="50px">
                      Name
                    </Text>
                    <Text fontSize="sm" noOfLines={1} title={imageName}>
                      {imageName}
                    </Text>
                  </HStack>

                  {/* Image Tag */}
                  <HStack align="center">
                    <Icon as={FiTag} color="orange.500" boxSize="1.2em" />
                    <Text fontSize="sm" fontWeight="medium" w="50px">
                      Tag
                    </Text>
                    <Text fontSize="sm" fontFamily="mono">
                      {imageTag}
                    </Text>
                  </HStack>

                  {/* Image ID */}
                  <HStack align="center">
                    <Icon as={FiHash} color="purple.500" boxSize="1.2em" />
                    <Text fontSize="sm" fontWeight="medium" color="gray.600" w="50px">
                      ID
                    </Text>
                    {/* give copy to clipboard icon */}

                    <Tooltip label={container.image_id.split('@')[1]} placement="top" hasArrow>
                      <Text fontSize="xs" fontFamily="mono" noOfLines={1} title={container.image_id}>
                        {container.image_id.split('@')[1]?.substring(0, 25)}...
                      </Text>
                    </Tooltip>

                    <CopyButton text={container.image_id.split('@')[1]} label="Image ID" />
                  </HStack>
                </VStack>
              </VStack>
            </Box>

            <Box
              p={3}
              bg="white"
              borderRadius="lg"
              border="1px"
              borderColor="purple.100"
              _hover={{
                transform: 'translateY(-3px)',
              }}
              transition="all 0.3s ease"
            >
              <HStack spacing={2} mb={2}>
                <Icon as={FiHash} color="purple.500" boxSize={4} />
                <Text fontSize="md" fontWeight="600">
                  Container ID
                </Text>
              </HStack>
              <HStack spacing={2}>
                <Tooltip label={container.container_id.split('//')[1]} placement="top" hasArrow>
                  <Text fontSize="sm" fontFamily="mono">
                    {container.container_id.split('//')[1]?.substring(0, 30)}...
                  </Text>
                </Tooltip>
                <CopyButton text={container.container_id.split('//')[1]} label="Container ID" />
              </HStack>
            </Box>
          </SimpleGrid>

          {/* System Conditions Timeline */}
          <Box>
            <HStack spacing={2} mb={3}>
              <Icon as={MdHistory} color="green.600" boxSize={6} />
              <Text fontSize="md" fontWeight="bold" color="gray.700">
                History
              </Text>
            </HStack>
            <VStack spacing={2} align="stretch">
              {sortedConditions.slice(0, 5).map((condition) => (
                <Flex
                  key={condition.type}
                  justify="space-between"
                  align="center"
                  p={3}
                  borderRadius="lg"
                  border="1px"
                  borderColor={condition.status === 'True' ? 'green.100' : 'red.100'}
                  _hover={{
                    bg: condition.status === 'True' ? 'green.100' : 'red.100',
                    transform: 'translateY(-2px)',
                  }}
                  transition="all 0.3s ease"
                >
                  <HStack spacing={3} flex="1">
                    <Icon
                      as={condition.status === 'True' ? FiCheckCircle : FiXCircle}
                      color={condition.status === 'True' ? 'green.500' : 'red.500'}
                      boxSize={4}
                    />
                    <Text fontSize="sm" fontWeight="600" color="gray.700" minW="140px">
                      {condition.type.replace(/([A-Z])/g, ' $1').trim()}
                    </Text>
                  </HStack>
                  <HStack spacing={3} flex="1">
                    <Text fontSize="sm" color="gray.600" minW="140px" textAlign="right">
                      {formatInReadableTimeDate(condition.last_transition_time)}
                    </Text>
                    <VStack align="start">
                      <Badge
                        colorScheme={condition.status === 'True' ? 'green' : 'red'}
                        variant="solid"
                        borderRadius="full"
                        size="sm"
                        minW="50px"
                        textAlign="center"
                      >
                        {condition.status}
                      </Badge>
                      {condition.reason && (
                        <Text color="red" fontSize="sm" fontWeight="bold">
                          {startCase(condition.reason)}
                        </Text>
                      )}
                    </VStack>
                  </HStack>
                </Flex>
              ))}
            </VStack>
          </Box>
        </VStack>
      </CardBody>
    </Card>
  );
};

// Modern Network Card
const NetworkCard = ({ data }: { data: any }) => {
  return (
    <Card
      bg="white"
      shadow="lg"
      variant="outline"
      borderColor="cyan.100"
      overflow="hidden"
      _hover={{ transform: 'translateY(-5px)' }}
      transition="all 0.3s ease"
    >
      <CardHeader py={4}>
        <HStack spacing={3}>
          <Box bg="cyan.500" borderRadius="full" p={3}>
            <Icon as={FiWifi} boxSize={8} color="white" />
          </Box>
          <VStack align="start" spacing={1}>
            <Heading size="md" color="cyan.700">
              Network Topology
            </Heading>
            <Text fontSize="sm">Pod & Host Connectivity</Text>
          </VStack>
        </HStack>
      </CardHeader>

      <CardBody py={1}>
        <VStack spacing={1} align="stretch">
          {/* Visual Network Diagram */}
          <Flex justify="space-around" align="center" p={2} bg="cyan.25" borderRadius="xl">
            {/* Host Node */}
            <VStack
              spacing={3}
              _hover={{
                transform: 'translateY(-3px)',
              }}
              transition="all 0.3s ease"
            >
              <Box bg="cyan.500" bgGradient="linear(135deg, cyan.400, cyan.600)" borderRadius="full" p={3} shadow="lg">
                <Icon as={FiServer} boxSize={6} color="white" />
              </Box>
              <VStack spacing={1}>
                <Text fontSize="md" fontWeight="bold">
                  Host Node
                </Text>

                <HStack spacing={1} p="2">
                  <Icon as={FiWifi} boxSize={6} color="cyan.500" />
                  <Text fontSize="md" fontFamily="mono" bg="white" borderRadius="md" shadow="sm">
                    {data.host_ip}
                  </Text>
                  <CopyButton text={data.host_ip} label="Host IP" />
                </HStack>
              </VStack>
            </VStack>

            {/* Connection */}
            <VStack
              spacing={2}
              _hover={{
                transform: 'translateY(-3px)',
              }}
              transition="all 0.3s ease"
            >
              <Box h="6px" w="350px" bgGradient="linear(135deg, cyan.400, blue.600)" borderRadius="full" />
              <Text fontSize="sm" fontWeight="600">
                Network Bridge
              </Text>
            </VStack>

            {/* Pod */}
            <VStack
              spacing={3}
              _hover={{
                transform: 'translateY(-3px)',
              }}
              transition="all 0.3s ease"
            >
              <Box bg="blue.500" bgGradient="linear(135deg, blue.400, blue.600)" borderRadius="full" p={3} shadow="lg">
                <Icon as={FiBox} boxSize={6} color="white" />
              </Box>
              <VStack spacing={1}>
                <Text fontSize="sm" fontWeight="bold">
                  Pod Instance
                </Text>
                <HStack spacing={2} p="2">
                  <Icon as={FiWifi} boxSize={5} color="blue.500" />
                  <Text fontSize="md" fontFamily="mono" bg="white" borderRadius="md" shadow="sm">
                    {data.pod_ip}
                  </Text>
                  <CopyButton text={data.pod_ip} label="Pod IP" />
                </HStack>
              </VStack>
            </VStack>
          </Flex>

          {/* Network Details */}
          <SimpleGrid columns={2} spacing={4}>
            <Box
              p={6}
              borderRadius="lg"
              border="1px"
              borderColor="cyan.100"
              _hover={{
                transform: 'translateY(-3px)',
              }}
              transition="all 0.3s ease"
            >
              <HStack spacing={2} mb={3}>
                <Icon as={FiServer} color="cyan.500" boxSize={4} />
                <Text fontSize="sm" fontWeight="bold">
                  Host Network
                </Text>
              </HStack>
              <VStack align="start" spacing={2} w="full">
                {data.host_ips?.map((hostIp: any, idx: number) => (
                  <HStack
                    key={idx}
                    spacing={2}
                    w="full"
                    _hover={{ transform: 'translateY(-3px)' }}
                    transition="all 0.3s ease"
                  >
                    <Icon as={FiWifi} boxSize={5} color="cyan.500" />
                    <Text fontSize="xs" fontFamily="mono">
                      {hostIp.ip}
                    </Text>
                    <CopyButton text={hostIp.ip} label={`Host IP ${idx + 1}`} />
                  </HStack>
                ))}
              </VStack>
            </Box>

            <Box
              p={4}
              borderRadius="lg"
              border="1px"
              borderColor="blue.100"
              _hover={{
                transform: 'translateY(-3px)',
              }}
              transition="all 0.3s ease"
            >
              <HStack spacing={2} mb={3}>
                <Icon as={FiBox} color="blue.500" boxSize={4} />
                <Text fontSize="sm" fontWeight="bold">
                  Pod Network
                </Text>
              </HStack>
              <VStack align="start" spacing={2} w="full">
                {data.pod_ips?.map((podIp: any, idx: number) => (
                  <HStack
                    key={idx}
                    spacing={2}
                    w="full"
                    _hover={{ transform: 'translateY(-3px)' }}
                    transition="all 0.3s ease"
                  >
                    <Icon as={FiWifi} boxSize={5} color="blue.500" />
                    <Text fontSize="xs" fontFamily="mono">
                      {podIp.ip}
                    </Text>
                    <CopyButton text={podIp.ip} label={`Pod IP ${idx + 1}`} />
                  </HStack>
                ))}
              </VStack>
            </Box>
          </SimpleGrid>
        </VStack>
      </CardBody>
    </Card>
  );
};

// Main Pod Details Component
const InstanceDetails = ({ data, pod_status, node_serial_no }: { data: any; pod_status: any; node_serial_no: any }) => {
  const [showContent, setShowContent] = useState(false);
  const { isOpen: isJsonViewerOpen, onOpen: onJsonViewerOpen, onClose: onJsonViewerClose } = useDisclosure();
  const phaseConfig = getPhaseConfig(data.phase);

  const statusColor = pod_status === 'OK' ? 'green' : 'red';

  return (
    <Card
      shadow="md"
      borderWidth={1}
      borderRadius="2xl"
      borderColor="green.200"
      overflow="hidden"
      position="relative"
      _before={{
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '4px',
        bg: `${phaseConfig.color}.400`,
        bgGradient: `linear(to-r, ${phaseConfig.color}.400, ${phaseConfig.color}.600)`,
      }}
    >
      <CardHeader
        onClick={() => setShowContent(!showContent)}
        cursor="pointer"
        _hover={{
          transform: 'translateY(-5px)',
        }}
        transition="all 0.3s ease"
        shadow="md"
      >
        <Flex align="center" justify="space-between">
          <HStack spacing={6}>
            <Box
              bg={`${phaseConfig.color}.500`}
              bgGradient={`linear(135deg, ${phaseConfig.color}.400, ${phaseConfig.color}.600)`}
              borderRadius="full"
              p={4}
              shadow="md"
            >
              <Icon as={FiBox} boxSize={10} color="white" />
            </Box>

            <VStack>
              <Heading size="xl" color="green.600" fontWeight="800">
                Instance Status
              </Heading>
              <Badge colorScheme="purple" variant="outline" px={3} py={1} borderRadius="full">
                QoS: {data.qos_class}
              </Badge>
            </VStack>

            <HStack spacing={4}>
              <Box
                textAlign="center"
                p={4}
                bg={`${phaseConfig.color}.50`}
                borderRadius="xl"
                border="1px"
                borderColor={`${phaseConfig.color}.100`}
              >
                <HStack spacing={2}>
                  <Icon as={phaseConfig.icon} boxSize={8} color={`${phaseConfig.color}.500`} mb={2} />
                  <Text fontSize="lg" color={`${phaseConfig.color}.600`} fontWeight="600" mb="1">
                    {phaseConfig.label.toUpperCase()}
                  </Text>
                </HStack>

                <Text fontSize="2xl" fontWeight="bold" color="orange.700">
                  {TimeElapsed({ initialUptime: data.start_time, fontSize: 'xlg' })}
                </Text>
              </Box>
            </HStack>
          </HStack>
          <HStack>
            <Icon as={statusColor === 'green' ? FiCheckCircle : FiXCircle} mr={1} boxSize={14} color={statusColor} />
            <Text fontSize="2xl" fontWeight="bold" color={statusColor}>
              {statusColor === 'green' ? 'OK' : 'ERROR'}
            </Text>
          </HStack>
        </Flex>
      </CardHeader>
      <Collapse in={showContent} animateOpacity>
        <CardBody p={4} borderTop={'1px solid #e2e2e2'} borderRadius="lg">
          <HStack justifyContent="flex-end" mb={4}>
            <Button
              leftIcon={<Icon as={FiEye} />}
              colorScheme={'green'}
              variant="outline"
              size="md"
              onClick={onJsonViewerOpen}
            >
              View Raw JSON
            </Button>
          </HStack>
          <VStack spacing={6} align="stretch">
            {/* DETAILED INFORMATION GRID */}
            <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
              {/* Container Details with System Conditions */}

              {data.container_statuses?.map((container: any) => (
                <ContainerCard
                  key={container.name}
                  container={container}
                  conditions={data.conditions || []}
                  node_serial_no={node_serial_no}
                />
              ))}

              {/* Network Information */}
              <NetworkCard data={data} />
            </SimpleGrid>

            <JsonViewerDialog
              isOpen={isJsonViewerOpen}
              onClose={onJsonViewerClose}
              title="Instance Details"
              data={data}
              colorScheme={'green'}
            />
          </VStack>
        </CardBody>
      </Collapse>
    </Card>
  );
};

export default InstanceDetails;
