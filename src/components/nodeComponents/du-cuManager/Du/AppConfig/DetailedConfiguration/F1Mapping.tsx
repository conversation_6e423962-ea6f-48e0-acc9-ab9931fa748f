import React from 'react';
import {
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Alert,
  AlertIcon,
  As,
  Box,
  Card,
  CardBody,
  HStack,
  Icon,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  TableContainer,
  Text,
  VStack,
  Grid,
} from '@chakra-ui/react';
import { FiHardDrive, FiHash, FiLayers, FiMap, FiRadio, FiTerminal } from 'react-icons/fi';

interface PLMNId {
  MCC: string;
  MNC: string;
}

interface NRCgi {
  cellId: number;
  gNBId: number;
  gNBIdLength: number;
  pLMNId: PLMNId;
}

interface CellMap {
  id: number;
  NRDUCellID: number;
  NRCgi: NRCgi;
  servedPlmns: any[];
}

interface F1MappingData {
  id: number;
  F1CendpointId: number;
  F1UendpointId: number;
  cellMap: CellMap[];
}

const StatBox = ({
  icon,
  label,
  value,
  colorScheme,
}: {
  icon: As;
  label: string;
  value: string | number;
  colorScheme: string;
}) => (
  <Stat
    p={3}
    bg={`${colorScheme}.50`}
    borderRadius="md"
    textAlign="center"
    borderWidth="1px"
    borderColor={`${colorScheme}.100`}
  >
    <StatLabel fontSize="sm" color={`${colorScheme}.600`} fontWeight="medium">
      <HStack justify="center" spacing={1.5}>
        <Icon as={icon} />
        <Text>{label}</Text>
      </HStack>
    </StatLabel>
    <StatNumber fontSize="xl" color={`${colorScheme}.800`} fontFamily="mono" mt={1}>
      {value}
    </StatNumber>
  </Stat>
);

const CellMappingDetails = ({ mapping }: { mapping: F1MappingData }) => {
  return (
    <Box>
      <Grid
        templateColumns={{ md: '1fr 1fr 1fr 1fr 1fr' }}
        gap={4}
        px={4}
        py={2}
        borderBottom="1px"
        borderColor="gray.200"
        alignItems="center"
      >
        <HStack spacing={1}>
          <Icon as={FiHash} color="orange.500" boxSize={3.5} />
          <Text fontSize="xs" fontWeight="bold" color="gray.500" textTransform="uppercase">
            Cell ID
          </Text>
        </HStack>
        <HStack spacing={1}>
          <Icon as={FiHash} color="orange.500" boxSize={3.5} />
          <Text fontSize="xs" fontWeight="bold" color="gray.500" textTransform="uppercase">
            DU Cell ID
          </Text>
        </HStack>
        <HStack spacing={1}>
          <Icon as={FiHash} color="orange.500" boxSize={3.5} />
          <Text fontSize="xs" fontWeight="bold" color="gray.500" textTransform="uppercase">
            gNB ID
          </Text>
        </HStack>
        <HStack spacing={1}>
          <Icon as={FiHash} color="orange.500" boxSize={3.5} />
          <Text fontSize="xs" fontWeight="bold" color="gray.500" textTransform="uppercase">
            gNB Len
          </Text>
        </HStack>
        <HStack spacing={1}>
          <Icon as={FiHash} color="orange.500" boxSize={3.5} />
          <Text fontSize="xs" fontWeight="bold" color="gray.500" textTransform="uppercase">
            PLMNs
          </Text>
        </HStack>
      </Grid>

      {/* Data Rows */}
      <VStack spacing={2.5} mt={3} align="stretch">
        {mapping.cellMap.map((cell) => (
          <Grid
            key={cell.id}
            templateColumns={{ md: '1fr 1fr 1fr 1fr 1fr' }}
            gap={4}
            p={3}
            bg="orange.50"
            borderRadius="lg"
            borderWidth="1px"
            borderColor="orange.200"
            _hover={{
              borderColor: 'orange.300',
              bg: 'orange.100',
              transform: 'translateY(-2px)',
            }}
            transition="all 0.2s ease-in-out"
            alignItems="center"
          >
            <Text fontWeight="bold" fontSize="sm" isTruncated>
              {cell.id}
            </Text>

            <Text isTruncated maxW="250px" fontSize="sm">
              {cell.NRDUCellID}
            </Text>
            <Text isTruncated maxW="250px" fontSize="sm">
              {cell.NRCgi.gNBId}
            </Text>
            <Text isTruncated maxW="250px" fontSize="sm">
              {cell.NRCgi.gNBIdLength}
            </Text>
            <Text isTruncated maxW="250px" fontSize="sm">
              {cell.servedPlmns?.length || 0}
            </Text>
          </Grid>
        ))}
      </VStack>
    </Box>
  );
};

const F1MappingItem = ({ mapping }: { mapping: F1MappingData }) => {
  return (
    <VStack spacing={4} align="stretch">
      <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
        <StatBox icon={FiTerminal} label="F1C Endpoint" value={mapping.F1CendpointId} colorScheme="blue" />
        <StatBox icon={FiTerminal} label="F1U Endpoint" value={mapping.F1UendpointId} colorScheme="orange" />
        <StatBox icon={FiRadio} label="Mapped Cells" value={mapping.cellMap?.length || 0} colorScheme="green" />
        <StatBox icon={FiHash} label="Mapping ID" value={mapping.id} colorScheme="purple" />
      </SimpleGrid>

      <VStack spacing={2} align="stretch" p={3} borderRadius="lg" borderWidth="1px">
        <HStack px={1}>
          <Icon as={FiLayers} color="orange.500" />
          <Text fontWeight="bold" fontSize="md" color="gray.600">
            Cell Mapping Details
          </Text>
        </HStack>
        <TableContainer>
          <CellMappingDetails mapping={mapping} />
        </TableContainer>
      </VStack>
    </VStack>
  );
};

// Content-only component (no accordion wrapper)
export const F1InterfaceMapping = ({ spec }: { spec: any }) => {
  const data = spec.F1Mapping || [];
  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <Alert status="info" borderRadius="lg">
        <AlertIcon />
        <Text>No F1 mapping data available.</Text>
      </Alert>
    );
  }

  return (
    <Card variant="outline" bg="white" border="none">
      <CardBody>
        <Tabs variant="soft-rounded" colorScheme="blue">
          <TabList display="flex" flexDirection="row" gap={2}>
            {data.map((mapping) => (
              <Tab
                key={mapping.id}
                shadow="lg"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'blue.200', transform: 'translateY(-5px)' }}
                _selected={{ bg: 'blue.200' }}
                transition="all 0.3s ease-in-out"
              >
                <VStack>
                  <HStack spacing={2}>
                    <Icon as={FiHardDrive} boxSize={6} color="blue.500" />
                    <Text>Mapping {mapping.id}</Text>
                  </HStack>
                  {mapping.cellMap.map((cell: any) => (
                    <HStack key={cell.id}>
                      <Text>MCC: {cell.NRCgi.pLMNId.MCC}</Text>
                      <Text>MNC: {cell.NRCgi.pLMNId.MNC}</Text>
                    </HStack>
                  ))}
                </VStack>
              </Tab>
            ))}
          </TabList>
          <TabPanels mt={4}>
            {data.map((mapping) => (
              <TabPanel key={mapping.id} p={4} mx={4} mt={4}>
                <F1MappingItem mapping={mapping} />
              </TabPanel>
            ))}
          </TabPanels>
        </Tabs>
      </CardBody>
    </Card>
  );
};
