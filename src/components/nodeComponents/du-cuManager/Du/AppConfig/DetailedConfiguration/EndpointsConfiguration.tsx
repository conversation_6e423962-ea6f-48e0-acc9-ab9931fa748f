import React from 'react';
import {
  Box,
  HStack,
  Icon,
  Text,
  Badge,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  TableContainer,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  CardBody,
  Card,
} from '@chakra-ui/react';
import {
  FiMapPin,
  FiLink,
  FiTerminal,
  FiShare2,
  FiCheckCircle,
  FiXCircle,
  FiHash,
  FiGlobe,
  FiServer,
  FiSettings,
  FiRefreshCw,
  FiRepeat,
} from 'react-icons/fi';
import { FaNetworkWired } from 'react-icons/fa';
import { CopyButton } from '../../common';

interface AppConfigEndpointsConfigurationProps {
  spec: any;
}

const E2endpointsTable = ({ spec }: { spec: any }) => {
  return (
    <TableContainer bg="white" borderRadius="lg" shadow="sm">
      <Table size="md">
        <Thead bg="purple.50">
          <Tr>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiHash} color="purple.500" boxSize={4} />
                <Text>ID</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiGlobe} color="purple.500" boxSize={4} />
                <Text>Remote IP</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiServer} color="purple.500" boxSize={4} />
                <Text>SCTP Port</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiSettings} color="purple.500" boxSize={4} />
                <Text>Feature Bitmap</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiCheckCircle} color="purple.500" boxSize={4} />
                <Text>Status</Text>
              </HStack>
            </Th>
          </Tr>
        </Thead>
        <Tbody>
          {(spec.E2endpoints || []).map((endpoint: any) => (
            <Tr key={endpoint.id} _hover={{ transform: 'translateY(-3px)' }} transition="all 0.3s ease-in-out">
              <Td fontWeight="bold" color="purple.700">
                {endpoint.id}
              </Td>
              <Td>
                <HStack spacing={2}>
                  <Text fontFamily="mono" fontSize="sm">
                    {endpoint.remoteIP}
                  </Text>
                  <CopyButton text={endpoint.remoteIP} label="Remote IP" />
                </HStack>
              </Td>
              <Td>{endpoint.remoteSCTPport}</Td>
              <Td>{endpoint.e2FeatureBitmap}</Td>
              <Td>
                <Badge
                  colorScheme={endpoint.enabled ? 'green' : 'red'}
                  variant="solid"
                  px={3}
                  py={1}
                  borderRadius="full"
                >
                  <HStack spacing={1}>
                    <Icon as={endpoint.enabled ? FiCheckCircle : FiXCircle} boxSize={4} />
                    <Text>{endpoint.enabled ? 'ENABLED' : 'DISABLED'}</Text>
                  </HStack>
                </Badge>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
};

const F1CendpointsTable = ({ spec }: { spec: any }) => {
  return (
    <TableContainer bg="white" borderRadius="lg" shadow="sm">
      <Table size="md">
        <Thead bg="purple.50">
          <Tr>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiHash} color="purple.500" boxSize={4} />
                <Text>ID</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiGlobe} color="purple.500" boxSize={4} />
                <Text>Remote IP</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FaNetworkWired} color="purple.500" boxSize={4} />
                <Text>Remote Port</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FaNetworkWired} color="purple.500" boxSize={4} />
                <Text>Local Port</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiSettings} color="purple.500" boxSize={4} />
                <Text>DSCP</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiCheckCircle} color="purple.500" boxSize={4} />
                <Text>Accelerated SCTP</Text>
              </HStack>
            </Th>
          </Tr>
        </Thead>
        <Tbody>
          {(spec.F1Cendpoints || []).map((endpoint: any) => (
            <Tr key={endpoint.id} _hover={{ transform: 'translateY(-3px)' }} transition="all 0.3s ease-in-out">
              <Td fontWeight="bold" color="purple.700">
                {endpoint.id}
              </Td>
              <Td>
                <HStack spacing={2}>
                  <Text fontFamily="mono" fontSize="sm">
                    {endpoint.remoteIP}
                  </Text>
                  <CopyButton text={endpoint.remoteIP} label="Remote IP" />
                </HStack>
              </Td>
              <Td>{endpoint.remoteSCTPport}</Td>
              <Td>{endpoint.localSCTPport}</Td>
              <Td>{endpoint.dscpValue}</Td>
              <Td>
                <Badge
                  colorScheme={endpoint.acceleratedSCTP === 'enabled' ? 'green' : 'red'}
                  variant="solid"
                  px={3}
                  py={1}
                  borderRadius="full"
                >
                  <HStack spacing={1}>
                    <Icon as={endpoint.acceleratedSCTP === 'enabled' ? FiCheckCircle : FiXCircle} boxSize={4} />
                    <Text>{endpoint.acceleratedSCTP === 'enabled' ? 'ENABLED' : 'DISABLED'}</Text>
                  </HStack>
                </Badge>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
};

const F1UendpointsTable = ({ spec }: { spec: any }) => {
  return (
    <TableContainer bg="white" borderRadius="lg" shadow="sm">
      <Table size="md">
        <Thead bg="purple.50">
          <Tr>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiHash} color="purple.500" boxSize={4} />
                <Text>ID</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiHash} color="purple.500" boxSize={4} />
                <Text>Interface ID</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiGlobe} color="purple.500" boxSize={4} />
                <Text>Local IP</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiGlobe} color="purple.500" boxSize={4} />
                <Text>Gateway IP</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiShare2} color="purple.500" boxSize={4} />
                <Text>Subnet</Text>
              </HStack>
            </Th>

            <Th>
              <HStack spacing={1}>
                <Icon as={FiRepeat} color="purple.500" boxSize={4} />
                <Text>Echo Interval</Text>
              </HStack>
            </Th>

            <Th>
              <HStack spacing={1}>
                <Icon as={FiRefreshCw} color="purple.500" boxSize={4} />
                <Text>Max Retries</Text>
              </HStack>
            </Th>

            <Th>
              <HStack spacing={1}>
                <Icon as={FiSettings} color="purple.500" boxSize={4} />
                <Text>DSCP</Text>
              </HStack>
            </Th>
          </Tr>
        </Thead>
        <Tbody>
          {(spec.F1Uendpoints || []).map((endpoint: any) => (
            <Tr key={endpoint.id} _hover={{ transform: 'translateY(-3px)' }} transition="all 0.3s ease-in-out">
              <Td fontWeight="bold" color="purple.700">
                {endpoint.id}
              </Td>
              <Td>{endpoint.F1UinterfaceId}</Td>
              <Td>
                <HStack spacing={2}>
                  <Text fontFamily="mono" fontSize="sm">
                    {endpoint.localIP}
                  </Text>
                  <CopyButton text={endpoint.localIP} label="Local IP" />
                </HStack>
              </Td>
              <Td>
                <HStack spacing={2}>
                  <Text fontFamily="mono" fontSize="sm">
                    {endpoint.gatewayIP}
                  </Text>
                  <CopyButton text={endpoint.gatewayIP} label="Gateway IP" />
                </HStack>
              </Td>
              <Td>/{endpoint.subNetMask}</Td>
              <Td>{endpoint.echoInterval}</Td>
              <Td>{endpoint.maxRetries}</Td>
              <Td>{endpoint.dscpValue}</Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
};

const FHCendpointsTable = ({ spec }: { spec: any }) => {
  return (
    <TableContainer bg="white" borderRadius="lg" shadow="sm">
      <Table size="md">
        <Thead bg="purple.50">
          <Tr>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiHash} color="purple.500" boxSize={4} />
                <Text>ID</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiHash} color="purple.500" boxSize={4} />
                <Text>Interface ID</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiGlobe} color="purple.500" boxSize={4} />
                <Text>Local IP</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiGlobe} color="purple.500" boxSize={4} />
                <Text>Gateway IP</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FaNetworkWired} color="purple.500" boxSize={4} />
                <Text>SCTP Port</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiShare2} color="purple.500" boxSize={4} />
                <Text>Subnet</Text>
              </HStack>
            </Th>
          </Tr>
        </Thead>
        <Tbody>
          {(spec.FHCendpoints || []).map((endpoint: any) => (
            <Tr key={endpoint.id} _hover={{ transform: 'translateY(-3px)' }} transition="all 0.3s ease-in-out">
              <Td fontWeight="bold" color="purple.700">
                {endpoint.id}
              </Td>
              <Td>{endpoint.FHinterfaceId}</Td>
              <Td>
                <HStack spacing={2}>
                  <Text fontFamily="mono" fontSize="sm">
                    {endpoint.localIP}
                  </Text>
                  <CopyButton text={endpoint.localIP} label="Local IP" />
                </HStack>
              </Td>
              <Td>
                <HStack spacing={2}>
                  <Text fontFamily="mono" fontSize="sm">
                    {endpoint.gatewayIP}
                  </Text>
                  <CopyButton text={endpoint.gatewayIP} label="Gateway IP" />
                </HStack>
              </Td>
              <Td>{endpoint.localSCTPport}</Td>
              <Td>/{endpoint.subNetMask}</Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
};

const FHUendpointsTable = ({ spec }: { spec: any }) => {
  return (
    <TableContainer bg="white" borderRadius="lg" shadow="sm">
      <Table size="md">
        <Thead bg="purple.50">
          <Tr>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiHash} color="purple.500" boxSize={4} />
                <Text>ID</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiHash} color="purple.500" boxSize={4} />
                <Text>Interface ID</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiGlobe} color="purple.500" boxSize={4} />
                <Text>Local IP</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FiGlobe} color="purple.500" boxSize={4} />
                <Text>Gateway IP</Text>
              </HStack>
            </Th>
            <Th>
              <HStack spacing={1}>
                <Icon as={FaNetworkWired} color="purple.500" boxSize={4} />
                <Text>UDP Port Range</Text>
              </HStack>
            </Th>

            <Th>
              <HStack spacing={1}>
                <Icon as={FiShare2} color="purple.500" boxSize={4} />
                <Text>Subnet</Text>
              </HStack>
            </Th>
          </Tr>
        </Thead>
        <Tbody>
          {(spec.FHUendpoints || []).map((endpoint: any) => (
            <Tr key={endpoint.id} _hover={{ transform: 'translateY(-3px)' }} transition="all 0.3s ease-in-out">
              <Td fontWeight="bold" color="purple.700">
                {endpoint.id}
              </Td>
              <Td>{endpoint.FHinterfaceId}</Td>
              <Td>
                <HStack spacing={2}>
                  <Text fontFamily="mono" fontSize="sm">
                    {endpoint.localIP}
                  </Text>
                  <CopyButton text={endpoint.localIP} label="Local IP" />
                </HStack>
              </Td>
              <Td>
                <HStack spacing={2}>
                  <Text fontFamily="mono" fontSize="sm">
                    {endpoint.gatewayIP}
                  </Text>
                  <CopyButton text={endpoint.gatewayIP} label="Gateway IP" />
                </HStack>
              </Td>
              <Td fontFamily="mono" fontSize="sm">
                {endpoint.localUDPPortRange?.begin}-{endpoint.localUDPPortRange?.end}
              </Td>
              <Td>/{endpoint.subNetMask}</Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
};

export const EndpointsConfiguration: React.FC<AppConfigEndpointsConfigurationProps> = ({ spec }) => {
  return (
    <Card borderWidth="1px" borderRadius="lg" overflow="hidden" variant="outline" border="none">
      <CardBody p={4}>
        <Tabs variant="soft-rounded" colorScheme="purple" size="md">
          <TabList mb={4} bg="white" p={1} borderRadius="lg" flexWrap="wrap" gap={2}>
            <Tab
              fontWeight="bold"
              shadow="md"
              _selected={{ bg: 'purple.200' }}
              _hover={{ bg: 'purple.200', transform: 'translateY(-5px)' }}
              transition="all 0.3s ease-in-out"
            >
              <HStack spacing={2}>
                <Icon as={FiTerminal} />
                <Text>E2</Text>
                <Badge size="sm">{spec.E2endpoints?.length || 0}</Badge>
              </HStack>
            </Tab>
            <Tab
              fontWeight="bold"
              shadow="md"
              _selected={{ bg: 'purple.200' }}
              _hover={{ bg: 'purple.200', transform: 'translateY(-5px)' }}
              transition="all 0.3s ease-in-out"
            >
              <HStack spacing={2}>
                <Icon as={FiTerminal} />
                <Text>F1C</Text>
                <Badge size="sm">{spec.F1Cendpoints?.length || 0}</Badge>
              </HStack>
            </Tab>
            <Tab
              fontWeight="bold"
              shadow="md"
              _selected={{ bg: 'purple.200' }}
              _hover={{ bg: 'purple.200', transform: 'translateY(-5px)' }}
              transition="all 0.3s ease-in-out"
            >
              <HStack spacing={2}>
                <Icon as={FiTerminal} />
                <Text>F1U</Text>
                <Badge size="sm">{spec.F1Uendpoints?.length || 0}</Badge>
              </HStack>
            </Tab>
            <Tab
              fontWeight="bold"
              shadow="md"
              _selected={{ bg: 'purple.200' }}
              _hover={{ bg: 'purple.200', transform: 'translateY(-5px)' }}
              transition="all 0.3s ease-in-out"
            >
              <HStack spacing={2}>
                <Icon as={FiTerminal} />
                <Text>FHC</Text>
                <Badge size="sm">{spec.FHCendpoints?.length || 0}</Badge>
              </HStack>
            </Tab>
            <Tab
              fontWeight="bold"
              shadow="md"
              _selected={{ bg: 'purple.200' }}
              _hover={{ bg: 'purple.200', transform: 'translateY(-5px)' }}
              transition="all 0.3s ease-in-out"
            >
              <HStack spacing={2}>
                <Icon as={FiTerminal} />
                <Text>FHU</Text>
                <Badge size="sm">{spec.FHUendpoints?.length || 0}</Badge>
              </HStack>
            </Tab>
          </TabList>
          <TabPanels>
            {/* E2 Endpoints */}
            <TabPanel p={4} mx={4} mt={4}>
              <E2endpointsTable spec={spec} />
            </TabPanel>

            {/* F1C Endpoints */}
            <TabPanel p={4} mx={4} mt={4}>
              <F1CendpointsTable spec={spec} />
            </TabPanel>

            {/* F1U Endpoints */}
            <TabPanel p={4} mx={4} mt={4}>
              <F1UendpointsTable spec={spec} />
            </TabPanel>

            {/* FHC Endpoints */}
            <TabPanel p={4} mx={4} mt={4}>
              <FHCendpointsTable spec={spec} />
            </TabPanel>

            {/* FHU Endpoints */}
            <TabPanel p={4} mx={4} mt={4}>
              <FHUendpointsTable spec={spec} />
            </TabPanel>
          </TabPanels>
        </Tabs>
      </CardBody>
    </Card>
  );
};
