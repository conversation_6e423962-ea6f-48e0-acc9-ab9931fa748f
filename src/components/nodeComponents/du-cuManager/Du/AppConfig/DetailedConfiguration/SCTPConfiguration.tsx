import { HStack, Icon, Text, Card, CardHeader, CardBody, Heading, SimpleGrid, VStack } from '@chakra-ui/react';
import { FiGlobe, FiSettings, FiShare2, FiRefreshCw, FiActivity, FiDownload, FiUpload } from 'react-icons/fi';

interface AppConfigSCTPConfigurationProps {
  spec: any;
}

export const SCTPConfiguration: React.FC<AppConfigSCTPConfigurationProps> = ({ spec }) => {
  return (
    <Card borderWidth="1px" borderRadius="lg" overflow="hidden" variant="outline" border="none">
      <CardBody p={4}>
        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
          <Card
            variant="outline"
            bg="white"
            _hover={{ transform: 'translateY(-3px)' }}
            transition="all 0.3s ease-in-out"
          >
            <CardHeader pb={2} bg="orange.50">
              <Heading size="sm" color="orange.700">
                <Icon as={FiSettings} mr={2} />
                Stream Configuration
              </Heading>
            </CardHeader>
            <CardBody>
              <VStack align="start" spacing={3}>
                <HStack>
                  <Icon as={FiDownload} color="orange.500" />
                  <Text fontSize="sm" fontWeight="600">
                    Max Inbound Streams:
                  </Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {spec.SCTP?.maxInboundStreams}
                  </Text>
                </HStack>
                <HStack>
                  <Icon as={FiUpload} color="orange.500" />
                  <Text fontSize="sm" fontWeight="600">
                    Outbound Streams:
                  </Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {spec.SCTP?.numOutboundStreams}
                  </Text>
                </HStack>
                <HStack>
                  <Icon as={FiRefreshCw} color="orange.500" />
                  <Text fontSize="sm" fontWeight="600">
                    Max Init Attempts:
                  </Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {spec.SCTP?.maxInitAttempts}
                  </Text>
                </HStack>
              </VStack>
            </CardBody>
          </Card>

          <Card
            variant="outline"
            bg="white"
            _hover={{ transform: 'translateY(-3px)' }}
            transition="all 0.3s ease-in-out"
          >
            <CardHeader pb={2} bg="orange.50">
              <Heading size="sm" color="orange.700">
                <Icon as={FiActivity} mr={2} />
                Timing & Reliability
              </Heading>
            </CardHeader>
            <CardBody>
              <VStack align="start" spacing={3}>
                <HStack>
                  <Icon as={FiActivity} color="orange.500" />
                  <Text fontSize="sm" fontWeight="600">
                    Heartbeat Interval:
                  </Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {spec.SCTP?.heartBeatIntervalInMs}ms
                  </Text>
                </HStack>
                <HStack>
                  <Icon as={FiRefreshCw} color="orange.500" />
                  <Text fontSize="sm" fontWeight="600">
                    Max Path Retx:
                  </Text>
                  <Text fontSize="sm" fontWeight="bold">
                    {spec.SCTP?.maxPathRetx}
                  </Text>
                </HStack>
              </VStack>
            </CardBody>
          </Card>
        </SimpleGrid>
      </CardBody>
    </Card>
  );
};
