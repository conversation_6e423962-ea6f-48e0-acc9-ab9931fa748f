import React, { useMemo } from 'react';
import {
  Box,
  Card,
  CardBody,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  VStack,
  Heading,
  Wrap,
  WrapItem,
  Tag,
  TagLabel,
  Icon,
  Text,
  HStack,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import { FiAlertTriangle, FiMessageSquare, FiCpu, FiGitMerge, FiAlertOctagon, FiActivity } from 'react-icons/fi';

interface LogEntry {
  id: number;
  logLevel: 'FATAL' | 'ERR' | 'TRC' | string;
  moduleId: string;
}

interface GroupedLogLevelTabsProps {
  duLogs?: LogEntry[];
  ngpLogs?: LogEntry[];
}

const getLogLevelStyle = (logLevel: string) => {
  switch (logLevel?.toUpperCase()) {
    case 'FATAL':
      return { colorScheme: 'purple', icon: FiAlertOctagon, title: 'Fatal Errors' };
    case 'ERR':
      return { colorScheme: 'red', icon: FiAlertTriangle, title: 'Errors' };
    case 'TRC':
      return { colorScheme: 'blue', icon: FiActivity, title: 'Trace Logs' };
    default:
      return { colorScheme: 'gray', icon: FiMessageSquare, title: 'Other Logs' };
  }
};

const LOG_LEVEL_ORDER = ['FATAL', 'ERR', 'TRC'];

const LogLevelGroup: React.FC<{ logs: LogEntry[] }> = ({ logs }) => {
  const groupedAndSortedLogs = useMemo(() => {
    if (!logs) return {};

    const grouped = logs.reduce((acc, log) => {
      if (!acc[log.logLevel]) {
        acc[log.logLevel] = [];
      }
      acc[log.logLevel].push(log.moduleId);
      return acc;
    }, {} as Record<string, string[]>);

    Object.keys(grouped).forEach((level) => {
      grouped[level].sort((a, b) => a.localeCompare(b));
    });

    return grouped;
  }, [logs]);

  const orderedLevels = LOG_LEVEL_ORDER.filter((level) => groupedAndSortedLogs[level]);

  return (
    <VStack spacing={6} align="stretch">
      {orderedLevels.map((level) => {
        const { colorScheme, icon, title } = getLogLevelStyle(level);
        const modules = groupedAndSortedLogs[level];
        return (
          <Box key={level}>
            <HStack mb={3}>
              <Icon as={icon} color={`${colorScheme}.500`} boxSize={6} />
              <Heading size="sm" color="gray.700">
                {title} ({modules.length})
              </Heading>
            </HStack>
            <Wrap spacing={2} ml="4">
              {modules.map((moduleId) => (
                <WrapItem key={moduleId} _hover={{ transform: 'translateY(-2px)' }} transition="all 0.2s ease">
                  <Tag size="lg" colorScheme={colorScheme} variant="solid" borderRadius="full">
                    <TagLabel>{moduleId}</TagLabel>
                  </Tag>
                </WrapItem>
              ))}
            </Wrap>
          </Box>
        );
      })}
    </VStack>
  );
};

export const LogLevels: React.FC<{ spec: any }> = ({ spec }) => {
  const duLogs = spec.duLog || [];
  const ngpLogs = spec.ngpLog || [];
  if (duLogs.length === 0 && ngpLogs.length === 0) {
    return (
      <Alert status="info" borderRadius="lg" m={4}>
        <AlertIcon />
        <Text>No log level data available.</Text>
      </Alert>
    );
  }

  return (
    <Card borderWidth="1px" borderRadius="lg" overflow="hidden" variant="outline" border="none">
      <CardBody p={4}>
        <Tabs variant="soft-rounded" colorScheme="cyan" align="start">
          <TabList gap={2}>
            <Tab
              shadow="lg"
              _hover={{ shadow: 'lg', bg: 'cyan.100', transform: 'translateY(-3px)' }}
              transition="all 0.3s ease"
            >
              <HStack>
                <Icon as={FiCpu} boxSize={6} />
                <Text>DU Log Levels</Text>
              </HStack>
            </Tab>
            <Tab
              shadow="lg"
              _hover={{ shadow: 'lg', bg: 'cyan.100', transform: 'translateY(-3px)' }}
              transition="all 0.3s ease"
            >
              <HStack>
                <Icon as={FiGitMerge} boxSize={6} />
                <Text>NGP Log Levels</Text>
              </HStack>
            </Tab>
          </TabList>
          <TabPanels mt={4}>
            <TabPanel p={4} mx={4} mt={4}>
              <LogLevelGroup logs={duLogs} />
            </TabPanel>
            <TabPanel p={4} mx={4} mt={4}>
              <LogLevelGroup logs={ngpLogs} />
            </TabPanel>
          </TabPanels>
        </Tabs>
      </CardBody>
    </Card>
  );
};
