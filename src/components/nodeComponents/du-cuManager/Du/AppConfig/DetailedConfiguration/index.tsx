import {
  Accordion,
  Card,
  CardBody,
  CardHeader,
  HStack,
  Icon,
  Heading,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Box,
  Text,
  Badge,
  Button,
  Flex,
  Tab,
  VStack,
  Divider,
} from '@chakra-ui/react';
import { useState } from 'react';
import {
  FiSettings,
  FiMap,
  FiRadio,
  FiGlobe,
  FiAlertTriangle,
  FiFileText,
  FiChevronUp,
  FiHardDrive,
  FiDownload,
  FiUpload,
  FiActivity,
} from 'react-icons/fi';
import { F1InterfaceMapping } from './F1Mapping';
import { RUConfiguration } from './RUConfiguration';
import { EndpointsConfiguration } from './EndpointsConfiguration';
import { SCTPConfiguration } from './SCTPConfiguration';
import { AlarmThresholds } from './AlarmThresholds';
import { LogLevels } from './LogLevels';

// Centralized accordion configuration
const accordionConfig = [
  {
    id: 'f1mapping',
    title: 'F1 Mapping',
    icon: FiMap,
    color: 'blue',
    component: F1InterfaceMapping,
    crucialDetails: (spec: any) => {
      return spec.F1Mapping.map((mapping: any) => (
        <VStack key={mapping.id} borderWidth="2px" borderRadius="lg" borderColor={`orange.200`} p={4}>
          <HStack spacing={2}>
            <Text>Mapping {mapping.id}</Text>
          </HStack>
          {mapping.cellMap.map((cell: any) => (
            <HStack key={cell.id}>
              <Badge colorScheme="orange" variant="subtle">
                MCC: {cell.NRCgi.pLMNId.MCC}
              </Badge>
              <Badge colorScheme="orange" variant="subtle">
                MNC: {cell.NRCgi.pLMNId.MNC}
              </Badge>
            </HStack>
          ))}
        </VStack>
      ));
    },
  },
  {
    id: 'ru',
    title: 'Radio Unit Configuration',
    icon: FiRadio,
    color: 'green',
    component: RUConfiguration,
    crucialDetails: (spec: any) => {
      return spec.RU.map((ru: any) => (
        <VStack key={ru.id} align="stretch" borderWidth="2px" borderRadius="lg" borderColor={`orange.200`} p={4}>
          <Badge colorScheme="orange" variant="subtle">
            IP: {ru.ruIP}
          </Badge>

          <HStack>
            <Icon as={FiDownload} boxSize={5} color="orange.500" />
            <Text mr={2}>DL: {ru.nfapiDLTransportDelay}ms</Text>
            <Icon as={FiUpload} boxSize={5} color="orange.500" />
            <Text>UL: {ru.nfapiULTransportDelay}ms</Text>
          </HStack>
        </VStack>
      ));
    },
  },
  {
    id: 'endpoints',
    title: 'Endpoints Configuration',
    icon: FiGlobe,
    color: 'purple',
    component: EndpointsConfiguration,
    crucialDetails: (spec: any) => {
      const total =
        (spec.E2endpoints?.length || 0) +
        (spec.F1Cendpoints?.length || 0) +
        (spec.F1Uendpoints?.length || 0) +
        (spec.FHCendpoints?.length || 0) +
        (spec.FHUendpoints?.length || 0);
      return (
        <Badge colorScheme="orange" variant="subtle" px={2.5} py={1} borderRadius="md" fontSize="sm">
          {total} total
        </Badge>
      );
    },
  },
  {
    id: 'sctp',
    title: 'SCTP Configuration',
    icon: FiGlobe,
    color: 'orange',
    component: SCTPConfiguration,
    crucialDetails: (spec: any) => {
      const sctpConfig = spec.SCTP || {};
      const inbound = sctpConfig.maxInboundStreams || 0;
      const outbound = sctpConfig.numOutboundStreams || 0;
      const heartbeat = sctpConfig.heartBeatIntervalInMs || 0;

      return (
        <HStack spacing={4}>
          <Badge colorScheme="orange" variant="subtle">
            <HStack spacing={2}>
              <Icon as={FiDownload} boxSize={5} color="orange.500" />
              <Text mr={2}>In: {inbound}</Text>
            </HStack>
          </Badge>
          <Badge colorScheme="orange" variant="subtle">
            <HStack spacing={2}>
              <Icon as={FiUpload} boxSize={5} color="orange.500" />
              <Text mr={2}>Out: {outbound}</Text>
            </HStack>
          </Badge>
          <Badge colorScheme="orange" variant="subtle">
            <HStack spacing={2}>
              <Icon as={FiActivity} boxSize={5} color="orange.500" />
              <Text mr={2}>Heartbeat: {heartbeat}ms</Text>
            </HStack>
          </Badge>
        </HStack>
      );
    },
  },
  {
    id: 'alarms',
    title: 'Alarm Thresholds',
    icon: FiAlertTriangle,
    color: 'red',
    component: AlarmThresholds,
    crucialDetails: (spec: any) => {
      const alarms = spec.alarmThresholds || [];

      if (alarms.length === 0) {
        return (
          <Badge colorScheme="green" variant="subtle">
            ✓ No alarms
          </Badge>
        );
      }

      // Count alarms by severity/level
      const criticalAlarms = alarms.filter((alarm: any) => (alarm?.thresholdLevel || 0) >= 80).length;

      const warningAlarms = alarms.filter(
        (alarm: any) => (alarm?.thresholdLevel || 0) >= 50 && (alarm?.thresholdLevel || 0) < 80
      ).length;

      const lowAlarms = alarms.filter((alarm: any) => (alarm?.thresholdLevel || 0) < 50).length;

      return (
        <HStack spacing={2}>
          <Badge colorScheme="blue" variant="subtle">
            {alarms.length} Total
          </Badge>
          {criticalAlarms > 0 && (
            <Badge colorScheme="red" variant="solid">
              {criticalAlarms} Critical
            </Badge>
          )}
          {warningAlarms > 0 && (
            <Badge colorScheme="orange" variant="solid">
              {warningAlarms} Warning
            </Badge>
          )}
          {lowAlarms > 0 && (
            <Badge colorScheme="yellow" variant="subtle">
              {lowAlarms} Low
            </Badge>
          )}
        </HStack>
      );
    },
  },
  {
    id: 'logs',
    title: 'Log Levels Configuration',
    icon: FiFileText,
    color: 'cyan',
    component: LogLevels,
    crucialDetails: (spec: any) => {
      const duLogs = spec.duLog || [];
      const ngpLogs = spec.ngpLog || [];
      const LOG_LEVEL_ORDER = ['FATAL', 'ERR', 'TRC'];

      const duLogsFiltered = duLogs.filter((log: any) => LOG_LEVEL_ORDER.includes(log.logLevel));
      const ngpLogsFiltered = ngpLogs.filter((log: any) => LOG_LEVEL_ORDER.includes(log.logLevel));

      const duLogsCount = duLogsFiltered.length;
      const ngpLogsCount = ngpLogsFiltered.length;

      // Count different log levels for DU
      const duFatalCount = duLogs.filter((log: any) => log.logLevel === 'FATAL').length;
      const duErrorCount = duLogs.filter((log: any) => log.logLevel === 'ERR').length;
      const duTraceCount = duLogs.filter((log: any) => log.logLevel === 'TRC').length;

      // Count different log levels for NGP
      const ngpFatalCount = ngpLogs.filter((log: any) => log.logLevel === 'FATAL').length;
      const ngpErrorCount = ngpLogs.filter((log: any) => log.logLevel === 'ERR').length;
      const ngpTraceCount = ngpLogs.filter((log: any) => log.logLevel === 'TRC').length;

      if (duLogsCount === 0 && ngpLogsCount === 0) {
        return (
          <Badge colorScheme="green" variant="subtle">
            ✓ Default
          </Badge>
        );
      }

      return (
        <HStack spacing={4} align="start">
          {' '}
          {/* Changed from justify="space-between" to spacing={4} and added align="start" */}
          {duLogsCount > 0 && (
            <VStack spacing={1} align="start">
              {' '}
              {/* Reduced spacing from 2 to 1 */}
              <Badge colorScheme="blue" variant="subtle" fontSize="xs">
                DU ({duLogsCount})
              </Badge>{' '}
              {/* Added HStack wrapper for badges */}
              {duFatalCount > 0 && (
                <Badge colorScheme="purple" variant="solid" fontSize="xs">
                  FATAL: {duFatalCount}
                </Badge>
              )}
              {duErrorCount > 0 && (
                <Badge colorScheme="red" variant="solid" fontSize="xs">
                  ERROR: {duErrorCount}
                </Badge>
              )}
              {duTraceCount > 0 && (
                <Badge colorScheme="cyan" variant="subtle" fontSize="xs">
                  TRACE: {duTraceCount}
                </Badge>
              )}
            </VStack>
          )}
          {ngpLogsCount > 0 && (
            <VStack spacing={1} align="start">
              {' '}
              {/* Reduced spacing from 2 to 1 */}
              <Badge colorScheme="green" variant="subtle" fontSize="xs">
                NGP ({ngpLogsCount})
              </Badge>
              <HStack spacing={1} wrap="wrap">
                {' '}
                {/* Added HStack wrapper for badges */}
                {ngpFatalCount > 0 && (
                  <Badge colorScheme="purple" variant="solid" fontSize="xs">
                    FATAL: {ngpFatalCount}
                  </Badge>
                )}
                {ngpErrorCount > 0 && (
                  <Badge colorScheme="red" variant="solid" fontSize="xs">
                    ERROR: {ngpErrorCount}
                  </Badge>
                )}
                {ngpTraceCount > 0 && (
                  <Badge colorScheme="cyan" variant="subtle" fontSize="xs">
                    TRACE: {ngpTraceCount}
                  </Badge>
                )}
              </HStack>
            </VStack>
          )}
        </HStack>
      );
    },
  },
];

export const DetailedConfiguration = ({ spec, colorScheme }: { spec: any; colorScheme: string }) => {
  const [expandedItems, setExpandedItems] = useState<number[]>([]);
  const hasExpandedItems = expandedItems.length > 0;
  const collapseAll = () => {
    setExpandedItems([]);
  };

  return (
    <Card variant="outline" shadow="lg" borderColor={`${colorScheme}.200`}>
      <CardHeader py={6}>
        <Flex justifyContent="space-between" alignItems="center">
          <HStack spacing={3}>
            <Box
              bg={`${colorScheme}.500`}
              bgGradient={`linear(135deg, ${colorScheme}.400, ${colorScheme}.600)`}
              borderRadius="full"
              p={2}
              shadow="md"
            >
              <Icon as={FiSettings} boxSize={7} color="white" />
            </Box>
            <Heading size="md" color={`${colorScheme}.700`}>
              Application Detailed Configuration
            </Heading>
          </HStack>
          <Button
            size="md"
            colorScheme={colorScheme}
            variant="outline"
            onClick={collapseAll}
            isDisabled={!hasExpandedItems}
            mr="4"
            shadow="lg"
            _hover={{
              shadow: 'md',
              transform: 'translateY(-5px)',
            }}
            _active={{
              shadow: 'md',
              transform: 'translateY(-5px)',
            }}
            _focus={{
              shadow: 'md',
              transform: 'translateY(-5px)',
            }}
            transition="all 0.3s ease-in-out"
          >
            <Icon as={FiChevronUp} boxSize={6} />
            Collapse All
          </Button>
        </Flex>
      </CardHeader>
      <CardBody pt={0}>
        <Accordion
          allowMultiple
          mx="4"
          index={expandedItems}
          onChange={(expandedIndex) => setExpandedItems(expandedIndex as number[])}
        >
          {accordionConfig.map((config, index) => (
            <AccordionItem
              key={config.id}
              mb={3}
              shadow="md"
              _hover={{
                shadow: 'md',
                transform: 'translateY(-5px)',
              }}
              transition="all 0.3s ease-in-out"
              borderColor={`${colorScheme}.100`}
              borderWidth="1px"
              borderRadius="lg"
            >
              <h2>
                <AccordionButton
                  py={4}
                  px={5}
                  shadow="md"
                  borderRadius="lg"
                  _hover={{ bg: `${colorScheme}.100` }}
                  _expanded={{ bg: `${colorScheme}.100` }}
                >
                  <Box as="span" flex="1" textAlign="left" fontWeight="bold" fontSize="lg">
                    <HStack spacing={3}>
                      <Box
                        bg={`${colorScheme}.500`}
                        bgGradient={`linear(135deg, ${colorScheme}.400, ${colorScheme}.600)`}
                        borderRadius="full"
                        p={2}
                        shadow="md"
                      >
                        <Icon as={config.icon} boxSize={5} color="white" />
                      </Box>
                      <Text>{config.title}</Text>

                      {config.crucialDetails(spec)}
                    </HStack>
                  </Box>
                  <AccordionIcon />
                </AccordionButton>
              </h2>
              <AccordionPanel>
                <config.component spec={spec} />
              </AccordionPanel>
            </AccordionItem>
          ))}
        </Accordion>
      </CardBody>
    </Card>
  );
};
