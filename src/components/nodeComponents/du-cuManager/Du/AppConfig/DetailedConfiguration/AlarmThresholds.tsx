import { HStack, VStack, Icon, Text, Badge, SimpleGrid, Card, CardBody } from '@chakra-ui/react';
import { FiAlertTriangle, FiBar<PERSON>hart } from 'react-icons/fi';

interface AppConfigAlarmThresholdsProps {
  spec: any;
}

export const AlarmThresholds: React.FC<AppConfigAlarmThresholdsProps> = ({ spec }) => {
  return (
    <Card borderWidth="1px" borderRadius="lg" overflow="hidden" variant="outline" border="none">
      <CardBody p={4}>
        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
          {(spec.alarmThresholds || []).map((alarm: any) => (
            <Card
              key={alarm.id}
              variant="outline"
              bg="white"
              _hover={{ transform: 'translateY(-3px)' }}
              transition="all 0.3s ease-in-out"
            >
              <CardBody p={4}>
                <VStack align="start" spacing={3}>
                  <HStack justify="space-between" w="full">
                    <HStack>
                      <Icon as={FiAlertTriangle} color="red.500" />
                      <Text fontSize="sm" fontWeight="bold" color="red.700">
                        ID: {alarm.id}
                      </Text>
                    </HStack>
                    <Badge colorScheme="red" variant="solid">
                      Level: {alarm.thresholdLevel}
                    </Badge>
                  </HStack>
                  <Text fontSize="xs" fontWeight="600" color="gray.700">
                    {alarm.alarmName.replace(/_/g, ' ')}
                  </Text>
                  <HStack>
                    <Icon as={FiBarChart} color="red.500" boxSize={3} />
                    <Text fontSize="xs">Threshold Count: {alarm.thresholdCount}</Text>
                  </HStack>
                </VStack>
              </CardBody>
            </Card>
          ))}
        </SimpleGrid>
      </CardBody>
    </Card>
  );
};
