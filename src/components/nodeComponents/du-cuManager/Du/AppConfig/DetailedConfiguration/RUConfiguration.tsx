import {
  Alert,
  AlertIcon,
  As,
  Badge,
  Card,
  CardBody,
  HStack,
  Icon,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  Text,
  VStack,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Table,
  TableContainer,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
} from '@chakra-ui/react';
import {
  FiClock,
  FiCheckCircle,
  FiDownload,
  FiGlobe,
  FiHash,
  FiRadio,
  FiRss,
  FiTarget,
  FiUpload,
  FiXCircle,
  FiZap,
} from 'react-icons/fi';
import { CopyButton } from '../../common';

interface CarrierCell {
  id: number;
  NRDUCellId: number;
  carrierId: number;
  rfPwr: number;
}

interface RU {
  id: number;
  ruIP: string;
  trpId: number;
  nfapiDLTransportDelay: number;
  nfapiULTransportDelay: number;
  FHCendpointId: number;
  FHUendpointId: number;
  timingWindow: number;
  frontHaulPDTestMode: number | boolean;
  carrierCellConfig: CarrierCell[];
}

const StatBox = ({
  icon,
  label,
  value,
  colorScheme,
  copyable = false,
}: {
  icon: As;
  label: string;
  value: string | number;
  colorScheme: string;
  copyable?: boolean;
}) => (
  <Stat p={3} bg={`${colorScheme}.50`} borderRadius="md" textAlign="center">
    <StatLabel fontSize="sm" color={`${colorScheme}.600`} fontWeight="medium">
      <HStack justify="center" spacing={1.5}>
        <Icon as={icon} boxSize={4} />
        <Text>{label}</Text>
      </HStack>
    </StatLabel>
    <HStack justify="center" spacing={2} mt={1}>
      <StatNumber fontSize="2xl" color={`${colorScheme}.800`} fontFamily="mono">
        {value}
      </StatNumber>
      {copyable && <CopyButton text={String(value)} label={label} />}
    </HStack>
  </Stat>
);

const InfoRow = ({
  icon,
  label,
  value,
  colorScheme = 'gray',
}: {
  icon: As;
  label: string;
  value: string | number;
  colorScheme?: string;
}) => (
  <HStack justify="space-between" w="full">
    <HStack spacing={3}>
      <Icon as={icon} color="gray.400" />
      <Text fontSize="sm" fontWeight="600" color="gray.600">
        {label}
      </Text>
    </HStack>
    <Badge variant="subtle" colorScheme={colorScheme} px={2.5} py={1} borderRadius="md" fontSize="sm">
      {value}
    </Badge>
  </HStack>
);

const RUDetails = ({ ru }: { ru: RU }) => (
  <VStack spacing={4} align="stretch">
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <StatBox icon={FiGlobe} label="RU IP" value={ru.ruIP} colorScheme="cyan" copyable />
      <StatBox icon={FiClock} label="Timing Window" value={ru.timingWindow} colorScheme="green" />

      <StatBox icon={FiDownload} label="DL Delay" value={`${ru.nfapiDLTransportDelay}ms`} colorScheme="blue" />
      <StatBox icon={FiUpload} label="UL Delay" value={`${ru.nfapiULTransportDelay}ms`} colorScheme="green" />
    </SimpleGrid>

    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
      <VStack spacing={3} align="stretch" p={4} bg="white" borderRadius="lg" borderWidth="1px">
        <InfoRow icon={FiHash} label="FHC Endpoint ID" value={ru.FHCendpointId} colorScheme="blue" />
        <InfoRow icon={FiHash} label="FHU Endpoint ID" value={ru.FHUendpointId} colorScheme="green" />
        <InfoRow icon={FiTarget} label="TRP ID" value={ru.trpId} colorScheme="green" />
      </VStack>

      <VStack spacing={2} align="stretch" p={3} borderRadius="lg" borderWidth="1px">
        <HStack px={1}>
          <Icon as={FiRss} color="orange.500" />
          <Text fontWeight="bold" fontSize="md" color="gray.600">
            Carrier Configuration
          </Text>
        </HStack>
        <TableContainer>
          <Table variant="simple" size="sm">
            <Thead>
              <Tr>
                <Th>
                  <HStack spacing={1}>
                    <Icon as={FiHash} boxSize={3.5} color="orange.500" />
                    <Text>ID</Text>
                  </HStack>
                </Th>
                <Th>
                  <HStack spacing={1}>
                    <Icon as={FiHash} boxSize={3.5} color="orange.500" />
                    <Text>Carrier ID</Text>
                  </HStack>
                </Th>
                <Th>
                  <HStack spacing={1}>
                    <Icon as={FiRss} boxSize={3.5} color="orange.500" />
                    <Text>Cell ID</Text>
                  </HStack>
                </Th>
                <Th isNumeric>
                  <HStack spacing={1} justify="flex-end">
                    <Icon as={FiZap} boxSize={3.5} color="orange.500" />
                    <Text>RF Power</Text>
                  </HStack>
                </Th>
              </Tr>
            </Thead>
            <Tbody>
              {(ru.carrierCellConfig || []).map((carrier) => (
                <Tr key={carrier.id}>
                  <Td>
                    <Badge variant="outline" colorScheme="orange">
                      {carrier.id}
                    </Badge>
                  </Td>
                  <Td>
                    <Badge variant="outline" colorScheme="orange">
                      {carrier.carrierId}
                    </Badge>
                  </Td>
                  <Td fontWeight="medium">{carrier.NRDUCellId}</Td>
                  <Td isNumeric>
                    <HStack justify="flex-end" spacing={1.5}>
                      <Text fontWeight="bold" color="red.500">
                        {carrier.rfPwr}
                      </Text>
                    </HStack>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </TableContainer>
      </VStack>
    </SimpleGrid>
  </VStack>
);

// Content-only component (no accordion wrapper)
export const RUConfiguration = ({ spec }: { spec: any }) => {
  const data = spec.RU || [];
  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <Alert status="info" borderRadius="lg" m={4}>
        <AlertIcon />
        <Text>No RU configuration data available.</Text>
      </Alert>
    );
  }

  return (
    <Card borderWidth="1px" borderRadius="lg" overflow="hidden" variant="outline" border="none">
      <CardBody px={2} py={0}>
        <Tabs variant="soft-rounded" colorScheme="green" align="start">
          <TabList mt={4} mx={4}>
            {data.map((ru) => (
              <Tab
                key={ru.id}
                shadow="md"
                _focus={{ boxShadow: 'none' }}
                _hover={{ bg: 'green.200', transform: 'translateY(-2px)' }}
                transition="all 0.2s ease"
              >
                <VStack>
                  <HStack>
                    <Icon as={FiRadio} boxSize={6} color="green.500" />
                    <Text fontWeight="outline" fontSize="md">
                      RU {ru.id}
                    </Text>
                  </HStack>
                  <HStack>
                    <Text>Test Mode</Text>

                    <Badge
                      colorScheme={ru.frontHaulPDTestMode ? 'green' : 'red'}
                      variant="solid"
                      px={3}
                      py={1}
                      borderRadius="full"
                    >
                      <HStack spacing={1}>
                        <Icon as={ru.frontHaulPDTestMode ? FiCheckCircle : FiXCircle} boxSize={4} />
                        <Text>{ru.frontHaulPDTestMode ? 'ON' : 'OFF'}</Text>
                      </HStack>
                    </Badge>
                  </HStack>
                </VStack>
              </Tab>
            ))}
          </TabList>
          <TabPanels mt={4} mx={4}>
            {data.map((ru) => (
              <TabPanel key={ru.id}>
                <RUDetails ru={ru} />
              </TabPanel>
            ))}
          </TabPanels>
        </Tabs>
      </CardBody>
    </Card>
  );
};
