import {
  Card,
  CardBody,
  CardHeader,
  HStack,
  Heading,
  Icon,
  Text,
  VStack,
  Box,
  Badge,
  SimpleGrid,
  Divider,
  Stat,
  StatNumber,
  StatLabel,
  useColorModeValue,
  Flex,
  Collapse,
} from '@chakra-ui/react';
import { useState } from 'react';
import {
  FiActivity,
  FiCheckCircle,
  FiHash,
  FiSettings,
  FiBarChart,
  FiXCircle,
  FiLink,
  FiToggleLeft,
  FiLayers,
  FiSliders,
  FiCode,
  FiPower,
  FiShare2,
  FiTrendingUp,
  FiWifiOff,
  FiFileText,
  FiServer,
} from 'react-icons/fi';
import { startCase } from 'lodash';
import CommonConfigurationStatus from '../CommonConfigurationStatus';
import CommonMetricsGrid from '../CommonMetricsComponent';

const StatusComponent = ({
  label,
  value,
  icon,
  colorScheme,
}: {
  label: string;
  value: boolean | string;
  icon: any;
  colorScheme: string;
}) => {
  const isEnabled = typeof value === 'boolean' ? value : value === 'enabled';
  const statusColorScheme = isEnabled ? 'green' : 'red';

  return (
    <Flex
      justify="space-between"
      align="center"
      p={3}
      borderRadius="md"
      borderWidth={1}
      borderColor={`${colorScheme}.100`}
      _hover={{
        bg: `${statusColorScheme}.100`,
        transform: 'translateY(-2px)',
      }}
      transition="all 0.2s ease-in-out"
    >
      <HStack spacing={2}>
        <Icon as={icon || FiToggleLeft} boxSize={4} color={`${colorScheme}.500`} />
        <Text fontWeight="600" fontSize="sm">
          {label}
        </Text>
      </HStack>
      <Badge colorScheme={statusColorScheme} variant="solid" px={3} py={1} borderRadius="full">
        <HStack spacing={1}>
          <Icon as={isEnabled ? FiCheckCircle : FiXCircle} boxSize={4} />
          <Text>{isEnabled ? 'ENABLED' : 'DISABLED'}</Text>
        </HStack>
      </Badge>
    </Flex>
  );
};

const MetricsGrid = ({ metrics, colorScheme }: { metrics: any; colorScheme: string }) => {
  return (
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      {Object.entries(metrics).map(([key, value]) => (
        <Stat
          key={key}
          p={3}
          borderRadius="lg"
          borderWidth={1}
          borderColor={`${colorScheme}.100`}
          textAlign="center"
          _hover={{
            shadow: 'lg',
            bg: `${colorScheme}.100`,
            transform: 'translateY(-2px)',
          }}
          transition="all 0.3s ease"
        >
          <StatLabel fontSize="xs" color={`${colorScheme}.600`} fontWeight="bold">
            {startCase(key)}
          </StatLabel>
          <StatNumber fontSize="lg" color={`${colorScheme}.700`}>
            {typeof value === 'number' ? value.toLocaleString() : String(value)}
          </StatNumber>
        </Stat>
      ))}
    </SimpleGrid>
  );
};

const ApplicationVitals = ({ spec, colorScheme }: { spec: any; colorScheme: string }) => {
  const [showContent, setShowContent] = useState(true);

  return (
    <Card
      variant="outline"
      shadow="lg"
      borderColor={`${colorScheme}.200`}
      overflow="hidden"
      _hover={{
        shadow: 'xl',
        transform: 'translateY(-2px)',
      }}
      transition="all 0.3s ease-in-out"
    >
      <CardHeader
        py={3}
        cursor="pointer"
        shadow="md"
        onClick={() => setShowContent(!showContent)}
        bg={showContent ? `${colorScheme}.100` : 'transparent'}
        _hover={{
          bg: `${colorScheme}.100`,
        }}
        transition="all 0.2s ease-in-out"
      >
        <HStack justify="space-between" align="center">
          <HStack spacing={3}>
            <Box
              bg={`${colorScheme}.500`}
              bgGradient={`linear(135deg, ${colorScheme}.400, ${colorScheme}.600)`}
              borderRadius="full"
              p={2}
              shadow="md"
            >
              <Icon as={FiActivity} boxSize={6} color="white" />
            </Box>

            <Box>
              <Heading size="md" color={`${colorScheme}.700`}>
                Application Vitals
              </Heading>
              <Text fontSize="sm" fontWeight="bold">
                An overview of runtime status and performance
              </Text>
            </Box>
          </HStack>
          <VStack spacing={1} align="end">
            <Badge colorScheme={spec.enabled ? 'green' : 'red'} variant="solid" px={3} py={1} borderRadius="full">
              <HStack spacing={1}>
                <Icon as={spec.enabled ? FiCheckCircle : FiXCircle} boxSize={6} />
                <Text>{spec.enabled ? 'Enabled' : 'Disabled'}</Text>
              </HStack>
            </Badge>
          </VStack>
        </HStack>
      </CardHeader>
      <Collapse in={showContent} animateOpacity>
        <CardBody py={4}>
          <VStack spacing={6} align="stretch" p={4}>
            {/* Configuration Status */}
            <CommonConfigurationStatus
              title="Application Configuration Status"
              colorScheme={colorScheme}
              columns={{ base: 1, md: 2, lg: 3 }}
              items={[
                {
                  label: 'Stop TX/RX Upon RU Sync Lost',
                  value: spec.StopTxRxUponRuSyncLostInd,
                  icon: FiWifiOff,
                },
                {
                  label: 'UL Carrier Aggregation',
                  value: spec.uplinkCA?.isUlCAEnbld,
                  icon: FiLayers,
                },
                {
                  label: 'RU P19 Control',
                  value: spec.RUp19Control,
                  icon: FiSliders,
                },
                {
                  label: 'Binary Logging',
                  value: spec.enableBinLog,
                  icon: FiFileText,
                },
                {
                  label: 'Application Enabled',
                  value: spec.enabled,
                  icon: FiPower,
                },
                {
                  label: 'F1 CDU',
                  value: spec.F1CDUId > 0,
                  icon: FiServer,
                },
                {
                  label: 'UL CA Enabled',
                  value: spec.uplinkCA?.isUlCAEnbld,
                  icon: FiTrendingUp,
                },
              ]}
            />

            <Divider borderColor={`${colorScheme}.200`} />

            {/* Performance Metrics */}
            {/* Performance & Storage Metrics */}
            <CommonMetricsGrid
              title="Log Metrics"
              colorScheme={colorScheme}
              columns={{ base: 2, md: 3, lg: 4 }}
              metrics={{
                logFileName: spec.logFileName,
                maxLogFileCount: spec.maxLogFileCount,
                maxLogFileSize: spec.maxLogFileSize,
              }}
            />
            <CommonMetricsGrid
              title="Statistics & Monitoring Metrics"
              colorScheme={colorScheme}
              columns={{ base: 2, md: 3, lg: 4 }}
              metrics={{
                maxStatsFileParts: spec.maxStatsFileParts,
                maxStatsFileSizeBytes: spec.maxStatsFileSizeBytes,
                RUSyncMonitorInterval: spec.RUSyncMonitorInterval,
                samplingPeriodInS: spec.MeasurementControl?.samplingPeriod,
              }}
            />
            <CommonMetricsGrid
              title="Flow Control & Network"
              colorScheme={colorScheme}
              columns={{ base: 2, md: 3, lg: 4 }}
              metrics={{
                enNrupMissingReport: spec.flowCtrlCfgInfo?.enNrupMissingReport,
                nrupFlowCtrlTimerInMs: spec.flowCtrlCfgInfo?.nrupFlowCtrlTmrInMS,
              }}
            />
            <CommonMetricsGrid
              title="Radio Network Temporary Identifier"
              colorScheme={colorScheme}
              columns={{ base: 2, md: 3, lg: 4 }}
              metrics={{
                maxNumRnti: spec.peeParametersList?.rntiInfo?.maxNumRnti,
                rntiStart: spec.peeParametersList?.rntiInfo?.rntiStart,
              }}
            />
          </VStack>
        </CardBody>
      </Collapse>
    </Card>
  );
};

export default ApplicationVitals;
