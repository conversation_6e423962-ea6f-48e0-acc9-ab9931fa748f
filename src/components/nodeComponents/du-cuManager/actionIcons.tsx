import { FiRotateCcw, <PERSON><PERSON>ock, <PERSON><PERSON><PERSON>lock, <PERSON>Tool, <PERSON>R<PERSON><PERSON><PERSON><PERSON>, FiServer } from 'react-icons/fi';

export const actionIcons: Record<OranRuActionIconType, JSX.Element> = {
  [OranRuActionIconType.RESTART]: <FiRotateCcw size="25" />,
  [OranRuActionIconType.REBOOT]: <FiRefreshCw size="25" />,
  [OranRuActionIconType.LOCK]: <FiLock size="25" />,
  [OranRuActionIconType.UNLOCK]: <FiUnlock size="25" />,
  [OranRuActionIconType.TRIGGER_DIAGNOSTICS]: <FiTool size="25" />,
  [OranRuActionIconType.FACTORY_RECOVERY]: <FiRefreshCw size="25" />,
  [OranRuActionIconType.TRIGGER_RU_CRASH_LOGS_UPLOAD]: <FiServer size="25" />,
};

import {
  OranRuActionType,
  OranRuActionIconType,
  actionIconMapping,
  actionButtonIconMapping,
} from './OranRuActionTypes';

export const getIconForAction = (actionType: OranRuActionIconType): JSX.Element | null => {
  const iconType = actionIconMapping[actionType];
  return iconType ? actionIcons[iconType] : null;
};

export const getIconForActionButton = (actionType: OranRuActionType): OranRuActionIconType | undefined => {
  return actionButtonIconMapping[actionType];
};
