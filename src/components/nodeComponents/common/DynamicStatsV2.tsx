import { Box, Divider, Flex, Stat, StatGroup, StatHelpText, StatLabel, StatNumber, Text } from '@chakra-ui/react';
import _, { isNil } from 'lodash';
import React, { useEffect } from 'react';
import { formatInReadableTimeDate } from '../../../utils/formatInReadableTimeData';
import { timeticksToTime } from '../../../utils/helpers';
import { UtilizationBar } from './UtilizationBar';
import BatteryStatusIcon, { statusType } from './BatteryStatusIcon';

import RestartButton from './DruidRestart';

interface StatData {
  label: string;
  value: React.ReactNode;
  optionalvalue?: React.ReactNode;
  helpText?: string;
  renderAs?: 'utilizationBar' | 'location' | 'status';
  arrow?: 'increase' | 'decrease';
  SectionText?: string;
  isHeader?: boolean;
}
interface StatHeader {
  label: string;
  value: 'Header'; // Since headers have a static value, we can type it directly
  isHeader: true;
}
type StatSectionItem = StatHeader | StatData[];

interface DynamicStatsV2Props {
  data: any;
  nodeId?: string | undefined;
  componentId?: string | number | undefined;
  caller?: string | undefined;
  clockData?: Array<any> | null;
  setHasError?: React.Dispatch<React.SetStateAction<boolean>>;
  isJuniperBox?: boolean;
  isJuniperBoxContents?: boolean;
  isJuniperFirewallVpn?: boolean;
  isPowerUPS?: boolean;
  isFortinetBox?: boolean;
  isFortinetBoxNtpContents?: boolean;
  isFortinetBoxDhcpContents?: boolean;
  isFortinetBoxPortContents?: boolean;
}

export const DynamicStatsV2: React.FC<DynamicStatsV2Props> = ({
  data,
  nodeId,
  componentId,
  isJuniperBox = false,
  isJuniperBoxContents = false,
  isJuniperFirewallVpn = false,
  isPowerUPS = false,
  isFortinetBox = false,
  isFortinetBoxNtpContents = false,
  isFortinetBoxDhcpContents = false,
  isFortinetBoxPortContents = false,
  setHasError,
  caller,
}) => {
  const vpninfo = [];
  if (isJuniperFirewallVpn && data) {
    vpninfo.push([
      {
        label: 'Configured Vpns',
        value: data?.jnxVpnConfiguredVpns,
        optionalvalue: 'VPNs Info',
      },
      {
        label: 'Active Vpns',
        value: data?.jnxVpnActiveVpns,
      },
    ]);
  }

  const rootValues = [
    {
      label: 'Component id',
      value: componentId,
    },
    {
      label: 'IP address',
      value: data?.ip_address,
    },
    {
      label: 'Start Time',
      value: formatInReadableTimeDate(data?.system?.startTime ?? '0').toString(),
    },
    {
      label: 'Last Query Time',
      value: formatInReadableTimeDate((data?.last_query_time ?? '0').toString()),
    },
    {
      label: 'Name',
      value: data?.system?.sysName,
    },
    {
      label: 'Contact',
      value: data?.system?.sysContact,
    },
    {
      label: 'Up time',
      value: timeticksToTime(Number(data?.system?.sysUpTime ?? '0')),
    },
    {
      label: 'Firmware version',
      value: data?.firmware,
    },
    {
      label: 'Monitored',
      value: data?.monitored,
    },
  ];

  const errorValues = [
    {
      label: 'Last Error',
      value: data?.last_error,
      optionalvalue: 'Error',
    },
    {
      label: 'Error count',
      value: data?.query_error_count,
    },
    {
      label: 'Last Successful Query',
      value: formatInReadableTimeDate((data?.last_success_time ?? '0').toString()),
    },
  ];

  const powerUPS = [
    [
      {
        label: 'Component id',
        value: componentId,
      },
      {
        label: 'IP address',
        value: data?.ip_address,
      },
      {
        label: 'Last Query Time',
        value: formatInReadableTimeDate((data?.last_query_time ?? '0').toString()),
      },
      {
        label: 'Firmware version',
        value: data?.firmware,
      },
      {
        label: 'Monitored',
        value: data?.monitored,
      },
    ],
    [
      { label: 'Description', value: data?.system?.sysDescr, optionalvalue: 'System' },
      { label: 'Object Id', value: data?.system?.sysObjectID },

      {
        label: 'Up time',
        value: timeticksToTime(Number(data?.system?.sysUpTime ?? '0')),
      },
      {
        label: 'Start Time',
        value: formatInReadableTimeDate(data?.system?.startTime ?? '0').toString(),
      },
      {
        label: 'Name',
        value: data?.system?.sysName,
      },
      {
        label: 'Contact',
        value: data?.system?.sysContact,
      },

      {
        label: 'Latitude',
        value: data?.system?.latitude,
      },
      {
        label: 'Longtitude',
        value: data?.system?.longitude,
      },
      {
        label: 'Services',
        value: data?.system?.sysServices,
      },
      {
        label: 'Last Change',
        value: data?.system?.sysORLastChange,
      },
    ],
    [
      {
        label: 'Name',
        value: data?.info?.name,
        optionalvalue: 'Info',
      },
      {
        label: 'Model',
        value: data?.info?.model,
      },
      {
        label: 'Manufacturer',
        value: data?.info?.manufacturer,
      },
    ],
    [
      {
        label: 'Status',
        value: data?.battery?.battery_status.toLowerCase(),
        renderAs: 'status',
        optionalvalue: 'Battery',
      },
      {
        label: 'Time on Battery',
        value: `${data?.battery?.time_on_battery} seconds`,
      },
      {
        label: 'Estimated Time Left',
        value: `${data?.battery?.estimated_time_left} minutes`,
      },
      {
        label: 'Estimated Charge Left',
        value: Number(data?.battery?.estimated_charge_left ?? '0'),
        renderAs: 'utilizationBar',
      },
      {
        label: 'Battery Voltage',
        value: `${data?.battery?.battery_voltage} V`,
      },
      {
        label: 'Battery Current',
        value: `${data?.battery?.battery_current} A`,
      },
      {
        label: 'Battery Temperature',
        value: `${data?.battery?.temperature} °C`,
      },
    ],
  ];

  const juniperBox = [
    [
      {
        label: 'Description',
        value: data?.jnxBoxDescr,
      },
      {
        label: 'Serial No.',
        value: data?.jnxBoxSerialNo,
      },
      {
        label: 'Revision',
        value: data?.jnxBoxRevision,
      },
      {
        label: 'Installed',
        value: formatInReadableTimeDate((data?.jnxBoxInstalled ?? '0').toString()),
      },
    ],
  ];

  const fortinetBox = [
    [
      {
        label: 'Description',
        value: !isNil(data?.description) ? data.description : undefined,
      },
      {
        label: 'Serial No.',
        value: !isNil(data?.serial) ? data.serial : undefined,
      },
      {
        label: 'Version',
        value: !isNil(data?.version) ? data.version : undefined,
      },
      {
        label: 'IP Address',
        value: !isNil(data?.ip_address) ? data.ip_address : undefined,
      },
      {
        label: 'Latitude',
        value: !isNil(data?.latitude) ? data.latitude : undefined,
      },
      {
        label: 'Longitude',
        value: !isNil(data?.longitude) ? data.longitude : undefined,
      },
      {
        label: 'Device name',
        value: !isNil(data?.device_name) ? data.device_name : undefined,
      },
      {
        label: 'Manager name ',
        value: !isNil(data?.manager_name) ? data.manager_name : undefined,
      },
      {
        label: 'Model',
        value: !isNil(data?.model) ? data.model : undefined,
      },
      {
        label: 'Status',
        value: !isNil(data?.status) ? data.status : undefined,
      },
      {
        label: 'Created at',
        value: formatInReadableTimeDate((data?.created_at ?? '0').toString()),
      },
      {
        label: 'Updated at',
        value: formatInReadableTimeDate((data?.updated_at ?? '0').toString()),
      },
    ].filter((item) => !isNil(item.value) && !_.isEmpty(item.value)),
  ];

  const fortinetBoxNtpContent = [
    [
      {
        label: 'NTP Sync',
        value: !isNil(data?.ntpsync) ? data.ntpsync : undefined,
      },
      {
        label: 'Type',
        value: !isNil(data?.type) ? data.type : undefined,
      },

      {
        label: 'Hostname',
        value: !isNil(data?.hostname) ? data.hostname : undefined,
      },
      {
        label: 'Synchronized',
        value: !isNil(data?.synchronized) ? data.synchronized : undefined,
      },
      {
        label: 'Authentication',
        value: !isNil(data?.authentication) ? data.authentication : undefined,
      },
      {
        label: 'Server',
        value: !isNil(data?.['server-mode'])
          ? data?.['server-mode'] === 'enable'
            ? `enabled${
                data?.interface && Array.isArray(data.interface) && data.interface.length > 0
                  ? ` (Interfaces: ${data.interface.map((iface: any) => iface['interface-name']).join(', ')})`
                  : ''
              }`
            : data?.['server-mode']
          : undefined,
      },
    ].filter((item) => !isNil(item.value) && !_.isEmpty(item.value)),
  ];

  const fortinetBoxDhcpContent = [
    [
      {
        label: 'default-gateway',
        value: !isNil(data?.['default-gateway']) ? data?.['default-gateway'] : undefined,
      },
      {
        label: 'ip-range',
        value: !isNil(data?.['ip-range'])
          ? `${data?.['ip-range'][0]['start-ip']} - ${data?.['ip-range'][0]['end-ip']}`
          : undefined,
      },
      {
        label: 'netmask',
        value: !isNil(data?.netmask) ? data.netmask : undefined,
      },
      {
        label: 'status',
        value: !isNil(data?.status) ? data.status : undefined,
      },
    ].filter((item) => !isNil(item.value) && !_.isEmpty(item.value)),
  ];

  const druid = [
    [
      {
        label: 'Device Id',
        value: data?.device_id,
      },
      {
        label: 'Sessions',
        value: data?.sessions?.length,
      },
    ],
  ];

  if (data?.details?.features) {
    const featuresArray = [
      {
        label: 'Operational State',
        value: data.details.features.oper_state,
        optionalvalue: 'Features',
      },
      {
        label: 'System Id',
        value: data.details.features.system_id,
      },
      {
        label: 'Issue Date',
        value: data.details.features.issue_date && formatInReadableTimeDate(data.details.features.issue_date),
      },
      {
        label: 'Expiry Date',
        value: data.details.features.expiry_date && formatInReadableTimeDate(data.details.features.expiry_date),
      },
      {
        label: 'Binding Date',
        value: data.details.features.binding_date && formatInReadableTimeDate(data.details.features.binding_date),
      },
      {
        label: 'Supported Until',
        value: data.details.features.supported_until && formatInReadableTimeDate(data.details.features.supported_until),
      },
    ];

    druid.push(featuresArray);
  }

  if (data?.details?.system) {
    const systemArray = [
      {
        label: 'Operational State',
        value: data.details.system.oper_state,
        optionalvalue: 'System',
      },
      {
        label: 'Admin State',
        value: data.details.system.admin_state,
      },
      {
        label: 'Service State',
        value: data.details.system.service_state,
      },
      {
        label: 'System Id',
        value: data.details.system.system_id,
      },
      {
        label: 'License Id',
        value: data.details.system.license_id,
      },
      {
        label: 'Software Version',
        value: data.details.system.software_version,
      },
      {
        label: 'Current Time',
        value: data.details.system.current_time && formatInReadableTimeDate(data.details.system.current_time),
      },
    ];

    druid.push(systemArray);
  }

  const additionalSections: StatData[][] = [];

  if (!isNil(data?.fru)) {
    additionalSections.push(
      [
        {
          label: 'Name',
          value: data?.fru?.jnxFruName,
          optionalvalue: 'Field Replaceable Unit',
        },
        {
          label: 'Type',
          value: data?.fru?.jnxFruType,
        },
        {
          label: 'Slot',
          value: data?.fru?.jnxFruSlot,
        },
        {
          label: 'State',
          value: data?.fru?.jnxFruState,
        },
        {
          label: 'Temp',
          value: data?.fru?.jnxFruTemp,
        },
        {
          label: 'Reason',
          value: data?.fru?.jnxOfflineReason,
        },
        {
          label: 'Up Time',
          value: data?.operating?.jnxFruPowerUpTime,
        },
      ].filter((item) => !isNil(item.value))
    );
  }

  if (!isNil(data?.led)) {
    additionalSections.push(
      [
        {
          label: 'Description',
          value: data?.led?.jnxLEDDescr,
          optionalvalue: 'LED',
        },
        {
          label: 'State',
          value: data?.led?.jnxLEDState,
        },
      ].filter((item) => !isNil(item.value))
    );
  }

  const juniperBoxContent = [
    // Details
    [
      {
        label: 'Description',
        value: data?.detail?.jnxContentsDescr,
        optionalvalue: 'Details',
      },
      {
        label: 'Serial No.',
        value: data?.detail?.jnxContentsSerialNo,
      },
      {
        label: 'Revision',
        value: data?.detail?.jnxContentsRevision,
      },
      {
        label: 'Installed',
        value: formatInReadableTimeDate((data?.detail?.jnxContentsInstalled ?? '0').toString()),
      },
      {
        label: 'PartNo',
        value: data?.detail?.jnxContentsPartNo,
      },
      {
        label: 'Model',
        value: data?.detail?.jnxContentsModel,
      },
    ],
    // Operating system
    [
      {
        label: 'Description',
        value: data?.operating?.jnxOperatingDescr,
        optionalvalue: data?.operating?.jnxOperatingDescr ? 'Operating System' : '',
      },
      {
        label: 'State',
        value: data?.operating?.jnxOperatingState,
      },
      {
        label: 'Temp',
        value: data?.operating?.jnxOperatingTemp,
      },
      {
        label: 'Up Time',
        value: data?.operating?.jnxOperatingUpTime,
      },
      {
        label: 'Last Restart',
        value: formatInReadableTimeDate((data?.operating?.jnxOperatingLastRestart ?? '0').toString()),
      },
      // to align the cpu bars
      { label: '', value: '' },
      {
        label: 'Min Avg CPU',
        value: Number(data?.operating?.jnxOperating1MinAvgCPU ?? '0'),
        renderAs: 'utilizationBar',
      },
      {
        label: 'Buffer CP',
        value: Number(data?.operating?.jnxOperatingBufferCP ?? '0'),
        renderAs: 'utilizationBar',
      },
    ].filter((item) => {
      if (item.renderAs === 'utilizationBar' && (_.isNil(item.value) || _.toNumber(item.value) === 0)) {
        return false; //keeping '0' as invalid for util bars
      }
      return !_.isNil(item.value) && !_.isEmpty(item.value.toString());
    }),
    ...additionalSections,
  ];

  function formatDataForDisplay(
    components: { label: string; value: any; optionalvalue?: string }[][],
    showNAForEmptyValues = false // New argument with a default value of false
  ): any[] {
    // Adjusted to process each item, showing "N/A" for empty values only if the label exists
    const processItem = (item: { label: string; value: any }) => {
      const isValueEmpty =
        !_.isNumber(item?.value) && (_.isEmpty(item?.value) || _.isNil(item?.value) || _.isNaN(item?.value));

      // Show "N/A" only if the label exists and the value is empty, according to the flag
      if (showNAForEmptyValues && item.label && isValueEmpty) {
        return { ...item, value: 'N/A' };
      }
      return item;
    };

    const preparedSections = components
      .flatMap((section) => {
        const sectionHeader = section[0]?.optionalvalue
          ? [{ label: section[0].optionalvalue, value: 'Header', isHeader: true }]
          : [];

        const pairedItems = [];
        for (let i = 0; i < section.length; i += 2) {
          // Process each item, applying the "N/A" logic only to items with labels and empty values
          const firstItemProcessed = processItem(section[i]);
          const secondItem = section[i + 1]
            ? processItem(section[i + 1])
            : processItem({ label: '', value: undefined });

          const pair = [firstItemProcessed, secondItem];
          // Include the pair if at least one item has a label
          if (pair[0].label || (pair[1] && pair[1].label)) {
            pairedItems.push(pair);
          }
        }

        // Sections with headers should always be shown; otherwise, only include sections with paired items
        return sectionHeader.length > 0 || pairedItems.length > 0 ? [[...sectionHeader, ...pairedItems]] : [];
      })
      .filter((section) => section.length > 0 && section.flat().length > 1); // Filter to ensure sections have meaningful content
    return preparedSections;
  }

  const hasError = errorValues.some((stat) => stat.label === 'Last Error' && stat.value && stat.value !== 'N/A');
  const updatedErrorValues = [...(hasError ? errorValues : [])];
  let section: any;
  const caseKey = isJuniperBox
    ? 'JuniperBox'
    : isJuniperBoxContents
    ? 'JuniperBoxContents'
    : isJuniperFirewallVpn
    ? 'JuniperFirewallVpn'
    : isFortinetBox
    ? 'FortinetBox'
    : isFortinetBoxNtpContents
    ? 'FortinetBoxNtpContent'
    : isFortinetBoxDhcpContents
    ? 'FortinetBoxDhcpContent'
    : caller === 'druid'
    ? 'Druid'
    : isPowerUPS
    ? 'PowerHouse'
    : 'Default';

  switch (caseKey) {
    case 'JuniperBox':
      section = juniperBox;
      break;
    case 'JuniperBoxContents':
      section = juniperBoxContent;
      break;
    case 'JuniperFirewallVpn':
      section = vpninfo;
      break;
    case 'FortinetBox':
      section = fortinetBox;
      break;
    case 'FortinetBoxNtpContent':
      section = fortinetBoxNtpContent;
      break;
    case 'FortinetBoxDhcpContent':
      section = fortinetBoxDhcpContent;
      break;
    case 'Druid':
      section = druid;
      break;
    case 'PowerHouse':
      section = powerUPS;
      break;
    default:
      section = [rootValues, updatedErrorValues];
      break;
  }

  useEffect(() => {
    if (setHasError) {
      if (hasError) setHasError(true);
      else setHasError(false);
    }
  }, [hasError, setHasError]);

  const displayData = formatDataForDisplay(section, true);

  return (
    <>
      {caseKey === 'Druid' && <RestartButton nodeId={nodeId} />}

      {displayData.map((section, sectionIndex) => (
        <StatGroup mb="8" key={sectionIndex} display="flex" flexDirection="column" mt={isJuniperBox ? '-7' : ''}>
          {section.map((item: StatSectionItem, itemIndex: number) => {
            if ('isHeader' in item) {
              // Render header
              return (
                <Box
                  width="100%"
                  mt="5"
                  mb="5"
                  display="flex"
                  alignItems="center"
                  data-testid="stats-header-box"
                  key={`header-${sectionIndex}-${itemIndex}`}
                >
                  <Text mb="2" fontWeight="bold" fontSize="md" data-testid="stats-header">
                    {item.label}
                  </Text>
                  <Divider flex="1" mb="4" ml="2" borderColor="gray.300" borderWidth="2px" />
                </Box>
              );
            } else {
              // Render stat pairs
              return (
                <Box
                  data-testid="stats-label-box"
                  display="flex"
                  mb="3"
                  key={`stat-pair-${sectionIndex}-${itemIndex}`}
                  width="100%"
                >
                  {item.map((stat: StatData, statIndex: number) => (
                    <Stat
                      width="calc(50% - 9px)"
                      height={['40px']}
                      key={`stat-${sectionIndex}-${itemIndex}-${statIndex}`}
                    >
                      {stat.value || stat.value === 0 ? (
                        <>
                          <StatLabel data-testid="stats-label">{stat.label}</StatLabel>
                          <StatNumber fontSize="md" isTruncated maxWidth="60ch">
                            {stat.renderAs === 'utilizationBar' ? (
                              <UtilizationBar
                                percentage={Number(stat.value)}
                                inverted={isPowerUPS ? true : false}
                                threshold={isPowerUPS ? 20 : 80}
                              />
                            ) : stat.renderAs === 'status' && stat.value ? (
                              <BatteryStatusIcon status={stat.value as statusType} />
                            ) : (
                              <Box mb="1">
                                {stat.label === 'Temp' && !_.isNil(stat.value) ? (
                                  <Text as="span">{stat.value.toString()} &#8451;</Text>
                                ) : (
                                  <Text as="span">{stat.value.toString()} </Text>
                                )}
                              </Box>
                            )}
                          </StatNumber>
                          {stat.helpText && <StatHelpText>{stat.helpText}</StatHelpText>}
                        </>
                      ) : (
                        <></>
                      )}
                    </Stat>
                  ))}
                </Box>
              );
            }
          })}
        </StatGroup>
      ))}
    </>
  );
};
