import { transformData } from '../../../../../pages/MetricsCollector/hooks/useParticleMetrics';

type ParticleMetrics = {
  count: number;
  events: Array<{
    id: number;
    coreid: string;
    date: string;
    temperature: number;
    bluwireless_fan_speed: number;
    airspan_fan_speed: number;
  }>;
};

describe('transformData function', () => {
  it('transforms ParticleMetrics into TransformedDataType', () => {
    const mockInput: ParticleMetrics = {
      count: 2,
      events: [
        {
          id: 1,
          coreid: 'abcd',
          date: '2023-10-15T12:42:33',
          temperature: 38.9,
          bluwireless_fan_speed: 0,
          airspan_fan_speed: 0,
        },
        {
          id: 2,
          coreid: 'efgh',
          date: '2023-10-15T12:43:33',
          temperature: 39.0,
          bluwireless_fan_speed: 60,
          airspan_fan_speed: 0,
        },
      ],
    };

    const result = transformData(mockInput);

    expect(result.temperature).toEqual([38.9, 39.0]);
    expect(result.bwFanSpeed).toEqual([0, 60]);
    expect(result.asFanSpeed).toEqual([0, 0]);
  });
});
