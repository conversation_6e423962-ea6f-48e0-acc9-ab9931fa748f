import { ArrowDownIcon, ArrowUpIcon } from '@chakra-ui/icons';
import { Box, Button, Card, CardBody, Icon, Stack, Text, useDisclosure } from '@chakra-ui/react';
import { ColumnDef } from '@tanstack/react-table';
import { isEmpty } from 'lodash';
import React, { useState } from 'react';
import { FaChartLine } from 'react-icons/fa';
import { ComponentTypeEnum } from '../../../services/types';
import { FortinetSwitchType } from '../../../types/orchestrator.types';
import { recursiveSearch } from '../../../utils/recursiveSearch';
import { DynamicStatsV2 } from '../common/DynamicStatsV2';
import ModalDataTable from '../common/ModalDataTable';
import ComponentModal from '../ComponentModal';
import FortinetBoxFirewall from './FortinetBoxFirewall';

type FortinetFirewallProps = {
  data?: FortinetSwitchType;
  id?: string | number;
  marginSpace?: string | number;
  caller?: string;
  nodeType?: string;
};

export type FortinetFirewallTypes = {
  ifDescr: string;
  name?: string;
  link_ref?: string;
  ifType?: string;
  ifIn1SecRate?: number;
  ifOut1SecRate?: number;
  ifOperStatus?: string;
  ifIndex?: number | null;
  ifLastChange?: number;
  ifAdminStatus?: string | null;
  ifPhysAddress?: string | null;
  ifInDiscards?: number | null;
  ifInErrors?: number | null;
  ifOutDiscards?: number | null;
  ifOutErrors?: number | null;
  ifSpeed?: number | null;
  ifMtu?: number | null;
};

const FortinetFirewall: React.FC<FortinetFirewallProps> = ({ data, marginSpace }) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: moreDataModalIsOpen, onOpen: openMoreDataModal, onClose: closeMoreDataModal } = useDisclosure();
  const [hasError, setHasError] = useState<boolean>(false);

  const flattenedData: any = recursiveSearch(data);

  const columns = React.useMemo<ColumnDef<FortinetFirewallTypes>[]>(
    () => [
      {
        header: 'Description',
        accessorKey: 'ifDescr',
        id: 'ifDescr',
      },
      {
        header: 'Name',
        accessorKey: 'name',
        id: 'name',
      },
      {
        header: 'Link Ref',
        accessorKey: 'link_ref',
        id: 'link_ref',
      },
      {
        header: 'Type',
        accessorKey: 'ifType',
        id: 'ifType',
      },
      {
        header: 'Oper Status',
        accessorKey: 'ifOperStatus',
        id: 'ifOperStatus',
        cell: ({ getValue }) => {
          if (getValue() === null) return '-';
          return (
            <Box boxSize="5">
              {getValue() === 'up' ? (
                <ArrowUpIcon boxSize="5" color="green" data-testid="arrowUp_grren">
                  getValue()
                </ArrowUpIcon>
              ) : (
                <ArrowDownIcon boxSize="5" color="red" data-testid="arrowDown_red">
                  getValue()
                </ArrowDownIcon>
              )}
            </Box>
          );
        },
      },
      {
        header: 'In 1-Sec Rate',
        accessorKey: 'ifIn1SecRate',
        id: 'ifIn1SecRate',
        cell: ({ getValue }) => {
          const value = Number(getValue()) || 0;
          return <>{value === 0 ? value + ' Bytes' : value + ' Mbps'}</>;
        },
      },
      {
        header: 'Out 1-Sec Rate',
        accessorKey: 'ifOut1SecRate',
        id: 'ifOut1SecRate',
        cell: ({ getValue }) => {
          const value = Number(getValue()) || 0;
          return <>{value === 0 ? value + ' Bytes' : value + ' Mbps'}</>;
        },
      },
      {
        header: '',
        accessorKey: 'chart',
        id: 'chart',
        cell: ({ row }) => {
          const data = row.original;

          if ('ifOperStatus' in data && 'ifAdminStatus' in data) {
            const ifOperStatus = data.ifOperStatus;
            const isUp = ifOperStatus === 'up';

            return (
              <Box boxSize="10" p="2">
                <Icon as={FaChartLine} color={isUp ? 'currentColor' : 'gray.400'} w="7" h="7" />
              </Box>
            );
          }
        },
      },
    ],
    []
  );

  return (
    <>
      <Card width="100%" marginRight={marginSpace} borderRadius="lg">
        <CardBody borderTop="1px solid #e2e2e2">
          <Stack spacing="2">
            <Text fontSize="x-large" my="4">
              Fortinet Firewall
            </Text>
            {!isEmpty(data) ? (
              <DynamicStatsV2
                data={data}
                setHasError={setHasError}
                componentId={data.device_name}
                isFortinetBox={true}
              />
            ) : (
              <Text fontSize="sm" color="gray.500">
                Not Available
              </Text>
            )}

            <Text fontSize="x-large">NTP</Text>
            {!isEmpty(data?.ntp) || !isEmpty(data?.ntp_status) ? (
              <Box>
                <DynamicStatsV2
                  data={{ ...data?.ntp, ...data?.ntp_status }}
                  setHasError={setHasError}
                  componentId={data?.ntp?.key}
                  isFortinetBoxNtpContents={true}
                />
              </Box>
            ) : (
              <Text fontSize="sm" color="gray.500">
                Not Available
              </Text>
            )}

            <Text fontSize="x-large">DHCP</Text>
            {data && Array.isArray(data.dhcp) && data.dhcp.length > 0 ? (
              <DynamicStatsV2
                data={data.dhcp[0]}
                setHasError={setHasError}
                componentId={data.dhcp[0].oid}
                isFortinetBoxDhcpContents={true}
              />
            ) : (
              <Text fontSize="sm" color="gray.500">
                Not Available
              </Text>
            )}

            {/* Fortinet Box alarms component */}
            {!hasError ? (
              !isEmpty(data?.alarms) ? (
                <FortinetBoxFirewall data={data?.alarms} type="Alarms" />
              ) : null
            ) : null}

            {/* Fortinet Box ports component */}
            {!hasError ? (
              !isEmpty(data?.ports) ? (
                <FortinetBoxFirewall data={data?.ports} type="Ports" />
              ) : (
                <Text fontSize="sm" color="gray.500">
                  Ports Not Available
                </Text>
              )
            ) : null}

            {/* Fortinet Box licences component */}
            {!hasError ? (
              !isEmpty(data?.licences) ? (
                <FortinetBoxFirewall data={data?.licences} type="Licences" />
              ) : (
                <Text fontSize="sm" color="gray.500">
                  Licences Not Available
                </Text>
              )
            ) : null}

            {/* Fortinet Box certificates component */}
            {!hasError ? (
              !isEmpty(data?.certificates) ? (
                <FortinetBoxFirewall data={data?.certificates} type="Certificates" />
              ) : (
                <Text fontSize="sm" color="gray.500">
                  Certificates Not Available
                </Text>
              )
            ) : null}

            {data?.interfaces?.length && !data?.error && !hasError ? (
              <Button onClick={onOpen} mt="12">
                Interface Details
              </Button>
            ) : null}
            <Button onClick={openMoreDataModal}>More Data</Button>
          </Stack>
        </CardBody>
      </Card>

      {/* More data Model */}
      <ComponentModal isOpen={moreDataModalIsOpen} onClose={closeMoreDataModal} flattenedData={flattenedData} />

      {/* Firewall Modal */}
      <ModalDataTable<FortinetFirewallTypes>
        isOpen={isOpen}
        onClose={onClose}
        columns={columns}
        data={data?.interfaces || []}
        caller={'interface'}
        subComponentProps={{
          component_id: data?.device_name as string,
          component_type: ComponentTypeEnum.FORTINET,
        }}
      />
    </>
  );
};

export default FortinetFirewall;
