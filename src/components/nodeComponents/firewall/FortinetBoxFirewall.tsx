import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
  Flex,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tooltip,
  Tr,
} from '@chakra-ui/react';
import React, { Fragment, useState } from 'react';
import { formatInReadableTimeDate } from '../../../utils/formatInReadableTimeData';

interface FortinetBoxFirewallProps {
  data?: any;
  type?: string;
}

type FortinetFirewallAlarmsData = {
  component: string;
  event_id: string;
  event_type: string;
  message: string;
  severity: string;
};

type Ipv4Address = {
  ip: string;
  netmask: string;
  cidr_netmask: number;
};

type FortinetFirewallPortsData = {
  name: string;
  status: string;
  type: string;
  mac_address: string;
  ipv4_addresses: Ipv4Address[];
};

type FortinetFirewallCertificatesData = {
  name: string;
  issuer: {
    O: string;
    CN: string;
    C: string;
  };
  type: string;
  source: string;
  status: string;
  valid_from_raw: string;
  valid_to_raw: string;
};

const FortinetBoxFirewall: React.FC<FortinetBoxFirewallProps> = ({ data, type }) => {
  const [hasError, setHasError] = useState<boolean>(false);

  const alarmColumns = React.useMemo(
    () => [
      {
        header: 'Component',
        accessorKey: 'component',
        id: 'component',
      },
      {
        header: 'Event id',
        accessorKey: 'event_id',
        id: 'event_id',
      },
      {
        header: 'Event type',
        accessorKey: 'event_type',
        id: 'event_type',
      },
      {
        header: 'Message',
        accessorKey: 'message',
        id: 'message',
      },
      {
        header: 'Severity',
        accessorKey: 'severity',
        id: 'severity',
      },
    ],
    []
  );

  const portColumns = React.useMemo(
    () => [
      {
        header: 'Name',
        accessorKey: 'name',
        id: 'name',
      },
      {
        header: 'Status',
        accessorKey: 'status',
        id: 'status',
      },
      {
        header: 'Type',
        accessorKey: 'type',
        id: 'type',
      },
      {
        header: 'MAC Address',
        accessorKey: 'mac_address',
        id: 'mac_address',
      },
      {
        header: 'IP Address',
        accessorKey: 'ip_address',
        id: 'ip_address',
      },
    ],
    []
  );

  const licenceColumns = React.useMemo(
    () => [
      {
        header: 'Device name',
        accessorKey: 'device_name',
        id: 'device_name',
      },
      {
        header: 'Entitlement',
        accessorKey: 'entitlement',
        id: 'entitlement',
      },
      {
        header: 'Status',
        accessorKey: 'status',
        id: 'status',
      },
      {
        header: 'Type',
        accessorKey: 'type',
        id: 'type',
      },
    ],
    []
  );

  const certificatesColumns = React.useMemo(
    () => [
      {
        header: 'name',
        accessorKey: 'name',
        id: 'name',
      },
      {
        header: 'Issuer C',
        accessorKey: 'issuer',
        id: 'issuerC',
      },
      {
        header: 'Issuer CN',
        accessorKey: 'issuer',
        id: 'issuerCN',
      },
      {
        header: 'Issuer O',
        accessorKey: 'issuer',
        id: 'issuerO',
      },
      {
        header: 'Type',
        accessorKey: 'type',
        id: 'type',
      },
      {
        header: 'source',
        accessorKey: 'source',
        id: 'source',
      },
      {
        header: 'status',
        accessorKey: 'status',
        id: 'status',
      },
      {
        header: 'valid_from_raw',
        accessorKey: 'valid_from_raw',
        id: 'valid_from_raw',
      },
      {
        header: 'valid_to_raw',
        accessorKey: 'valid_to_raw',
        id: 'valid_to_raw',
      },
    ],
    []
  );

  let transformedData: any[] = [];

  if (type === 'Licences') {
    transformedData = Object.entries(data).map(([key, value]) => ({
      id: key,
      ...(value as Record<string, any>),
    }));
  }

  return (
    <>
      {!hasError && (
        <Box padding="10px">
          <Text fontSize="x-large" my="4">
            {type}
          </Text>
          {/* Alarms */}
          {type === 'Alarms' ? (
            <Accordion defaultIndex={[0]} allowMultiple>
              <AccordionItem>
                <AccordionButton>
                  <Text width="100%">View alarm data</Text>
                  <AccordionIcon />
                </AccordionButton>
                <AccordionPanel pb="4">
                  <Box overflowX="auto">
                    <Table variant="unstyled" size="sm" width="100%">
                      <Thead>
                        <Tr>
                          {alarmColumns.map((column) => (
                            <Th key={column.id} background="gray.50">
                              <Flex>
                                <Box ml="2">{column.header}</Box>
                              </Flex>
                            </Th>
                          ))}
                        </Tr>
                      </Thead>
                      {/* Alarms */}
                      <Tbody>
                        {data && data.length > 0 ? (
                          data
                            .filter((row: FortinetFirewallAlarmsData) => row && typeof row === 'object')
                            .map((rowData: FortinetFirewallAlarmsData, rowIndex: number) => (
                              <Fragment key={rowIndex}>
                                <Tr>
                                  <Td>
                                    {rowData.component && rowData.component.length > 20 ? (
                                      <Tooltip label={rowData.component} placement="top">
                                        <Box
                                          maxW="100px"
                                          whiteSpace="nowrap"
                                          overflow="hidden"
                                          textOverflow="ellipsis"
                                          cursor="pointer"
                                          onDoubleClick={() => {
                                            navigator.clipboard.writeText(rowData.component);
                                          }}
                                        >
                                          {rowData.component.slice(0, 20)}...
                                        </Box>
                                      </Tooltip>
                                    ) : (
                                      rowData.component || 'N/A'
                                    )}
                                  </Td>
                                  <Td>
                                    {rowData.event_id && rowData.event_id.length > 20 ? (
                                      <Tooltip label={rowData.event_id} placement="top">
                                        <Box
                                          maxW="100px"
                                          whiteSpace="nowrap"
                                          overflow="hidden"
                                          textOverflow="ellipsis"
                                          cursor="pointer"
                                          onDoubleClick={() => {
                                            navigator.clipboard.writeText(rowData.event_id);
                                          }}
                                        >
                                          {rowData.event_id.slice(0, 20)}...
                                        </Box>
                                      </Tooltip>
                                    ) : (
                                      rowData.event_id || 'N/A'
                                    )}
                                  </Td>
                                  <Td>{rowData.event_type || 'N/A'}</Td>
                                  <Td>{rowData.message || 'N/A'}</Td>
                                  <Td>{rowData.severity || 'N/A'}</Td>
                                </Tr>
                              </Fragment>
                            ))
                        ) : (
                          <Tr>
                            <Td colSpan={4}>No data available</Td>
                          </Tr>
                        )}
                      </Tbody>
                    </Table>
                  </Box>
                </AccordionPanel>
              </AccordionItem>
            </Accordion>
          ) : null}
          {/* Ports */}
          {type === 'Ports' ? (
            <Accordion defaultIndex={[0]} allowMultiple>
              <AccordionItem>
                <AccordionButton>
                  <Text width="100%">View ports data</Text>
                  <AccordionIcon />
                </AccordionButton>
                <AccordionPanel pb="4">
                  <Box overflowX="auto">
                    <Table variant="unstyled" size="sm" width="100%">
                      <Thead>
                        <Tr>
                          {portColumns.map((column) => (
                            <Th key={column.id} background="gray.50">
                              <Flex>
                                <Box ml="2">{column.header}</Box>
                              </Flex>
                            </Th>
                          ))}
                        </Tr>
                      </Thead>
                      <Tbody>
                        {data && data.length > 0 ? (
                          data
                            .filter((row: FortinetFirewallPortsData) => row && typeof row === 'object')
                            .map((rowData: FortinetFirewallPortsData, rowIndex: number) => (
                              <Fragment key={rowIndex}>
                                <Tr>
                                  <Td>{rowData.name || 'N/A'}</Td>
                                  <Td>{rowData.status || 'N/A'}</Td>
                                  <Td>{rowData.type || 'N/A'}</Td>
                                  <Td>{rowData.mac_address || ''}</Td>
                                  <Td>
                                    {rowData.ipv4_addresses && rowData.ipv4_addresses.length > 0
                                      ? rowData.ipv4_addresses
                                          .map((addr: { ip: string; netmask: string }) => `${addr.ip}/${addr.netmask}`)
                                          .join(', ')
                                      : ''}
                                  </Td>
                                </Tr>
                              </Fragment>
                            ))
                        ) : (
                          <Tr>
                            <Td colSpan={5}>No data available</Td>
                          </Tr>
                        )}
                      </Tbody>
                    </Table>
                  </Box>
                </AccordionPanel>
              </AccordionItem>
            </Accordion>
          ) : null}
          {/* Licences */}
          {type === 'Licences' ? (
            <Accordion defaultIndex={[0]} allowMultiple>
              <AccordionItem>
                <AccordionButton>
                  <Text width="100%">View licence data</Text>
                  <AccordionIcon />
                </AccordionButton>
                <AccordionPanel pb="4">
                  <Box overflowX="auto">
                    <Table variant="unstyled" size="sm" width="100%">
                      <Thead>
                        <Tr>
                          <Th background="gray.50">
                            <Flex>
                              <Box ml="2">Licence</Box>
                            </Flex>
                          </Th>
                          {licenceColumns.map((column) => (
                            <Th key={column.id} background="gray.50">
                              <Flex>
                                <Box ml="2">{column.header}</Box>
                              </Flex>
                            </Th>
                          ))}
                        </Tr>
                      </Thead>
                      <Tbody>
                        {transformedData && transformedData.length > 0 ? (
                          transformedData.map((rowData, rowIndex) => (
                            <Fragment key={rowIndex}>
                              <Tr>
                                <Td>{rowData.id || 'N/A'}</Td>
                                <Td>
                                  {rowData.device_name && rowData.device_name.length > 20 ? (
                                    <Tooltip label={rowData.device_name} placement="top">
                                      <Box
                                        maxW="100px"
                                        whiteSpace="nowrap"
                                        overflow="hidden"
                                        textOverflow="ellipsis"
                                        cursor="pointer"
                                        onDoubleClick={() => {
                                          navigator.clipboard.writeText(rowData.device_name);
                                        }}
                                      >
                                        {rowData.device_name.slice(0, 20)}...
                                      </Box>
                                    </Tooltip>
                                  ) : (
                                    rowData.device_name || 'N/A'
                                  )}
                                </Td>
                                <Td>{rowData.entitlement || 'N/A'}</Td>
                                <Td>{rowData.status || 'N/A'}</Td>
                                <Td>
                                  {rowData.type && rowData.type.length > 20 ? (
                                    <Tooltip label={rowData.type} placement="top">
                                      <Box
                                        maxW="100px"
                                        whiteSpace="nowrap"
                                        overflow="hidden"
                                        textOverflow="ellipsis"
                                        cursor="pointer"
                                        onDoubleClick={() => {
                                          navigator.clipboard.writeText(rowData.type);
                                        }}
                                      >
                                        {rowData.type.slice(0, 20)}...
                                      </Box>
                                    </Tooltip>
                                  ) : (
                                    rowData.type || 'N/A'
                                  )}
                                </Td>
                              </Tr>
                            </Fragment>
                          ))
                        ) : (
                          <Tr>
                            <Td colSpan={5}>No data available</Td>
                          </Tr>
                        )}
                      </Tbody>
                    </Table>
                  </Box>
                </AccordionPanel>
              </AccordionItem>
            </Accordion>
          ) : null}
          {/* Certificates */}
          {type === 'Certificates' ? (
            <Accordion defaultIndex={[0]} allowMultiple>
              <AccordionItem>
                <AccordionButton>
                  <Text width="100%">View certificates data</Text>
                  <AccordionIcon />
                </AccordionButton>
                <AccordionPanel pb="4">
                  <Box overflowX="auto">
                    <Table variant="unstyled" size="sm" width="100%">
                      <Thead>
                        <Tr>
                          {certificatesColumns.map((column) => (
                            <Th key={column.id} background="gray.50">
                              <Flex>
                                <Box ml="2">{column.header}</Box>
                              </Flex>
                            </Th>
                          ))}
                        </Tr>
                      </Thead>
                      {/* Certificates */}
                      <Tbody>
                        {data && data.length > 0 ? (
                          data
                            .filter((row: FortinetFirewallCertificatesData) => row && typeof row === 'object')
                            .map((rowData: FortinetFirewallCertificatesData, rowIndex: number) => (
                              <Fragment key={rowIndex}>
                                <Tr>
                                  <Td>
                                    {rowData.name && rowData.name.length > 20 ? (
                                      <Tooltip label={rowData.name} placement="top">
                                        <Box
                                          maxW="100px"
                                          whiteSpace="nowrap"
                                          overflow="hidden"
                                          textOverflow="ellipsis"
                                          cursor="pointer"
                                          onDoubleClick={() => {
                                            navigator.clipboard.writeText(rowData.name);
                                          }}
                                        >
                                          {rowData.name.slice(0, 20)}...
                                        </Box>
                                      </Tooltip>
                                    ) : (
                                      rowData.name || 'N/A'
                                    )}
                                  </Td>
                                  <Td>{rowData.issuer.C || 'N/A'}</Td>
                                  <Td>
                                    {rowData.issuer.CN && rowData.issuer.CN.length > 20 ? (
                                      <Tooltip label={rowData.issuer.CN} placement="top">
                                        <Box
                                          maxW="100px"
                                          whiteSpace="nowrap"
                                          overflow="hidden"
                                          textOverflow="ellipsis"
                                          cursor="pointer"
                                          onDoubleClick={() => {
                                            navigator.clipboard.writeText(rowData.issuer.CN);
                                          }}
                                        >
                                          {rowData.issuer.CN.slice(0, 20)}...
                                        </Box>
                                      </Tooltip>
                                    ) : (
                                      rowData.issuer.CN || 'N/A'
                                    )}
                                  </Td>
                                  <Td>
                                    {rowData.issuer.O && rowData.issuer.O.length > 20 ? (
                                      <Tooltip label={rowData.issuer.O} placement="top">
                                        <Box
                                          maxW="100px"
                                          whiteSpace="nowrap"
                                          overflow="hidden"
                                          textOverflow="ellipsis"
                                          cursor="pointer"
                                          onDoubleClick={() => {
                                            navigator.clipboard.writeText(rowData.issuer.O);
                                          }}
                                        >
                                          {rowData.issuer.O.slice(0, 20)}...
                                        </Box>
                                      </Tooltip>
                                    ) : (
                                      rowData.issuer.O || 'N/A'
                                    )}
                                  </Td>
                                  <Td>{rowData.type || 'N/A'}</Td>
                                  <Td>{rowData.source || 'N/A'}</Td>
                                  <Td>{rowData.status || 'N/A'}</Td>
                                  <Td>{formatInReadableTimeDate(rowData.valid_to_raw) || 'N/A'}</Td>
                                  <Td>{formatInReadableTimeDate(rowData.valid_from_raw) || 'N/A'}</Td>
                                </Tr>
                              </Fragment>
                            ))
                        ) : (
                          <Tr>
                            <Td colSpan={4}>No data available</Td>
                          </Tr>
                        )}
                      </Tbody>
                    </Table>
                  </Box>
                </AccordionPanel>
              </AccordionItem>
            </Accordion>
          ) : null}
        </Box>
      )}
    </>
  );
};

export default FortinetBoxFirewall;
