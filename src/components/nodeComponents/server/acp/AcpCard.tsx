import { ExternalLinkIcon } from '@chakra-ui/icons';
import { NODE_COMPONENT_TITLES, StatusToColor } from '../../../../data/constants';
import { StaticStatusCircleIcon } from '../../../icons/StatusIcon';
import ComponentModal from '../../ComponentModal';
import { UtilizationBar } from '../../common/UtilizationBar';
import { BsArrowReturnRight } from 'react-icons/bs';
import {
  Badge,
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  Icon,
  Link,
  Stack,
  StackDivider,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { useEffect, useState, useCallback, useRef } from 'react';
import { getStatusColor } from '../../../../pages/CellOverview/hooks/useStatus';
import { recursiveSearch } from '../../../../utils/recursiveSearch';

import { getComponentBgColor } from '../../utils';
import useCreateServerTask, {
  useGetServerTasks,
  useGetServerTaskByTaskId,
} from '../../../../pages/CellOverview/hooks/services/use_Orc_ServerTasks';
import { MdRefresh, MdAutorenew, MdCameraAlt, MdPlayArrow, MdStop, MdCheckCircle, MdWarning } from 'react-icons/md';
import ServerTasks from './ServerTasks';
import { BackupDetails } from './BackupDetails';
import ServicesStatusCard from './ServicesStatusCard';
import DiskUsageCard from './DiskUsageCard';
import { BasicInfo } from './BasicInfo';
import LicenceInfoCard from './LicenceInfoCard';
import CpuUsageCard from './CpuUsageCard';
import MemoryUsageCard from './MemoryUsageCard';
type ActionType = 'reboot' | 'recover' | 'takeSnapshot' | 'startBackup' | 'stopBackup';

type ActionIconType = {
  [key in ActionType]: JSX.Element;
};

const actionIcon: ActionIconType = {
  reboot: <MdRefresh size="25" />,
  recover: <MdAutorenew size="25" />,
  takeSnapshot: <MdCameraAlt size="25" />,
  startBackup: <MdPlayArrow size="25" />,
  stopBackup: <MdStop size="25" />,
};

interface Task {
  id: string;
  task_status: string;
  task_result: { reason: string };
  task_type: string;
  task_created_at: string;
  task_updated_at: string;
}

export const TimeElapsed = ({ initialUptime, fontSize = 'sm' }: { initialUptime: string; fontSize?: string }) => {
  const initialUptimeRef = useRef(initialUptime);

  // Update ref when prop changes
  useEffect(() => {
    initialUptimeRef.current = initialUptime;
  }, [initialUptime]);

  const calculateDuration = useCallback(() => {
    const startDate = new Date(initialUptimeRef.current);
    const endDate = new Date();

    const seconds = Math.floor((endDate.getTime() - startDate.getTime()) / 1000);
    const years = Math.floor(seconds / 31536000);
    const months = Math.floor((seconds % 31536000) / 2592000);
    const days = Math.floor(((seconds % 31536000) % 2592000) / 86400);
    const hours = Math.floor((((seconds % 31536000) % 2592000) % 86400) / 3600);
    const minutes = Math.floor(((((seconds % 31536000) % 2592000) % 86400) % 3600) / 60);
    const remainingSeconds = seconds % 60;

    return {
      years,
      months,
      days,
      hours,
      minutes,
      seconds: remainingSeconds,
    };
  }, []);

  const [uptime, setUptime] = useState(() => calculateDuration());

  useEffect(() => {
    // Reset the uptime when initialUptime changes
    setUptime(calculateDuration());

    const interval = setInterval(() => {
      setUptime(calculateDuration());
    }, 1000);

    return () => clearInterval(interval);
  }, [initialUptime, calculateDuration]);

  const formatUptime = (uptime: {
    years: number;
    months: number;
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  }) => {
    let result = '';
    if (uptime.years > 0) result += `${uptime.years} ${uptime.years === 1 ? 'year' : 'years'} `;
    if (uptime.months > 0) result += `${uptime.months} ${uptime.months === 1 ? 'month' : 'months'} `;
    if (uptime.days > 0) result += `${uptime.days} ${uptime.days === 1 ? 'day' : 'days'} `;

    // Always format hours, minutes, and seconds as two digits.
    const hours = uptime.hours.toString().padStart(2, '0');
    const minutes = uptime.minutes.toString().padStart(2, '0');
    const seconds = uptime.seconds.toString().padStart(2, '0');

    // Append the time part to the result string, ensuring it always displays even if 0.
    result += `${hours}:${minutes}:${seconds}`;
    return result;
  };

  const result = formatUptime(uptime);
  return <Text fontSize={fontSize}>{result}</Text>;
};

const AcpCard = ({ data, nodeId }: { data: any; nodeId: string }) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [showContent, setShowContent] = useState(false);
  const [taskType, setTaskType] = useState('');
  const [tasks, setTasks] = useState<Task[]>([]);
  const [acpStatus, setAcpStatus] = useState('unknown');
  const { isLoading: isLoadingGetRecentTask, error: getRecentTaskError, data: recentTasks } = useGetServerTasks(nodeId);

  const { status: createServerTaskStatus, data: createServerTaskData } = useCreateServerTask(nodeId, taskType);

  const task_id = createServerTaskData?.id || (tasks.length > 0 && tasks[0].id);
  const [task_status, setTaskStatus] = useState('');

  const acpUrl = data?.url && 'https://' + new URL(data.url).hostname;
  const { status: getServerTaskStatus, data: newlyCreatedServerTaskData } = useGetServerTaskByTaskId(
    nodeId,
    task_id,
    task_status
  );
  useEffect(() => {
    if (createServerTaskData) {
      setTaskStatus(createServerTaskData.task_status);
    }
  }, [createServerTaskData]);

  useEffect(() => {
    if (recentTasks?.length > 0) {
      setTasks(recentTasks);
      setTaskStatus(recentTasks[0].task_status);
    }
  }, [recentTasks]);

  useEffect(() => {
    if (newlyCreatedServerTaskData) {
      setTaskStatus(newlyCreatedServerTaskData.task_status);
      setTasks((prevTasks: Task[]) => {
        const taskExists = prevTasks.some((task) => task.id === newlyCreatedServerTaskData.id);

        if (taskExists) {
          return prevTasks.map((task) =>
            task.id === newlyCreatedServerTaskData.id ? newlyCreatedServerTaskData : task
          );
        } else {
          // remove the last task
          const Tasks: Task[] = prevTasks.slice(0, -1);
          return [newlyCreatedServerTaskData, ...Tasks];
        }
      });
      if (newlyCreatedServerTaskData.task_status === 'completed') {
        setAcpStatus(newlyCreatedServerTaskData?.task_result?.status);
      }
    }
  }, [newlyCreatedServerTaskData]);

  useEffect(() => {
    setAcpStatus(data.health_status);
  }, [data]);

  const isRecentTaskRunningOrPending = task_status === 'pending' || task_status === 'running';
  const handleClick = (taskType: string) => {
    setTaskType(taskType);
  };

  const flattenedData: any = recursiveSearch(data);

  const getDiskUsageNumber = (diskUsage: string | number): number => {
    if (typeof diskUsage === 'number') {
      return diskUsage;
    } else {
      return Number(diskUsage?.replace('%', ''));
    }
  };

  const statusColor = getStatusColor(acpStatus);
  const bgColor = getComponentBgColor(statusColor);
  return (
    <>
      <Card width="100%" borderRadius="lg" style={{ marginRight: '1rem' }} overflow="auto">
        <CardHeader
          _hover={{
            bg: bgColor,
            cursor: 'pointer',
            boxShadow: `0 0 12px ${bgColor}`,
          }}
          transition="background-color 0.2s ease, box-shadow 0.2s ease"
          onClick={() => setShowContent(!showContent)}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          h="70px"
          minH="70px"
        >
          <Heading size="xs" fontWeight="500" display="flex" justifyContent="space-between">
            <Flex alignItems="center">
              <Icon as={BsArrowReturnRight} />
              <Text ml={2}> {NODE_COMPONENT_TITLES.ACP}</Text>
            </Flex>
          </Heading>
          <StaticStatusCircleIcon size={40} color={StatusToColor[acpStatus as keyof typeof StatusToColor]} />
        </CardHeader>
        {showContent && (
          <CardBody borderTop={'1px solid #e2e2e2'}>
            <Stack divider={<StackDivider />} spacing="4">
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Text mt="2">Acp Name : {data?.manager_instance}</Text>

                <Stack direction="row" spacing={4} mt="1" ml="2">
                  <Button
                    isDisabled={!data?.acp_platform_agent_available || isRecentTaskRunningOrPending}
                    colorScheme="orange"
                    onClick={() => handleClick('reboot')}
                    size="sm"
                  >
                    <Text>Reboot </Text>
                    {actionIcon['reboot']}
                  </Button>
                  <Button
                    isDisabled={!data?.acp_platform_agent_available || isRecentTaskRunningOrPending}
                    colorScheme="red"
                    onClick={() => handleClick('recover')}
                    size="sm"
                  >
                    <Text>Recover</Text>
                    {actionIcon['recover']}
                  </Button>
                </Stack>
              </Box>

              {typeof data?.health_status_logs?.info === 'object' ? (
                <Flex justifyContent="space-between" alignItems="flex-end">
                  <Box width="70%" data-testid="basic-info">
                    <BasicInfo
                      info={data?.health_status_logs?.info}
                      license_valid={data?.health_status_logs?.license_valid}
                    />
                  </Box>
                  <Box display="flex" alignItems="center" justifyContent="center" width="30%" data-testid="acp-url">
                    <Link textAlign="center" isExternal rel="noopener noreferrer" href={acpUrl}>
                      ACP Hosted on
                      <ExternalLinkIcon mx="2px" boxSize="20px" color="teal" />
                    </Link>
                  </Box>
                </Flex>
              ) : (
                <Link textAlign="center" isExternal rel="noopener noreferrer" href={acpUrl}>
                  ACP Hosted on
                  <ExternalLinkIcon mx="2px" boxSize="20px" color="teal" />
                </Link>
              )}

              {data?.acp_platform_agent_available && (
                <>
                  <ServerTasks tasks={tasks} />
                  <BackupDetails
                    data={data}
                    isRecentTaskRunningOrPending={isRecentTaskRunningOrPending}
                    handleClick={handleClick}
                    actionIcon={actionIcon}
                  />
                </>
              )}

              {typeof data?.health_status_logs?.services_status === 'object' && (
                <ServicesStatusCard services_status={data?.health_status_logs?.services_status} />
              )}
              {typeof data?.health_status_logs?.disk_usage?.[0] === 'object' && (
                <DiskUsageCard details={data?.health_status_logs?.disk_usage} />
              )}
              {typeof data?.health_status_logs?.memory_usage === 'object' && (
                <MemoryUsageCard details={data?.health_status_logs?.memory_usage} />
              )}
              {typeof data?.health_status_logs?.cpu_usage === 'object' && (
                <CpuUsageCard details={data?.health_status_logs?.cpu_usage} />
              )}
              {typeof data?.health_status_logs?.license === 'object' && (
                <LicenceInfoCard licence_info={data?.health_status_logs?.license} />
              )}

              {data?.reason && (
                <Box pb={2}>
                  <Stack spacing={2} align="start" justifyContent="start" direction="row">
                    <Text>Reason :</Text>
                    <Badge whiteSpace="normal" color="hsl(1, 100%, 50%)">
                      {data.reason}
                    </Badge>
                  </Stack>
                </Box>
              )}

              <Button onClick={onOpen}>More Data</Button>
            </Stack>
          </CardBody>
        )}
      </Card>
      <ComponentModal isOpen={isOpen} onClose={onClose} flattenedData={flattenedData} />
    </>
  );
};

export default AcpCard;
