import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  Button,
  Text,
  Box,
  Alert,
  AlertIcon,
  VStack,
  HStack,
  Spinner,
  Flex,
} from '@chakra-ui/react';

import { useMutation, useQuery } from '@tanstack/react-query';
import { pingNode, PingResponse, PingErrorResponse, isPingError, isPingResult } from '../../../services/orchestrator';
import { FiCheckCircle, FiXCircle } from 'react-icons/fi';
import { formatInReadableTimeDate } from '../../../utils/formatInReadableTimeData';

interface PingModalProps {
  isOpen: boolean;
  onClose: () => void;
  nodeId: string;
  nodeType?: string;
}

const PingModal: React.FC<PingModalProps> = ({ isOpen, onClose, nodeId, nodeType }) => {
  const [pingResult, setPingResult] = useState<PingResponse | null>(null);

  // Auto-ping when modal opens
  const { data: autoPingResult, isLoading: isAutoPingLoading } = useQuery({
    queryKey: ['modalAutoPing', nodeId],
    queryFn: () => pingNode(nodeId),
    enabled: isOpen && !!nodeId,
    retry: false,
    staleTime: 30000, // Cache for 30 seconds
  });

  const pingMutation = useMutation({
    mutationFn: () => pingNode(nodeId),
    onSuccess: (data: PingResponse) => {
      setPingResult(data);
    },
    onError: (error) => {
      console.error('Ping failed:', error);
      // Create error response for display
      const errorResponse: PingErrorResponse = {
        utc_stamp: new Date().toISOString(),
        origin: 'nms-orchestrator',
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        status: 'FAILED',
        object: null,
      };
      setPingResult(errorResponse);
    },
  });

  const handlePingAgain = () => {
    pingMutation.mutate();
  };

  const handleModalClose = () => {
    setPingResult(null); // Reset ping result when closing
    onClose();
  };

  // Three-color status: good (green), error (red), no-ip (orange)
  const getButtonStatus = () => {
    // Check manual ping result first
    if (pingResult) {
      if (isPingError(pingResult)) {
        // Check if it's "No pingable components" error
        if (pingResult.message.includes('No pingable components')) return 'no-ip';
        return 'error';
      }
      if (isPingResult(pingResult) && pingResult.summary.loss === 0) return 'good';
      return 'error'; // Any packet loss = error state
    }

    // Then check auto-ping result
    if (autoPingResult) {
      if (isPingError(autoPingResult)) {
        // Check if it's "No pingable components" error
        if (autoPingResult.message.includes('No pingable components')) return 'no-ip';
        return 'error';
      }
      if (isPingResult(autoPingResult) && autoPingResult.summary.loss === 0) return 'good';
      return 'error';
    }

    return 'unknown';
  };

  const formatTime = (timeMs?: number | null) => {
    if (timeMs === null || timeMs === undefined) return 'N/A';
    return `${timeMs.toFixed(2)}ms`;
  };

  const getOverallStatus = (response: PingResponse): string => {
    if (isPingError(response)) return 'Error';
    if (isPingResult(response)) {
      const { summary } = response;
      if (summary.error) return 'Error';
      if (summary.loss === 100) return 'Failed';
      if (summary.loss > 0) return 'Partial';
      return 'Success';
    }
    return 'Unknown';
  };

  // Get current result to display (manual ping takes precedence)
  const currentResult = pingResult || autoPingResult;

  return (
    <Modal isOpen={isOpen} onClose={handleModalClose} size="2xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <Flex justifyContent="space-between" alignItems="center">
            <Text fontSize="2xl">
              Ping Results - ({nodeType}) {nodeId}
            </Text>
          </Flex>
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <VStack spacing={4} align="stretch">
            {(pingMutation.isLoading || isAutoPingLoading) && (
              <Box textAlign="center" py={4}>
                <Spinner size="lg" color="blue.500" />
                <Text mt={2}>Pinging node...</Text>
              </Box>
            )}

            {!pingMutation.isLoading && !isAutoPingLoading && !currentResult && (
              <Box textAlign="center" py={4}>
                <Text color="gray.500" fontSize="sm">
                  No ping data available yet. Click &quot;Ping Now&quot; to test connectivity.
                </Text>
              </Box>
            )}

            {currentResult && !pingMutation.isLoading && !isAutoPingLoading && (
              <>
                <Alert status={getButtonStatus() === 'good' ? 'success' : 'error'} gap={1}>
                  {getButtonStatus() === 'good' ? (
                    <FiCheckCircle color="green" size="30px" />
                  ) : (
                    <FiXCircle color="red" size="30px" />
                  )}

                  <Text fontWeight="bold">Ping {getOverallStatus(currentResult)}</Text>
                </Alert>

                {isPingError(currentResult) ? (
                  // Error case - no pingable components
                  <Box p={4} borderWidth="1px" borderRadius="md" borderColor="red.500">
                    <VStack align="stretch" spacing={2}>
                      <Alert status="error" bg="white" borderRadius="md" borderColor="red.500">
                        <AlertIcon />
                        <Text fontSize="sm" color="red.500" whiteSpace="pre-line" wordBreak="break-word">
                          {currentResult.message}
                        </Text>
                      </Alert>
                      <Text fontSize="xs" color="gray.500">
                        Error occurred at: {formatInReadableTimeDate(currentResult.utc_stamp).toLocaleString()}
                      </Text>
                    </VStack>
                  </Box>
                ) : isPingResult(currentResult) ? (
                  // Success case - show detailed ping results
                  currentResult.summary.error ? (
                    <Box p={4} borderWidth="1px" borderRadius="md" borderColor="red.500">
                      <VStack align="stretch" spacing={3}>
                        <Alert status="error">
                          <AlertIcon />
                          <Text fontSize="sm" color="red.500" whiteSpace="pre-line" wordBreak="break-word">
                            {currentResult.summary.error.message.detail}
                          </Text>
                        </Alert>
                        <Text fontSize="xs" color="gray.500">
                          Error occurred at:{' '}
                          {formatInReadableTimeDate(currentResult.summary.utc_stamp || '').toLocaleString()}
                        </Text>
                      </VStack>
                    </Box>
                  ) : (
                    <Box
                      p={4}
                      borderWidth="1px"
                      borderRadius="md"
                      borderColor={currentResult.summary.loss === 0 ? 'green.500' : 'red.500'}
                    >
                      <VStack align="stretch" spacing={4}>
                        {/* Ping Summary */}
                        <Box>
                          <Text fontWeight="bold" mb={2}>
                            Ping Summary
                          </Text>
                          <VStack align="stretch" spacing={1} fontSize="sm">
                            <HStack justify="space-between">
                              <Text fontWeight="semibold">Target IP:</Text>
                              <Text>{currentResult.summary.ip_address}</Text>
                            </HStack>
                            <HStack justify="space-between">
                              <Text fontWeight="semibold">Packets Sent:</Text>
                              <Text>{currentResult.summary.sent}</Text>
                            </HStack>
                            <HStack justify="space-between">
                              <Text fontWeight="semibold">Packets Received:</Text>
                              <Text>{currentResult.summary.received}</Text>
                            </HStack>
                            <HStack justify="space-between">
                              <Text fontWeight="semibold">Packet Loss:</Text>
                              <Text color={currentResult.summary.loss === 0 ? 'green.600' : 'red.600'}>
                                {currentResult.summary.loss}%
                              </Text>
                            </HStack>
                            {currentResult.summary.min_rtt > 0 && (
                              <>
                                <HStack justify="space-between">
                                  <Text fontWeight="semibold">Min RTT:</Text>
                                  <Text>{formatTime(currentResult.summary.min_rtt)}</Text>
                                </HStack>
                                <HStack justify="space-between">
                                  <Text fontWeight="semibold">Avg RTT:</Text>
                                  <Text color={currentResult.summary.loss === 0 ? 'green.600' : 'red.600'}>
                                    {formatTime(currentResult.summary.avg_rtt)}
                                  </Text>
                                </HStack>
                                <HStack justify="space-between">
                                  <Text fontWeight="semibold">Max RTT:</Text>
                                  <Text>{formatTime(currentResult.summary.max_rtt)}</Text>
                                </HStack>
                              </>
                            )}
                          </VStack>
                        </Box>

                        {/* Individual Ping Results */}
                        {currentResult.summary.results && currentResult.summary.results.length > 0 && (
                          <Box>
                            <Text fontWeight="bold" mb={2}>
                              Individual Ping Results
                            </Text>
                            <VStack align="stretch" spacing={2}>
                              {currentResult.summary.results.map((result, index) => (
                                <Box key={index} p={2} bg="white" borderRadius="md" borderWidth="1px">
                                  <HStack justify="space-between" fontSize="sm">
                                    <Text>Ping #{index + 1}</Text>
                                    {result.timeout ? (
                                      <Text color="red.600" fontWeight="semibold">
                                        TIMEOUT
                                      </Text>
                                    ) : result.send_fail ? (
                                      <Text color="red.600" fontWeight="semibold">
                                        SEND FAILED
                                      </Text>
                                    ) : (
                                      <Text color="green.600" fontWeight="semibold">
                                        {formatTime(result.rtt)} (TTL: {result.ttl})
                                      </Text>
                                    )}
                                  </HStack>
                                  {result.description && (
                                    <Text fontSize="xs" color="gray.600" mt={1}>
                                      {result.description}
                                    </Text>
                                  )}
                                </Box>
                              ))}
                            </VStack>
                          </Box>
                        )}
                      </VStack>
                    </Box>
                  )
                ) : null}
              </>
            )}
          </VStack>
        </ModalBody>
        <ModalFooter>
          <Button colorScheme="blue" onClick={handlePingAgain} isDisabled={pingMutation.isLoading} size="md" mr={3}>
            {pingMutation.isLoading ? 'Pinging...' : currentResult ? 'Ping Again' : 'Ping Now'}
          </Button>
          <Button onClick={handleModalClose}>Close</Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default PingModal;
