import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  <PERSON>over<PERSON>ontent,
  <PERSON>overHeader,
  PopoverBody,
  PopoverArrow,
  PopoverCloseButton,
  Button,
  Text,
  Box,
  Alert,
  AlertIcon,
  VStack,
  HStack,
  Spinner,
  Flex,
  IconButton,
} from '@chakra-ui/react';

import { useMutation, useQuery } from '@tanstack/react-query';
import { pingNode, PingResponse, PingErrorResponse, isPingError, isPingResult } from '../../../services/orchestrator';
import { FaWifi } from 'react-icons/fa';
import { FiCheckCircle, FiXCircle } from 'react-icons/fi';
import { formatInReadableTimeDate } from '../../../utils/formatInReadableTimeData';

interface PingButtonProps {
  nodeId: string;
  disabled?: boolean;
  autoEnable?: boolean;
}

const PingButton: React.FC<PingButtonProps> = ({ nodeId, disabled = false, autoEnable = true }) => {
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [pingResult, setPingResult] = useState<PingResponse | null>(null);

  // Auto-ping on component mount
  const { data: autoPingResult, isLoading: isAutoPingLoading } = useQuery({
    queryKey: ['autoPing', nodeId],
    queryFn: () => pingNode(nodeId),
    enabled: autoEnable && !!nodeId,
    retry: false,
    staleTime: 30000, // Cache for 30 seconds
  });

  const pingMutation = useMutation({
    mutationFn: () => pingNode(nodeId),
    onSuccess: (data: PingResponse) => {
      setPingResult(data);
      // Don't auto-open popover, let user click to see results
    },
    onError: (error) => {
      console.error('Ping failed:', error);
      // Create error response for display
      const errorResponse: PingErrorResponse = {
        utc_stamp: new Date().toISOString(),
        origin: 'nms-orchestrator',
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        status: 'FAILED',
        object: null,
      };
      setPingResult(errorResponse);
    },
  });

  const handlePing = (event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent node row expansion
    setIsPopoverOpen(!isPopoverOpen);
  };

  const handlePingAgain = (event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }
    pingMutation.mutate();
  };

  const handlePopoverClose = () => {
    setIsPopoverOpen(false);
  };

  const handlePopoverContainerClick = (event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent any row expansion from popover interactions
  };

  // Three-color status: good (green), error (red), no-ip (orange)
  const getButtonStatus = () => {
    // Check manual ping result first
    if (pingResult) {
      if (isPingError(pingResult)) {
        // Check if it's "No pingable components" error
        if (pingResult.message.includes('No pingable components')) return 'no-ip';
        return 'error';
      }
      if (isPingResult(pingResult) && pingResult.summary.loss === 0) return 'good';
      return 'error'; // Any packet loss = error state
    }

    // Then check auto-ping result
    if (autoPingResult) {
      if (isPingError(autoPingResult)) {
        // Check if it's "No pingable components" error
        if (autoPingResult.message.includes('No pingable components')) return 'no-ip';
        return 'error';
      }
      if (isPingResult(autoPingResult) && autoPingResult.summary.loss === 0) return 'good';
      return 'error';
    }

    return 'unknown';
  };

  const getButtonColorScheme = (status: string) => {
    switch (status) {
      case 'good':
        return 'green';
      case 'error':
        return 'red';
      case 'no-ip':
        return 'gray';
      default:
        return 'black';
    }
  };

  const formatTime = (timeMs?: number | null) => {
    if (timeMs === null || timeMs === undefined) return 'N/A';
    return `${timeMs.toFixed(2)}ms`;
  };

  const getOverallStatus = (response: PingResponse): string => {
    if (isPingError(response)) return 'Error';
    if (isPingResult(response)) {
      const { summary } = response;
      if (summary.error) return 'Error';
      if (summary.loss === 100) return 'Failed';
      if (summary.loss > 0) return 'Partial';
      return 'Success';
    }
    return 'Unknown';
  };

  // Get current result to display (manual ping takes precedence)
  const currentResult = pingResult || autoPingResult;

  return (
    <>
      <Popover isOpen={isPopoverOpen} onClose={handlePopoverClose} placement="auto">
        <PopoverTrigger>
          <span>
            <Tooltip
              label={
                getButtonStatus() === 'good'
                  ? `Good connectivity`
                  : getButtonStatus() === 'error'
                  ? `Ping error`
                  : getButtonStatus() === 'no-ip'
                  ? `No IP address`
                  : `Ping node`
              }
              placement="top"
            >
              {isAutoPingLoading || pingMutation.isLoading ? (
                <Spinner size="md" color="blue.500" />
              ) : (
                <Button
                  bg={`${getButtonColorScheme(getButtonStatus()).toString()}.500`}
                  color="white"
                  variant="solid"
                  size="sm"
                  p="2"
                  shadow="lg"
                  onClick={handlePing}
                  _hover={{ opacity: 0.9, transform: 'translateY(-5px)' }}
                  _disabled={{ bg: getButtonColorScheme(getButtonStatus()), opacity: 0.3, cursor: 'not-allowed' }}
                  _focus={{ boxShadow: 'lg' }}
                  isDisabled={disabled}
                  boxShadow="5px 5px 5px gray"
                >
                  {isAutoPingLoading || pingMutation.isLoading ? (
                    <Spinner size="sm" color="blue.500" />
                  ) : (
                    <FaWifi size="20px" />
                  )}
                </Button>
              )}
            </Tooltip>
          </span>
        </PopoverTrigger>

        <PopoverContent width="500px" onClick={handlePopoverContainerClick} p="2">
          <PopoverArrow />
          <PopoverCloseButton
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              handlePopoverClose();
            }}
            onMouseDown={(e) => e.stopPropagation()}
          />
          <PopoverHeader onClick={handlePopoverContainerClick}>
            <Flex justifyContent="left" width="100%" fontSize="lg">
              <HStack>
                <Text>Ping Results - {nodeId}</Text>
              </HStack>
            </Flex>
          </PopoverHeader>
          <PopoverBody onClick={handlePopoverContainerClick}>
            <VStack spacing={4} align="stretch">
              {(pingMutation.isLoading || isAutoPingLoading) && (
                <Box textAlign="center" py={4}>
                  <Spinner size="lg" color="blue.500" />
                  <Text mt={2}>Pinging node...</Text>
                </Box>
              )}

              {!pingMutation.isLoading && !isAutoPingLoading && !currentResult && (
                <Box textAlign="center" py={4}>
                  <Text color="gray.500" fontSize="sm">
                    No ping data available yet. Click &quot;Ping Now&quot; to test connectivity.
                  </Text>
                </Box>
              )}

              {currentResult && !pingMutation.isLoading && !isAutoPingLoading && (
                <>
                  <Alert status={getButtonStatus() === 'good' ? 'success' : 'error'} gap={1}>
                    {getButtonStatus() === 'good' ? (
                      <FiCheckCircle color="green" size="30px" />
                    ) : (
                      <FiXCircle color="red" size="30px" />
                    )}

                    <Text fontWeight="bold">Ping {getOverallStatus(currentResult)}</Text>
                  </Alert>

                  {isPingError(currentResult) ? (
                    // Error case - no pingable components
                    <Box p={4} borderWidth="1px" borderRadius="md" borderColor="red.500">
                      <VStack align="stretch" spacing={2}>
                        <Alert status="error" bg="white" borderRadius="md" borderColor="red.500">
                          <AlertIcon />
                          <Text fontSize="sm" color="red.500" whiteSpace="pre-line" wordBreak="break-word">
                            {currentResult.message}
                          </Text>
                        </Alert>
                        <Text fontSize="xs" color="gray.500">
                          Error occurred at: {formatInReadableTimeDate(currentResult.utc_stamp).toLocaleString()}
                        </Text>
                      </VStack>
                    </Box>
                  ) : isPingResult(currentResult) ? (
                    // Success case - show detailed ping results
                    currentResult.summary.error ? (
                      <Box p={4} borderWidth="1px" borderRadius="md" borderColor="red.500">
                        <VStack align="stretch" spacing={3}>
                          <Alert status="error">
                            <AlertIcon />
                            <Text fontSize="sm" color="red.500" whiteSpace="pre-line" wordBreak="break-word">
                              {currentResult.summary.error.message.detail}
                            </Text>
                          </Alert>
                          <Text fontSize="xs" color="gray.500">
                            Error occurred at:{' '}
                            {formatInReadableTimeDate(currentResult.summary.utc_stamp || '').toLocaleString()}
                          </Text>
                        </VStack>
                      </Box>
                    ) : (
                      <Box
                        p={4}
                        borderWidth="1px"
                        borderRadius="md"
                        borderColor={currentResult.summary.loss === 0 ? 'green.500' : 'red.500'}
                      >
                        <VStack align="stretch" spacing={4}>
                          {/* Ping Summary */}
                          <Box>
                            <Text fontWeight="bold" mb={2}>
                              Ping Summary
                            </Text>
                            <VStack align="stretch" spacing={1} fontSize="sm">
                              <HStack justify="space-between">
                                <Text fontWeight="semibold">Target IP:</Text>
                                <Text>{currentResult.summary.ip_address}</Text>
                              </HStack>
                              <HStack justify="space-between">
                                <Text fontWeight="semibold">Packets Sent:</Text>
                                <Text>{currentResult.summary.sent}</Text>
                              </HStack>
                              <HStack justify="space-between">
                                <Text fontWeight="semibold">Packets Received:</Text>
                                <Text>{currentResult.summary.received}</Text>
                              </HStack>
                              <HStack justify="space-between">
                                <Text fontWeight="semibold">Packet Loss:</Text>
                                <Text color={currentResult.summary.loss === 0 ? 'green.600' : 'red.600'}>
                                  {currentResult.summary.loss}%
                                </Text>
                              </HStack>
                              {currentResult.summary.min_rtt > 0 && (
                                <>
                                  <HStack justify="space-between">
                                    <Text fontWeight="semibold">Min RTT:</Text>
                                    <Text>{formatTime(currentResult.summary.min_rtt)}</Text>
                                  </HStack>
                                  <HStack justify="space-between">
                                    <Text fontWeight="semibold">Avg RTT:</Text>
                                    <Text color={currentResult.summary.loss === 0 ? 'green.600' : 'red.600'}>
                                      {formatTime(currentResult.summary.avg_rtt)}
                                    </Text>
                                  </HStack>
                                  <HStack justify="space-between">
                                    <Text fontWeight="semibold">Max RTT:</Text>
                                    <Text>{formatTime(currentResult.summary.max_rtt)}</Text>
                                  </HStack>
                                </>
                              )}
                            </VStack>
                          </Box>

                          {/* Individual Ping Results */}
                          {currentResult.summary.results && currentResult.summary.results.length > 0 && (
                            <Box>
                              <Text fontWeight="bold" mb={2}>
                                Individual Ping Results
                              </Text>
                              <VStack align="stretch" spacing={2}>
                                {currentResult.summary.results.map((result, index) => (
                                  <Box key={index} p={2} bg="white" borderRadius="md" borderWidth="1px">
                                    <HStack justify="space-between" fontSize="sm">
                                      <Text>Ping #{index + 1}</Text>
                                      {result.timeout ? (
                                        <Text color="red.600" fontWeight="semibold">
                                          TIMEOUT
                                        </Text>
                                      ) : result.send_fail ? (
                                        <Text color="red.600" fontWeight="semibold">
                                          SEND FAILED
                                        </Text>
                                      ) : (
                                        <Text color="green.600" fontWeight="semibold">
                                          {formatTime(result.rtt)} (TTL: {result.ttl})
                                        </Text>
                                      )}
                                    </HStack>
                                    {result.description && (
                                      <Text fontSize="xs" color="gray.600" mt={1}>
                                        {result.description}
                                      </Text>
                                    )}
                                  </Box>
                                ))}
                              </VStack>
                            </Box>
                          )}
                        </VStack>
                      </Box>
                    )
                  ) : null}
                </>
              )}
            </VStack>
            {/* Ping Again Button */}
            <Box mt={4} textAlign="center">
              <Button
                colorScheme="blue"
                onClick={handlePingAgain}
                onMouseDown={(e) => e.stopPropagation()}
                isDisabled={pingMutation.isLoading}
                size="md"
              >
                {pingMutation.isLoading ? 'Pinging...' : currentResult ? 'Ping Again' : 'Ping Now'}
              </Button>
            </Box>
          </PopoverBody>
        </PopoverContent>
      </Popover>
    </>
  );
};

export default PingButton;
