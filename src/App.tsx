import { Box } from '@chakra-ui/react';
import { BrowserRouter, Route, Routes } from 'react-router-dom';
import Plmn from './components/cellCreation/CellPlmn';
import CreateCellPage from './components/cellCreation/CreateCellPage';
import CreateNodePage from './components/cellCreation/createNode/CreateNodePage';
import ErrorPage from './components/errorComponents/ErrorPage';
import Loader from './components/loader/Loader';
import AddNodeToCells from './components/nodeCreation/AddNodeToCells';
import EditNode from './components/nodeCreation/EditNode';
import ScrollToTop from './components/scrollToTop/ScrollToTop';
import useFetchSettings from './hooks/useFetchSettings';
import Layout from './layouts/Layout';
import CellOverview from './pages/CellOverview';
import Landing from './pages/Landing';
import Login from './pages/Login';
import AdvancedView from './pages/OranDuCuManager/advancedView/AdvancedView';
import Clusters from './pages/OranDuCuManager/advancedView/Clusters';
import ConfigSets from './pages/OranDuCuManager/advancedView/ConfigSets';
import CustomResources from './pages/OranDuCuManager/advancedView/CustomResources';
import AdvancedViewPods from './pages/OranDuCuManager/advancedView/AdvancedViewPods';
import CreateConfigSet from './pages/OranDuCuManager/formFields/advancedView/configSetForms/CreateConfigSet';
import CreateConfigCustomResource from './pages/OranDuCuManager/formFields/advancedView/customResourceForms/CreateConfigCustomResource';
import CreateDeploymentCustomResource from './pages/OranDuCuManager/formFields/advancedView/customResourceForms/CreateDeploymentCustomResource';
import OranDuCuManager from './pages/OranDuCuManager/OranDuCuManager';
import DeploymentCell from './pages/OranDuCuManager/simpleView/DeploymentCell';
import { default as Identities } from './pages/OranDuCuManager/simpleView/Identities';
import Interfaces from './pages/OranDuCuManager/simpleView/Interfaces';
import SimpleView from './pages/OranDuCuManager/simpleView/SimpleView';
import PrivateRoutes from './pages/PrivateRoutes';
import SiteManager from './pages/SiteManager/SiteManager';
import CBSDOverview from './pages/OranDuCuManager/cbrsManagement/CBRSOverview';
import CBSDFormPage from './pages/OranDuCuManager/cbrsManagement/CBSD/CBSDFormPage';
import CPI from './pages/OranDuCuManager/cbrsManagement/CPI/CPI';
import SimpleViewPods from './pages/OranDuCuManager/simpleView/SimpleViewPods';
import ManifestOverview from './pages/Manifests/ManifestOverview';
import SoftwareUpgrade from './pages/SoftwareUpgrade/SoftwareUpgrade';
import CreateRolloutPage from './pages/SoftwareUpgrade/createRollout/CreateRolloutPage';
import CustomResourceParameters from './pages/OranDuCuManager/simpleView/CustomResourceParameters';
import Deployment from './pages/OranDuCuManager/simpleView/Deployment';
import CreateNetworkManifest from './pages/Manifests/createNetworkManifest/CreateNetworkManifest';
import AlarmsAndEvents from './pages/MetricsCollector';
import DeploymentUseCases from './pages/OranDuCuManager/simpleView/DeploymentUseCases';

function App() {
  const { isLoading, error } = useFetchSettings();

  if (isLoading) {
    return (
      <Box>
        <Loader />
      </Box>
    );
  }

  if (error) {
    return (
      <Box>
        <ErrorPage message={error} />
      </Box>
    );
  }
  return (
    <BrowserRouter future={{ v7_startTransition: true }}>
      <ScrollToTop />
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Landing />} />
          <Route path="/login" element={<Login />} />
          <Route
            path="/cell-overview"
            element={
              <PrivateRoutes>
                <CellOverview />
              </PrivateRoutes>
            }
          />
          <Route
            path="/cell-overview/:tab/"
            element={
              <PrivateRoutes>
                <CellOverview />
              </PrivateRoutes>
            }
          />
          <Route
            path="/cell-overview/:tab/:id"
            element={
              <PrivateRoutes>
                <CellOverview />
              </PrivateRoutes>
            }
          />
          <Route
            path="/cell-overview/create"
            element={
              <PrivateRoutes>
                <CreateCellPage />
              </PrivateRoutes>
            }
          />
          <Route
            path="/cell-overview/create/node"
            element={
              <PrivateRoutes>
                <CreateNodePage />
              </PrivateRoutes>
            }
          />
          <Route
            path="/cell-overview/edit"
            element={
              <PrivateRoutes>
                <CreateCellPage />
              </PrivateRoutes>
            }
          />
          <Route
            path="/cell-overview/plmn"
            element={
              <PrivateRoutes>
                <Plmn />
              </PrivateRoutes>
            }
          />
          <Route
            path="/cell-overview/editnode"
            element={
              <PrivateRoutes>
                <EditNode />
              </PrivateRoutes>
            }
          />
          <Route
            path="/cell-overview/nodes/manage-cell-node"
            element={
              <PrivateRoutes>
                <AddNodeToCells />
              </PrivateRoutes>
            }
          />
          <Route
            path="/alarms-and-events/:tab/:id?"
            element={
              <PrivateRoutes>
                <AlarmsAndEvents />
              </PrivateRoutes>
            }
          />
          <Route
            path="/site-manager"
            element={
              <PrivateRoutes>
                <SiteManager />
              </PrivateRoutes>
            }
          />
          <Route
            path="/site-manager/:tab/"
            element={
              <PrivateRoutes>
                <SiteManager />
              </PrivateRoutes>
            }
          />
          <Route
            path="/manifest-overview/:tab"
            element={
              <PrivateRoutes>
                <ManifestOverview />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/"
            element={
              <PrivateRoutes>
                <OranDuCuManager />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/simple-view"
            element={
              <PrivateRoutes>
                <SimpleView />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/simple-view/deployment-cell"
            element={
              <PrivateRoutes>
                <DeploymentCell />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/simple-view/deployment-use-cases"
            element={
              <PrivateRoutes>
                <DeploymentUseCases />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/simple-view/deployment"
            element={
              <PrivateRoutes>
                <Deployment />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/simple-view/interfaces"
            element={
              <PrivateRoutes>
                <Interfaces />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/simple-view/identities"
            element={
              <PrivateRoutes>
                <Identities />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/simple-view/custom-resources-parameters"
            element={
              <PrivateRoutes>
                <CustomResourceParameters />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/simple-view/update-pods"
            element={
              <PrivateRoutes>
                <SimpleViewPods />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/advanced-view"
            element={
              <PrivateRoutes>
                <AdvancedView />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/cbrs-management/:tab/"
            element={
              <PrivateRoutes>
                <CBSDOverview />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/cbrs-management/cbsd/create"
            element={
              <PrivateRoutes>
                <CBSDFormPage />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/cbrs-management/cbsd/edit"
            element={
              <PrivateRoutes>
                <CBSDFormPage />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/cbrs-management/cpi/create"
            element={
              <PrivateRoutes>
                <CPI />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/cbrs-management/cpi/edit"
            element={
              <PrivateRoutes>
                <CPI />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/advanced-view/config-sets"
            element={
              <PrivateRoutes>
                <ConfigSets />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/advanced-view/config-sets/create-config-set"
            element={
              <PrivateRoutes>
                <CreateConfigSet />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/advanced-view/custom-resources"
            element={
              <PrivateRoutes>
                <CustomResources />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/advanced-view/custom-resources/create-config-custom-resource"
            element={
              <PrivateRoutes>
                <CreateConfigCustomResource />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/advanced-view/custom-resources/create-deployment-custom-resource"
            element={
              <PrivateRoutes>
                <CreateDeploymentCustomResource />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/advanced-view/pods"
            element={
              <PrivateRoutes>
                <AdvancedViewPods />
              </PrivateRoutes>
            }
          />
          <Route
            path="/oran-du-cu-manager/advanced-view/clusters"
            element={
              <PrivateRoutes>
                <Clusters />
              </PrivateRoutes>
            }
          />
          <Route
            path="/software-upgrade/:tab/"
            element={
              <PrivateRoutes>
                <SoftwareUpgrade />
              </PrivateRoutes>
            }
          />
          <Route
            path="/software-upgrade/:tab/:id"
            element={
              <PrivateRoutes>
                <SoftwareUpgrade />
              </PrivateRoutes>
            }
          />
          <Route
            path="/software-upgrade/rollout/create"
            element={
              <PrivateRoutes>
                <CreateRolloutPage />
              </PrivateRoutes>
            }
          />
          <Route
            path="/manifest-overview/add-network-manifest"
            element={
              <PrivateRoutes>
                <CreateNetworkManifest />
              </PrivateRoutes>
            }
          />
          <Route path="*" element={<ErrorPage message="404 - Page Not Found" />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
}

export default App;
