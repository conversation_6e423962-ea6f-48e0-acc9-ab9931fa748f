// Global color theme
export const LIGHT = 'light';
export const DARK = 'dark';

// Global colors
//TODO: Move these colors into the theme/foundation/token file
export const TRAFFIC_OK_GREEN = 'green.500';
export const TRAFFIC_WARNING_ORANGE = 'orange.500';
export const TRAFFIC_ERROR_RED = 'red.500';
export const TRAFFIC_SHUTDOWN_BLACK = 'black';
export const TRAFFIC_CRITICAL_PURPLE = 'purple.500';
export const TRAFFIC_UNKNOWN_GRAY = 'gray.500';
export const TRAFFIC_UNKNOWN_BLUE = 'gray.500';
export const TRAFFIC_DEFAULT = 'gray.500';
export const ACTION_BUTTON_COLOR = 'blue.500';

// Temp circle
//TODO: Move these colors into the theme/foundation/token file
export const OK_GREEN = 'green-500';
export const WARN_ORANGE = 'orange-500';
export const ERROR_RED = 'red-500';

// Cell table view status values - [ UNKNOWN, OK, WARNING, ERROR, SHUTDOWN, CRITICAL ]
export const OK = 'OK';
export const TRUE = 'True';
export const FALSE = 'False';
export const WARNING = 'WARNING';
export const ERROR = 'ERROR';
export const SHUTDOWN = 'SHUTDOWN';
export const UNKNOWN = 'UNKNOWN';
export const CRITICAL = 'CRITICAL';

// Cell table view sort
//TODO: Move these to the component folder
export const ASC = 'asc';
export const DESC = 'desc';
export const CELL_VERSION = 'cell_version';
export const ORAN_SPLIT = 'oran_split';
export const COUNTRY_CODE = 'country_code';
export const COUNTRY_NAME = 'country_name';
export const SITE_NAME = 'site_name';
export const CELL_ORIENTATION = 'Orientation';
export const REGION_NAME = 'region_name';
export const REGION_CODE = 'region_code';
export const CELL_LIFECYCLE = 'cell_lifecycle';
export const CELL_LIFECYCLE_NUM = 'cell_lifecycle_num';
export const CELL_STATUS = 'cell_status';
export const CELL_STATUS_NUM = 'cell_status_num';
export const CLUSTER_ID = 'cluster_id';
export const LATITUDE = 'latitude';
export const LONGITUDE = 'longitude';
export const LIFECYCLE = 'lifecycle';

// SSE Network Status
export const SUCCESS = 'SUCCESS'; // All good updates applied
export const FAILED = 'FAILED'; // NO good updates NOT applied
export const PENDING = 'PENDING'; // Still waiting for updates to be applied, keep spinning.
export const CANCELLED = 'CANCELLED'; // Was not able to apply updates, but not sure why. component is usable again.

export const NODE_SERIAL_NO = 'node_serial_no';
export const SITE_ID = 'site_id';
export const HAS_MANIFEST = 'has_manifest';

export enum O_RAN_SPLIT {
  gNodeB = 'gNodeB',
  Split6 = 'Split 6',
  eNodeB = 'eNodeB',
  Oran = 'ORAN',
}

export enum SELECTED_NODE_TYPE {
  server = 'server',
  network = 'network',
  radio = 'radio',
}

export enum LIFE_CYCLE {
  FACTORY = 'FACTORY',
  PLANNING = 'PLANNING',
  STAGING = 'STAGING',
  COMMISSIONING = 'COMMISSIONING',
  OPERATIONAL = 'OPERATIONAL',
  DECOMMISSIONED = 'DECOMMISSIONED',
  UNSET = 'UNSET',
  LAB = 'LAB',
  FAULTY = 'FAULTY',
}

export function lifecycleToApiValue(lifecycle: LIFE_CYCLE): string | null {
  return lifecycle === LIFE_CYCLE.UNSET ? null : lifecycle;
}

export enum NODE_COMPONENT_NAMES {
  BLUWIRELESS = 'BLUWIRELESS',
  AIRSPAN = 'AIRSPAN',
  FIBROLAN = 'FIBROLAN',
  PARTICLE = 'PARTICLE',
}

export enum NODE_COMPONENT_TITLES {
  CU_UP_5G = 'CU User plane',
  CU_CP_5G = 'CU Control plane',
  DU_5G = 'DU',
  RU_5G = 'RU',
  DU_POD = 'DU',
  CU_UP_POD = 'CU User plane',
  CU_CP_POD = 'CU Control plane',
  ORAN_RU = 'RU',
  NODE = 'NODE & XPU',
  CONTROLLER_BOARD = 'Controller board',
  SWITCH = 'Switch',
  MMWAVE = 'MMWave',
  SERVER = 'Server',
  VSR = 'VSR',
  DRUID = 'DRUID',
  NEXUS = 'NEXUS',
  ACP = 'ACP',
  FIREWALL = 'FireWall',
  GDCV = 'GDCV',
  GKE = 'GKE',
  POWER = 'Power',
  AIRSPAN4G = 'Airspan4G',
}

export enum NODE_TYPE {
  STREETCELL = 'StreetCell',
  MESH_ROOT = 'MeshRoot',
  Server = 'Server',
  Server_VM = 'Server-VM',
  GCP_VM = 'GCP-VM',
  DATACENTRE = 'Datacentre',
  GCP = 'GCP',
  SWITCH = 'Switch',
  FIREWALL = 'Firewall',
  GDCV = 'GDCV',
  GKE = 'GKE',
  POWER = 'Power',
  AIRSPAN4G = 'Airspan4G',
  DU = 'DU',
  CUUP = 'CUUP',
  CUCP = 'CUCP',
  RADIO = 'Radio',
}

export enum MANIFEST_TYPE {
  STREETCELL = 'streetcell',
  SERVER = 'server',
  CLUSTER = 'cluster',
  NETWORK = 'network',
  SWITCH = 'switch',
  AS4G = 'as4g',
}

// export enum ORIENTATION_NAMES {
//   North = 'North',
//   South = 'South',
//   East = 'East',
//   West = 'West',
//   North_East = 'North East',
//   North_West = 'North West',
//   South_East = 'South East',
//   South_West = 'South West',
//   Omni_directional = 'Omni-directional',
//   Unknown = 'UNKUnknown',
// }

export enum ORIENTATION {
  'North' = 'N',
  'South' = 'S',
  'East' = 'E',
  'West' = 'W',
  'Down' = 'Down',
  'North East' = 'NE',
  'North West' = 'NW',
  'South East' = 'SE',
  'South West' = 'SW',
  'Omni-directional' = '360',
  'Unknown' = 'UNK',
}

export enum ROLE_OF_NODE {
  RU = 'RU', //Radio Unit
  DU = 'DU', //Distributed Unit
  CU = 'CU', //Centralized Unit
  MMW = 'MMW', //Millimeter Wave
  VSR = 'VSR', //Virtualized Small cell Router
  CORE = 'CORE', //Core network node
  EMS = 'EMS', //Element Management System
  JUMP = 'JUMP', //Jump node
  SEGW = 'SEGW', //Security Gateway
  NHE = 'NHE', //Network Hub Equipment
  PKI = 'PKI', //Public Key Infrastructure
  PDN = 'PDN', //Packet Data Network
  UPS = 'UPS', //uninterupted power supply
}

export const ROLES_ARRAY = Object.values(ROLE_OF_NODE);

export const ROLE_OF_NODE_ARRAY = [
  { label: 'RU', value: 'RU' },
  { label: 'DU', value: 'DU' },
  { label: 'CU', value: 'CU' },
  { label: 'ENB', value: 'ENB' },
  { label: 'MMW', value: 'MMW' },
  { label: 'VSR', value: 'VSR' },
  { label: 'CUUP', value: 'CUUP' },
  { label: 'CUCP', value: 'CUCP' },
  { label: 'CORE', value: 'CORE' },
  { label: 'UPF', value: 'UPF' },
  { label: 'SWITCH', value: 'SW' },
  { label: 'EMS', value: 'EMS' },
  { label: 'PDN', value: 'PDN' },
  { label: 'SEGW', value: 'SEGW' },
  { label: 'PKI', value: 'PKI' },
  { label: 'NHE', value: 'NHE' },
  { label: 'JUMP', value: 'JUMP' },
  { label: 'NTP', value: 'NTP' },
  { label: 'FW', value: 'FW' },
  { label: 'VPN', value: 'VPN' },
  { label: 'UPS', value: 'UPS' },
  { label: 'ACP', value: 'ACP' },
];

export enum STATUS {
  CRITICAL = 'CRITICAL',
  ERROR = 'ERROR',
  WARNING = 'WARNING',
  OK = 'OK',
  SHUTDOWN = 'SHUTDOWN',
  UNKNOWN = 'UNKNOWN',
}

export enum LifeCycle {
  'FACTORY' = '0',
  'PLANNING' = '-1',
  'STAGING' = '1',
  'COMMISSIONING' = '2',
  'OPERATIONAL' = '3',
  'DECOMMISSIONED' = '4',
  'UNSET' = '-2',
}

export enum CUSTOM_RESOURCE_KINDS {
  CuCpDeployment = 'CuCpDeployment',
  CuUpDeployment = 'CuUpDeployment',
  Split6DuDeployment = 'Split6DuDeployment',
  DuCellConfig = 'DuCellConfig',
  CuCellConfig = 'CuCellConfig',
  Split6DuAppConfig = 'Split6DuAppConfig',
  CuCpAppConfig = 'CuCpAppConfig',
  CuUpAppConfig = 'CuUpAppConfig',
}

export enum Du_CR_KIND {
  Split6DuDeployment = 'Split6DuDeployment',
  Split6DuAppConfig = 'Split6DuAppConfig',
  DuCellConfig = 'DuCellConfig',
}

export enum CuUp_CR_KIND {
  CuUpDeployment = 'CuUpDeployment',
  CuUpAppConfig = 'CuUpAppConfig',
}

export enum CuCp_CR_KIND {
  CuCpDeployment = 'CuCpDeployment',
  CuCpAppConfig = 'CuCpAppConfig',
  CuCellConfig = 'CuCellConfig',
}

export enum NODE_KIND {
  DU = 'Du',
  CUCP = 'CuCp',
  CUUP = 'CuUp',
}

export const LifeCycle_OPS = ['FACTORY', 'STAGING', 'COMMISSIONING', 'OPERATIONAL', 'DECOMMISSIONED', 'LAB', 'FAULTY'];

export const ENV_COLORS: { [key: string]: string } = {
  // test_1: '#e5e338',
  'nms-dev-1': 'blue',
  'nms-dev-2': 'green',
  'nms-test-1': 'red',
  dev_1: 'blue',
  dev2_01: 'green',
  test_1: 'red',
};

// export enum LifeCycle {
//   'FACTORY' = 0,
//   'STAGING' = 1,
//   'COMMISSIONING' = 2,
//   'OPERATIONAL' = 3,
//   'DECOMMISSIONED' = 4,
// }

//Login
const currentDomain = new URL(window.location.href);
export const AUTH_TOKEN_KEY = 'dw_authToken';

export const DEV_IDS_AUTH_URL = 'http://www-dev.denseware.net/login';
export const PROD_IDS_AUTH_URL = 'https://www.denseware.net/login';
export const DENSEPASS_AUTH_URL = 'https://verix.ionxnetworks.com/login';
export const HARDCODED_HOST_URL = 'http://************';
export const CURRENT_HOST_URL = currentDomain.origin;
//NOTE: was trowing and error and it dosent seem tto be used anywhere
//export const DOCKER_HOST_URL = `http://${import.meta.env.NMS_HOST_URL}`;
// export const DOCKER_NMS_serv_Grafana_URL = `http://${
//   import.meta.env.NMS_serv_Grafana_URL
// }`;

export const LOCAL_NMS = 'http://localhost:8081';
export const LOCAL_IDS = 'http://localhost:3000/login';

export const ALLOWED_ROLES = [
  'SystemAdmin',
  'DevTeam',
  'ClientUser',
  'NqtUse',
  'DenseAirPT',
  'DenseAirNZ',
  'DenseAirIE',
  'DenseAirGB',
  'DenseAirBE',
  'DenseAirAU',
  'DenseAirUS',
  'NMSAdministrator',
  'NmsDev',
  'NmsOperator',
  'NmsReadOnly',
];
export const ROLES_SPECIFIC_COUNTRIES = ['DenseAirGB', 'DenseAirUS'];

export const READ_WRITE_ACCESS_ROLES = ['SystemAdmin', 'NmsAdministrator', 'NmsDev', 'NmsOperator'];

export const READ_ACCESS_ROLES = ['NmsReadOnly', 'NmsNoc'];

export const READ_ONLY_ACCESS_ROLES = ['NmsReadOnly'];

export enum MONITOR_STATE {
  'Active' = 'Y',
  'Passive' = 'P',
  'Ignore' = 'N',
}

export const PARAMETER_SETS = [
  { name: 'DU Cell Config - Cell Identity', set_id: 1 },
  { name: 'DU Cell Config - Frequency and SSB', set_id: 2 },
  { name: 'DU Cell Config - SIB1 / Power', set_id: 3 },
  { name: 'DU Cell Config - Timers', set_id: 4 },
  { name: 'DU Cell Config - MAC', set_id: 5 },
  { name: 'DU App Config - Identity and Connectivity', set_id: 6 },
  { name: 'CU_CP Cell Config - Identity', set_id: 7 },
  { name: 'CU_CP App Config - Identity and Connectivity', set_id: 8 },
  { name: 'CU_UP App Config - Identity and Connectivity', set_id: 9 },
];

export const greenBoxShadowColor = 'rgba(0, 128, 0, 0.6)';

export enum StatusToColor {
  OK = 'green',
  WARNING = 'orange',
  ERROR = 'red',
  UNKNOWN = 'gray',
  SHUTDOWN = 'black',
  CRITICAL = 'purple',
  //NOTE: Need a consistent naming convention
  TRUE = 'green',
  FALSE = 'black',
  True = 'green',
  False = 'black',
}

export enum ColorToStatus {
  green = 'OK',
  orange = 'WARNING',
  red = 'ERROR',
  gray = 'UNKNOWN',
  black = 'SHUTDOWN',
  purple = 'CRITICAL',
}

export const ROLE_TO_COUNTRY_MAPPING: { [key: string]: string } = {
  DenseAirGB: 'GBR',
  DenseAirUS: 'USA',
};

export enum FileUploadTypes {
  import = 'IMPORT',
  export = 'EXPORT',
  upload = 'UPLOAD',
  download = 'DOWNLOAD',
}

export const StatusConstants = {
  OK: 'OK',
  WARNING: 'WARNING',
  ERROR: 'ERROR',
  UNKNOWN: 'UNKNOWN',
  SHUTDOWN: 'SHUTDOWN',
  CRITICAL: 'CRITICAL',
};
