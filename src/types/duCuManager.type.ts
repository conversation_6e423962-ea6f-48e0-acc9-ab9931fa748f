//import { SelectedOption } from '../pages/OranDuCuManager/advancedView/CustomResources';

// export type Cluster = {
//   created: string;
//   updated: string;
//   updated_by: number; //'default 1'
//   id: number;
//   cluster_name: string;
//   ip_address: string;
//   gcp_project: string;
//   gcp_region: string;
//   software_version: string; //'default v1beta1'
//   cluster_ca_secret: string; //ca cert secret name. Only needed for GKE cluster
//   cluster_type: string; //'Default"GDCV"'
// };

export type ConfigSet = {
  created: string;
  updated: string;
  updated_by: number;
  id: number;
  name: string;
  cr_kind: string;
  file_part_id: number;
  parameter_set_id: number;
  data: string;
};

export type ParameterSets = {
  name: string;
  set_id: number;
  cr_file: string;
  plural: string;
  configset_names: {
    band: number;
    arfcn: number;
    bw: number;
    ssb_arfcn: number;
    ssb_duration: number;
    ssb_periodicity: number;
  };
};

export type UnknownObject = { [key: string]: any };

export type FormObject = {
  constraints: Record<string, unknown>;
  default_value: { rsrpLvl: number } | number;
  function_type: string;
  name: string;
  oran_fn_type: string;
  parameter_set_id: number;
  path: string;
  read_write: boolean;
  software_version: string;
  template_section: string;
  type: string;
};

export type NodeData = {
  label: string;
  ip: string;
  cluster?: string;
  site?: string;
  description?: string;
  edge?: string;
  onChange: (id: string, type?: string, value?: string) => void;
};

// Interfaces
// export type EdgeField = {
//   name: string;
//   label: string;
//   value: string;
// };

export type Field = {
  name: string;
  label: string;
  value: string;
};

export type InitialEdge = {
  id: string;
  source: string;
  sourceHandle: string;
  target: string;
  targetHandle: string;
  animated: boolean;
  type: string;
  name: string;
  data: EdgeData;
};

export type EdgeInterface = 'cu_cp' | 'du' | 'cu_up' | 'ru' | 'none' | undefined;
export type NodeInterface = 'ng_u' | 'ng_u_gw' | 'ng_c' | 'f1_c_gw' | 'none' | undefined;

export type EdgeData = {
  fields: Field[];
  onChange: (id: string, name: string, value: string) => void;
  interface: EdgeInterface[];
  edge: string;
  name?: string;
  position?: string;
};

export type ReactFlowNodeData = {
  name?: string;
  fields: Field[];
  node: EdgeInterface;
  selectedOption?: SelectedOption;
  onChange: (id: string, name: string, value: string) => void;
};

export type NodeType = 'DuNode' | 'CuCpNode' | 'CuUpNode';

export type InterfaceFormData = {
  site_name: string | null;
  version: string;
  du: DuNode;
  cu_cp: CuCpNode;
  cu_up: CuUpNode;
  cu_cp2?: CuCpNode;
  cu_up2?: CuUpNode;
  ru: RuNode;
};

export type InterfaceFieldData = {
  default: any;
  user_setting: any;
  name: string;
  target: string;
  [key: string]: any;
};

export type RuNode = {
  cluster_id: number | null;
  node_type: 'ru';
};

export type DuNode = {
  cluster_id: number | null;
  fh_c: AddressConfig;
  fh_u: Omit<AddressConfig, 'subnet_mask' | 'remote_address'>;
  f1_c: AddressConfig;
  f1_u: AddressConfig;
  f1_c2?: AddressConfig;
  f1_u2?: AddressConfig;
  node_type: 'du';
};

export type CuCpNode = {
  cluster_id: number | null;
  ng_c: AddressConfig;
  node_type: 'cu_cp';
};

export type CuUpNode = {
  cluster_id: number | null;
  f1_u: AddressConfig;
  ng_u: AddressConfig;
  e1: AddressConfig;
  node_type: 'cu_up';
};

export type AddressConfig = {
  local_ip?: string;
  gateway_address?: string;
  remote_address?: string;
  subnet_mask?: number;
};

export type Cluster = {
  cluster_ca_secret: string;
  cluster_name: string;
  cluster_type: string;
  created: string;
  cr_site?: string;
  cr_type?: string;
  gcp_project_id: string;
  gcp_region: string;
  id: number;
  ip_address: string;
  software_version: string;
  updated: string;
  updated_by: number;
};

export type CrCard = {
  created: string;
  enabled: string;
  kind: string;
  name: string;
  site: string;
};

export type SelectedOption = {
  cluster?: number | null;
  rowClicked?: boolean;
  deployment_name: string | null;
  du_site_name: string | null;
  cucp_site_name: string | null;
  cuup_site_name: string | null;
  du_cluster: number | null;
  cucp_cluster: number | null;
  cuup_cluster: number | null;
  du_cell_config_set_ids?: (number | null)[];
  du_app_config_set_ids?: (number | null)[];
  cu_cell_config_set_ids?: (number | null)[];
  cu_cp_app_config_set_ids?: (number | null)[];
  cu_up_app_config_set_ids?: (number | null)[];
  ru_vendor?: string | null;
  f1_ip?: string | null;
  e1_ip?: string | null;
  deployment_params?: DeploymentParams;
} | null;

export type DeploymentParams = {
  is_comp: boolean;
  comp_trp_count: number;
  rf_use_case?: number;
  max_cells?: number;
  ssb_arfcns?: number[];
  du_fh_vlans?: number[];
  cu2?: {
    cucp_site_name2?: string;
    cuup_site_name2?: string;
    f1_ip2?: string;
    e1_ip2?: string;
  };
};

export type CrCreationResponse = {
  cuup: any[][];
  cucp: any[][];
  du: any[][];
  detail?: any[][];
};

export const mapCrTypeToStateValue = (crType: string) => {
  const typeMapping: Record<string, string> = {
    cu_cp_deployment: 'CuCp',
    cu_cp_app_config: 'CuCp',
    cu_cell_config: 'CuCp',
    cu_up_app_config: 'CuUp',
    cu_up_deployment: 'CuUp',
    du_deployment: 'Du',
    du_app_config: 'Du',
    du_cell_config: 'Du',
  };
  return typeMapping[crType] || crType;
};

export type passedData = {
  selectedOption: SelectedOption;
  deployment_type: string;
};

export type Pod = {
  ip: string;
  name: string;
  namespace: string;
  site: string;
};

export type Resource = {
  name: string;
  created: string;
  kind: string;
  enabled: string;
  site: string;
};

export type InitialIdentityFormData = {
  site_name: string;
  version: string;
  shared: {
    gNodeB: {
      id_length: number;
      id: number;
    };
    PLMN: {
      MCC: string;
      MNC: string;
    };
    node_type: string;
  };
  du: {
    cluster_id: number | null;
    gNodeB: {
      du_id: number;
      du_name: string;
    };
    local_cell_id: string;
    physical_cell_id: number;
    tracking_area_code: string;
    ran_area_code: number;
    node_type: string;
  };
  cu_cp: {
    cluster_id: number | null;
    gNodeB: {
      cu_name: string;
      index: number;
    };
    node_type: string;
  };
  cu_up: {
    cluster_id: number | null;
    node_type: string;
  };
};

export type CBSDHeader = {
  serial: string;
  fccId: string;
  registered: string;
  grant: string;
  heartbeat: string;
  displayName: string;
  state?: string;
  cbrsId: string;
};

export type CBSDtype = {
  id: string;
  clusterId: string;
  serialNumber: string;
  fccId: string;
  state: string;
  requestGrant: boolean;
  installationParam: {
    latitude: number;
    longitude: number;
    height: number;
    horizontalAccuracy: number | null;
    verticalAccuracy: number | null;
    indoorDeployment: boolean;
    antennaAzimuth: number;
    antennaDowntilt: number;
    antennaGain: number;
    eirpCapability: number;
    antennaBeamwidth: number;
    antennaModel: string | null;
    heightType: string;
  };
  bandwidths: number[];
  groupingParam: any[];
  lowFrequency: number;
  highFrequency: number;
  grants: any[];
  activeGrants: number;
  allowedGrants: number;
  activeHeartbeats: number;
  displayName: string;
};

export type DeviceFormValues = {
  displayName: string;
  // name: string;
  serialNumber: string;
  macAddress: string;
  fccId: string;
  clusterId: string;
  // deviceState: string;
  userId: string;
  category: string;
};

export type ConfigFormValues = {
  vendor: string;
  model: string;
  softwareVersion: string;
  hardwareVersion: string;
  firmwareVersion: string;
  radioTechnology: string;
  supportedSpec: string;
};

export type InstallationFormValues = {
  latitude: number | null;
  longitude: number | null;
  height: number | null;
  heightType: string;
  horizontalAccuracy: number | null;
  verticalAccuracy: number | null;
  indoorDeployment: boolean;
  antennaAzimuth: number | null;
  antennaDowntilt: number | null;
  antennaGain: number | null;
  eirpCapability: number | null;
  antennaBeamwidth: number | null;
  antennaModel: string | null;
};

export type FormValues = {
  deviceFormData: DeviceFormValues;
  configFormData: ConfigFormValues;
  installationFormData: InstallationFormValues;
};

type CbsdInfo = {
  vendor: string;
  model: string;
  softwareVersion: string;
  hardwareVersion: string;
  firmwareVersion: string;
};

type AirInterface = {
  radioTechnology: string;
  supportedSpec: string;
};

export type CBSDPostType = {
  clusterId?: string;
  serialNumber?: string;
  fccId?: string;
  category?: string;
  installationParam?: InstallationFormValues;
  cbsdInfo?: CbsdInfo;
  airInterface?: AirInterface;
  displayName?: string;
  userId?: string;
};

export type CPIHeader = {
  email: string;
  installer_id: string;
  firstName: string;
  lastName: string;
  cpi_key: string;
};

export type CPIForm = {
  email: string;
  cpi_key: string;
  first_name?: string;
  last_name?: string;
  installer_id: string;
  id: number;
};

export type CPIViewProps = {
  email?: string;
  installer_id?: string;
};

export type OranDataType = {
  id: string;
  pod: {
    status: string;
    detail: {
      conditions: Array<{
        last_probe_time: string | null;
        last_transition_time: string;
        message: string | null;
        reason: string | null;
        status: string;
        type: string;
      }>;
      container_statuses: Array<{
        allocated_resources: any | null;
        container_id: string;
        image: string;
        image_id: string;
        last_state: {
          running: any | null;
          terminated: any | null;
          waiting: any | null;
        };
        name: string;
        ready: boolean;
        resources: any | null;
        restart_count: number;
        started: boolean;
        state: {
          running: {
            started_at: string;
          } | null;
          terminated: any | null;
          waiting: any | null;
        };
        volume_mounts: any | null;
      }>;
      ephemeral_container_statuses: any | null;
      host_ip: string;
      host_ips: Array<{ ip: string }>;
      init_container_statuses: any | null;
      message: string | null;
      nominated_node_name: string | null;
      phase: string;
      pod_ip: string;
      pod_ips: Array<{ ip: string }>;
      qos_class: string;
      reason: string | null;
      resize: any | null;
      resource_claim_statuses: any | null;
      start_time: string;
    };
    crs: {
      DuCellConfig: {
        status: {
          specHash: string;
        };
        content: {
          apiVersion: string;
          kind: string;
          metadata: {
            annotations: {
              rel: string;
            };
            creationTimestamp: string;
            generation: number;
            labels: {
              site: string;
            };
            name: string;
            namespace: string;
            resourceVersion: string;
            uid: string;
          };
          spec: {
            duCellConfig: {
              ME: {
                GNBDUFunction: Array<{
                  NRCellDU: Array<{
                    administrativeState: string;
                    arfcnDL: number;
                    arfcnSUL: number;
                    arfcnUL: number;
                    bSChannelBwSUL: number;
                    bSChannelBwUL: number;
                    gnbCellDuVsCfg: {
                      XranConfig: {
                        DynamicMultiSectionEna: number;
                        DynamicSectionEna: number;
                        DynamicSectionEnaUL: number;
                        EnableCp: number;
                        EnableCsirsTrans: number;
                        ExtBfwDl0: string;
                        ExtBfwDl1: string;
                        ExtBfwDl2: string;
                        ExtBfwDl3: string;
                        ExtBfwDl4: string;
                        ExtBfwDl5: string;
                        ExtBfwDl6: string;
                        ExtBfwDl7: string;
                        ExtBfwUl0: string;
                        ExtBfwUl1: string;
                        ExtBfwUl2: string;
                        ExtBfwUl3: string;
                        ExtBfwUl4: string;
                        ExtBfwUl5: string;
                        ExtBfwUl6: string;
                        ExtBfwUl7: string;
                        Gps_Alpha: number;
                        Gps_Beta: number;
                        MaxSectionsPerSlot: number;
                        MaxSectionsPerSymbol: number;
                        PrbElemDl0: string;
                        PrbElemDl1: string;
                        PrbElemDl2: string;
                        PrbElemDl3: string;
                        PrbElemDl4: string;
                        PrbElemDl5: string;
                        PrbElemDl6: string;
                        PrbElemDl7: string;
                        PrbElemSrs0: string;
                        PrbElemSrs1: string;
                        PrbElemSrs2: string;
                        PrbElemSrs3: string;
                        PrbElemSrs4: string;
                        PrbElemSrs5: string;
                        PrbElemSrs6: string;
                        PrbElemSrs7: string;
                        PrbElemUl0: string;
                        PrbElemUl1: string;
                        PrbElemUl2: string;
                        PrbElemUl3: string;
                        PrbElemUl4: string;
                        PrbElemUl5: string;
                        PrbElemUl6: string;
                        PrbElemUl7: string;
                        RadioTypeIndex: number;
                        T1a_max_cp_dl: number;
                        T1a_max_cp_ul: number;
                        T1a_max_up: number;
                        T1a_min_cp_dl: number;
                        T1a_min_cp_ul: number;
                        T1a_min_up: number;
                        T2a_max_cp_dl: number;
                        T2a_max_cp_ul: number;
                        T2a_max_up: number;
                        T2a_min_cp_dl: number;
                        T2a_min_cp_ul: number;
                        T2a_min_up: number;
                        Ta3_max: number;
                        Ta3_min: number;
                        Ta4_max: number;
                        Ta4_min: number;
                        Tadv_cp_dl: number;
                        band_sector_bitmask: number;
                        ccid_bitmask: number;
                        dIAxCOffSet: number;
                        nCCID: number;
                        nPrbElemDl: number;
                        nPrbElemSrs: number;
                        nPrbElemUl: number;
                        nPrimeRUPortIndex: number;
                        nRFDesPrimMacAddress: string;
                        nRFDesSecMacAddress: string;
                        nRUPortNum: number;
                        nSecondRUPortIndex: number;
                        numCCPerRu: number;
                        'o-du-port-bitmask': number;
                        oRuType: number;
                        oru0naecOffsetCsirs: number;
                        oru_id: number;
                        prachAxCOffSet: number;
                        'ru_port-bitmask': number;
                        uIAxCOffSet: number;
                        xRANNumDLPRBs: number;
                        xRANNumULPRBs: number;
                        xRANSFNWrap: number;
                        xranCompHdrType: number;
                        xranCompMethod: number;
                        xranModCompEna: number;
                        xranPrachCompMethod: number;
                        xranPrachiqWidth: number;
                        xraniqWidth: number;
                      };
                      cellAlarm: Array<{
                        alarmId: string;
                        alarmName: string;
                        thresholdCount: number;
                        thresholdLevel: number;
                        thresholdTimeInterval: number;
                      }>;
                      cellCenterThreshold: number;
                      cellPathLossOffset: number;
                    };
                    id: string;
                  }>;
                  id: string;
                }>;
              };
            };
          };
        };
      };
    };
  };
};
