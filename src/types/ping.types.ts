export enum PingStatus {
  SUCCESS = 'success',
  FAILURE = 'failure',
  PARTIAL_FAILURE = 'partial_failure',
  IN_PROGRESS = 'in_progress',
  TIMEOUT = 'timeout',
}

export interface PingRequest {
  nodeId: string;
  targetIp: string;
  count?: number;
  timeout?: number;
  packetSize?: number;
  interval?: number;
}

export interface PingResult {
  sequenceNumber: number;
  latency: number;
  status: 'success' | 'timeout' | 'error';
  timestamp?: Date;
}

export interface PingResponse {
  nodeId: string;
  targetIp: string;
  status: PingStatus;
  averageLatency: number;
  minLatency: number;
  maxLatency: number;
  packetLoss: number; // percentage
  packetsTransmitted: number;
  packetsReceived: number;
  results: PingResult[];
  timestamp: Date;
  error?: string;
  sessionId?: string;
}

export interface PingHistoryEntry {
  nodeId: string;
  targetIp: string;
  status: PingStatus;
  averageLatency: number;
  packetLoss: number;
  timestamp: Date;
  sessionId?: string;
}

export interface ContinuousPingConfig {
  nodeId: string;
  targetIp: string;
  interval: number; // milliseconds
  count?: number;
  timeout?: number;
  packetSize?: number;
}

export interface ContinuousPingSession {
  sessionId: string;
  nodeId: string;
  targetIp: string;
  status: 'started' | 'stopped' | 'paused';
  config: ContinuousPingConfig;
  createdAt: Date;
  lastPing?: Date;
}

export interface BulkPingStatusRequest {
  nodeIds: string[];
}

export interface BulkPingStatusResponse {
  nodeId: string;
  status: PingStatus;
  lastPing?: Date;
  averageLatency?: number;
  packetLoss?: number;
  isOnline: boolean;
}

export interface NetworkConnectivityStatus {
  nodeId: string;
  nodeName: string;
  nodeType: string;
  primaryIp: string;
  secondaryIp?: string;
  status: PingStatus;
  lastSuccessfulPing?: Date;
  consecutiveFailures: number;
  uptime: number; // percentage
  averageLatency: number;
  region?: string;
  site?: string;
}

export interface PingMetrics {
  totalNodes: number;
  onlineNodes: number;
  offlineNodes: number;
  partiallyFailingNodes: number;
  averageLatency: number;
  worstLatency: number;
  bestLatency: number;
  uptimePercentage: number;
  lastUpdated: Date;
}

export interface PingAlertConfig {
  nodeId: string;
  enabled: boolean;
  latencyThreshold: number; // ms
  packetLossThreshold: number; // percentage
  consecutiveFailureThreshold: number;
  alertRecipients: string[];
}

export interface PingAlert {
  id: string;
  nodeId: string;
  alertType: 'latency' | 'packet_loss' | 'connectivity';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  acknowledged: boolean;
  acknowledgedBy?: string;
  resolvedAt?: Date;
}
