import { useEffect, useState } from 'react';
declare global {
  interface Window {
    _env_: { [key: string]: string };
  }
}

interface Environment {
  [key: string]: string;
}

function useEnv<T extends Environment>(propName: keyof T): string | undefined {
  const [envValue, setEnvValue] = useState<string | undefined>(undefined);
  useEffect(() => {
    if (typeof window !== 'undefined' && window._env_ && window._env_[propName as keyof (typeof window)['_env_']]) {
      setEnvValue(window._env_[propName as keyof (typeof window)['_env_']] ?? '');
    }
  }, [propName]);

  return envValue;
}

export default useEnv;
