#!/bin/sh

# Name of the file where the commit message is stored
COMMIT_MSG_FILE=$1

# The type of commit message being prepared
COMMIT_SOURCE=$2

# Skip if this is an amend, merge, or template commit
if [ "$COMMIT_SOURCE" = "merge" ] || [ "$COMMIT_SOURCE" = "template" ]; then
  exit 0
fi

# Extract the current branch name
BRANCH_NAME=$(git branch --show-current)

# Enhanced regex patterns for different ticket formats
JIRA_TICKET_REGEX="([A-Za-z]+-[0-9]+)"
GITHUB_ISSUE_REGEX="(#[0-9]+)"
CUSTOM_TICKET_REGEX="([A-Z]{2,10}-[0-9]+)"

# Read the original commit message
ORIGINAL_COMMIT_MSG=$(cat $COMMIT_MSG_FILE)

# Skip if commit message already has a ticket prefix
if [[ $ORIGINAL_COMMIT_MSG =~ ^[A-Za-z]+-[0-9]+: ]] || [[ $ORIGINAL_COMMIT_MSG =~ ^#[0-9]+: ]]; then
  exit 0
fi

TICKET_ID=""

# Try to match different ticket patterns
if [[ $BRANCH_NAME =~ $JIRA_TICKET_REGEX ]]; then
  TICKET_ID=${BASH_REMATCH[1]}
elif [[ $BRANCH_NAME =~ $GITHUB_ISSUE_REGEX ]]; then
  TICKET_ID=${BASH_REMATCH[1]}
elif [[ $BRANCH_NAME =~ $CUSTOM_TICKET_REGEX ]]; then
  TICKET_ID=${BASH_REMATCH[1]}
fi

# If we found a ticket ID, prepend it to the commit message
if [ -n "$TICKET_ID" ]; then
  # Convert to uppercase for consistency
  TICKET_ID=$(echo "$TICKET_ID" | tr '[:lower:]' '[:upper:]')
  
  # Prepend the ticket ID to the commit message
  echo "$TICKET_ID: $ORIGINAL_COMMIT_MSG" > $COMMIT_MSG_FILE
fi
